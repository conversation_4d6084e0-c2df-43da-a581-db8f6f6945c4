module.exports = {

"[project]/lib/data/reports-mock-data.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "getReportById": ()=>getReportById,
    "getReportsByStatus": ()=>getReportsByStatus,
    "getReportsByType": ()=>getReportsByType,
    "getScheduleById": ()=>getScheduleById,
    "getTeacherById": ()=>getTeacherById,
    "getTemplateById": ()=>getTemplateById,
    "mockGeneratedReports": ()=>mockGeneratedReports,
    "mockReportAnalytics": ()=>mockReportAnalytics,
    "mockReportTemplates": ()=>mockReportTemplates,
    "mockScheduledReports": ()=>mockScheduledReports,
    "mockTeachers": ()=>mockTeachers,
    "schoolInfo": ()=>schoolInfo
});
const schoolInfo = {
    schoolId: "301216",
    schoolName: "Tanauan School of Arts and Trade",
    address: "Tanauan City, Batangas, Philippines",
    district: "Tanauan City District",
    division: "Schools Division of Batangas",
    region: "Region IV-A (CALABARZON)",
    principalName: "Dr. Maria Elena Rodriguez",
    schoolYear: "2024-2025",
    semester: "1st Semester",
    quarter: "2nd Quarter"
};
const mockTeachers = [
    {
        id: "TCH001",
        name: "Prof. Juan Carlos Santos",
        position: "Subject Teacher - Mathematics"
    },
    {
        id: "TCH002",
        name: "Mrs. Ana Marie Reyes",
        position: "Subject Teacher - English"
    },
    {
        id: "TCH003",
        name: "Mr. Jose Miguel Torres",
        position: "Subject Teacher - Science"
    },
    {
        id: "TCH004",
        name: "Ms. Princess Mae Garcia",
        position: "Subject Teacher - Filipino"
    }
];
const mockGeneratedReports = [
    {
        id: "RPT001",
        config: {
            id: "CFG001",
            name: "Daily Attendance Report - Grade 7A",
            type: "SF2",
            description: "SF2 Daily Attendance Report for Grade 7-A",
            dateRange: {
                startDate: "2025-01-02",
                endDate: "2025-01-02"
            },
            filters: {
                grades: [
                    "7"
                ],
                sections: [
                    "Grade 7-A"
                ]
            },
            settings: {
                includePhotos: false,
                includeSignatures: true,
                pageOrientation: "portrait",
                fontSize: "medium"
            },
            createdBy: "admin",
            createdAt: "2025-01-02T08:00:00Z",
            lastModified: "2025-01-02T08:00:00Z"
        },
        status: "READY",
        generatedAt: "2025-01-02T08:15:00Z",
        fileSize: 245760,
        downloadCount: 12,
        metadata: {
            totalStudents: 35,
            totalRecords: 35,
            dateRange: {
                startDate: "2025-01-02",
                endDate: "2025-01-02"
            },
            statistics: {
                presentCount: 32,
                lateCount: 2,
                absentCount: 1,
                attendanceRate: 94.3
            },
            schoolInfo
        }
    },
    {
        id: "RPT002",
        config: {
            id: "CFG002",
            name: "Monthly Learner's Movement - December 2024",
            type: "SF4",
            description: "SF4 Monthly Learner's Movement Report for December 2024",
            dateRange: {
                startDate: "2024-12-01",
                endDate: "2024-12-31"
            },
            filters: {
                grades: [
                    "7",
                    "8",
                    "9",
                    "10",
                    "11",
                    "12"
                ]
            },
            settings: {
                includeSignatures: true,
                pageOrientation: "landscape",
                fontSize: "small"
            },
            createdBy: "principal",
            createdAt: "2025-01-01T09:00:00Z",
            lastModified: "2025-01-01T09:00:00Z"
        },
        status: "READY",
        generatedAt: "2025-01-01T09:30:00Z",
        fileSize: 512000,
        downloadCount: 8,
        metadata: {
            totalStudents: 1234,
            totalRecords: 24680,
            dateRange: {
                startDate: "2024-12-01",
                endDate: "2024-12-31"
            },
            statistics: {
                presentCount: 22145,
                lateCount: 1235,
                absentCount: 1300,
                attendanceRate: 89.7
            },
            schoolInfo
        }
    },
    {
        id: "RPT003",
        config: {
            id: "CFG003",
            name: "Weekly Attendance Summary",
            type: "WEEKLY",
            description: "Weekly attendance summary for all grades",
            dateRange: {
                startDate: "2024-12-30",
                endDate: "2025-01-03"
            },
            filters: {},
            settings: {
                showStatistics: true,
                groupBy: "grade",
                pageOrientation: "landscape"
            },
            createdBy: "admin",
            createdAt: "2025-01-03T16:00:00Z",
            lastModified: "2025-01-03T16:00:00Z"
        },
        status: "GENERATING",
        generatedAt: "2025-01-03T16:15:00Z",
        downloadCount: 0,
        metadata: {
            totalStudents: 1234,
            totalRecords: 6170,
            dateRange: {
                startDate: "2024-12-30",
                endDate: "2025-01-03"
            },
            statistics: {
                presentCount: 5540,
                lateCount: 310,
                absentCount: 320,
                attendanceRate: 89.8
            },
            schoolInfo
        }
    }
];
const mockReportTemplates = [
    {
        id: "TPL001",
        name: "SF2 Daily Attendance",
        type: "SF2",
        description: "Standard SF2 Daily Attendance Report template",
        config: {
            type: "SF2",
            settings: {
                includeSignatures: true,
                pageOrientation: "portrait",
                fontSize: "medium",
                includeHeader: true,
                includeFooter: true
            }
        },
        isDefault: true,
        isPublic: true,
        createdBy: "system",
        createdAt: "2024-01-01T00:00:00Z"
    },
    {
        id: "TPL002",
        name: "SF4 Monthly Movement",
        type: "SF4",
        description: "Standard SF4 Monthly Learner's Movement Report template",
        config: {
            type: "SF4",
            settings: {
                includeSignatures: true,
                pageOrientation: "landscape",
                fontSize: "small",
                includeHeader: true,
                includeFooter: true
            }
        },
        isDefault: true,
        isPublic: true,
        createdBy: "system",
        createdAt: "2024-01-01T00:00:00Z"
    },
    {
        id: "TPL003",
        name: "Chronic Absenteeism Report",
        type: "CUSTOM",
        description: "Custom report for identifying students with chronic absenteeism",
        config: {
            type: "CUSTOM",
            filters: {
                attendanceStatus: [
                    "Absent"
                ]
            },
            settings: {
                showStatistics: true,
                groupBy: "grade",
                sortBy: "attendance"
            }
        },
        isDefault: false,
        isPublic: true,
        createdBy: "admin",
        createdAt: "2024-06-15T10:00:00Z"
    }
];
const mockScheduledReports = [
    {
        id: "SCH001",
        reportConfigId: "CFG001",
        name: "Daily SF2 Reports - All Grades",
        frequency: "daily",
        time: "07:00",
        timezone: "Asia/Manila",
        isActive: true,
        nextRun: "2025-01-03T07:00:00Z",
        recipients: [
            "<EMAIL>",
            "<EMAIL>"
        ],
        createdBy: "admin",
        createdAt: "2024-08-01T00:00:00Z"
    },
    {
        id: "SCH002",
        reportConfigId: "CFG002",
        name: "Monthly SF4 Reports",
        frequency: "monthly",
        dayOfMonth: 1,
        time: "09:00",
        timezone: "Asia/Manila",
        isActive: true,
        lastRun: "2025-01-01T09:00:00Z",
        nextRun: "2025-02-01T09:00:00Z",
        recipients: [
            "<EMAIL>",
            "<EMAIL>"
        ],
        createdBy: "principal",
        createdAt: "2024-08-01T00:00:00Z"
    }
];
const mockReportAnalytics = [
    {
        reportId: "RPT001",
        views: 45,
        downloads: 12,
        exports: {
            PDF: 8,
            EXCEL: 3,
            CSV: 1,
            PRINT: 0
        },
        lastAccessed: "2025-01-02T14:30:00Z",
        popularFilters: {
            "grades": 15,
            "sections": 12,
            "dateRange": 45
        },
        averageGenerationTime: 15.5
    },
    {
        reportId: "RPT002",
        views: 23,
        downloads: 8,
        exports: {
            PDF: 6,
            EXCEL: 2,
            CSV: 0,
            PRINT: 0
        },
        lastAccessed: "2025-01-01T16:45:00Z",
        popularFilters: {
            "grades": 8,
            "dateRange": 23
        },
        averageGenerationTime: 45.2
    }
];
function getReportById(id) {
    return mockGeneratedReports.find((report)=>report.id === id);
}
function getTemplateById(id) {
    return mockReportTemplates.find((template)=>template.id === id);
}
function getScheduleById(id) {
    return mockScheduledReports.find((schedule)=>schedule.id === id);
}
function getReportsByType(type) {
    return mockGeneratedReports.filter((report)=>report.config.type === type);
}
function getReportsByStatus(status) {
    return mockGeneratedReports.filter((report)=>report.status === status);
}
function getTeacherById(id) {
    return mockTeachers.find((teacher)=>teacher.id === id);
}
}),
"[project]/lib/utils/report-utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "applyReportFilters": ()=>applyReportFilters,
    "calculateAttendanceStatistics": ()=>calculateAttendanceStatistics,
    "calculateStudentAttendanceRate": ()=>calculateStudentAttendanceRate,
    "formatDateForDepEd": ()=>formatDateForDepEd,
    "generateReportMetadata": ()=>generateReportMetadata,
    "generateSF2Report": ()=>generateSF2Report,
    "generateSF4Report": ()=>generateSF4Report,
    "getQuarter": ()=>getQuarter,
    "getSchoolYear": ()=>getSchoolYear,
    "getSemester": ()=>getSemester
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/data/reports-mock-data.ts [app-ssr] (ecmascript)");
;
function formatDateForDepEd(date) {
    const d = new Date(date);
    return d.toLocaleDateString('en-PH', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}
function getSchoolYear(date = new Date()) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    // School year starts in June (month 6)
    if (month >= 6) {
        return `${year}-${year + 1}`;
    } else {
        return `${year - 1}-${year}`;
    }
}
function getQuarter(date = new Date()) {
    const month = date.getMonth() + 1;
    if (month >= 6 && month <= 8) return "1st Quarter";
    if (month >= 9 && month <= 11) return "2nd Quarter";
    if (month >= 12 || month <= 2) return "3rd Quarter";
    return "4th Quarter";
}
function getSemester(date = new Date()) {
    const month = date.getMonth() + 1;
    if (month >= 6 && month <= 10) return "1st Semester";
    return "2nd Semester";
}
function applyReportFilters(students, attendanceRecords, filters) {
    let filteredStudents = [
        ...students
    ];
    let filteredRecords = [
        ...attendanceRecords
    ];
    // Filter by grades
    if (filters.grades && filters.grades.length > 0) {
        filteredStudents = filteredStudents.filter((student)=>filters.grades.includes(student.grade));
    }
    // Filter by sections
    if (filters.sections && filters.sections.length > 0) {
        filteredStudents = filteredStudents.filter((student)=>student.section && filters.sections.includes(student.section));
    }
    // Filter by courses
    if (filters.courses && filters.courses.length > 0) {
        filteredStudents = filteredStudents.filter((student)=>filters.courses.includes(student.course));
    }
    // Filter by specific students
    if (filters.students && filters.students.length > 0) {
        filteredStudents = filteredStudents.filter((student)=>filters.students.includes(student.id));
    }
    // Filter by attendance status
    if (filters.attendanceStatus && filters.attendanceStatus.length > 0) {
        filteredRecords = filteredRecords.filter((record)=>filters.attendanceStatus.includes(record.status));
    }
    // Filter by student status
    if (!filters.includeTransferred) {
        filteredStudents = filteredStudents.filter((student)=>student.status !== 'Transferred');
    }
    if (!filters.includeInactive) {
        filteredStudents = filteredStudents.filter((student)=>student.status === 'Active');
    }
    // Filter records to match filtered students
    const studentIds = new Set(filteredStudents.map((s)=>s.id));
    filteredRecords = filteredRecords.filter((record)=>studentIds.has(record.studentId));
    return {
        students: filteredStudents,
        records: filteredRecords
    };
}
function calculateAttendanceStatistics(records) {
    const total = records.length;
    const present = records.filter((r)=>r.status === 'Present').length;
    const late = records.filter((r)=>r.status === 'Late').length;
    const absent = records.filter((r)=>r.status === 'Absent').length;
    return {
        totalRecords: total,
        presentCount: present,
        lateCount: late,
        absentCount: absent,
        attendanceRate: total > 0 ? (present + late) / total * 100 : 0
    };
}
function calculateStudentAttendanceRate(studentId, records) {
    const studentRecords = records.filter((r)=>r.studentId === studentId);
    const total = studentRecords.length;
    const present = studentRecords.filter((r)=>r.status === 'Present' || r.status === 'Late').length;
    return total > 0 ? present / total * 100 : 0;
}
function generateSF2Report(config, students, attendanceRecords) {
    const { students: filteredStudents, records: filteredRecords } = applyReportFilters(students, attendanceRecords, config.filters);
    // Get the first teacher (in real implementation, this would be based on subject/section)
    const teacher = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockTeachers"][0];
    // Create student records for SF2
    const sf2Students = filteredStudents.map((student)=>{
        const studentRecords = filteredRecords.filter((r)=>r.studentId === student.id);
        const dailyRecord = studentRecords.find((r)=>r.date === config.dateRange.startDate);
        return {
            studentId: student.id,
            studentName: `${student.firstName} ${student.middleName || ''} ${student.lastName}`.trim(),
            attendance: {
                'Morning': dailyRecord?.status === 'Present' ? 'P' : dailyRecord?.status === 'Late' ? 'L' : dailyRecord?.status === 'Absent' ? 'A' : 'A'
            },
            dailyStatus: dailyRecord?.status === 'Present' ? 'P' : dailyRecord?.status === 'Late' ? 'L' : dailyRecord?.status === 'Absent' ? 'A' : 'A',
            remarks: dailyRecord?.remarks
        };
    });
    // Calculate summary
    const summary = {
        totalStudents: sf2Students.length,
        presentCount: sf2Students.filter((s)=>s.dailyStatus === 'P').length,
        lateCount: sf2Students.filter((s)=>s.dailyStatus === 'L').length,
        absentCount: sf2Students.filter((s)=>s.dailyStatus === 'A').length,
        excusedCount: sf2Students.filter((s)=>s.dailyStatus === 'E').length,
        attendanceRate: 0
    };
    summary.attendanceRate = summary.totalStudents > 0 ? (summary.presentCount + summary.lateCount) / summary.totalStudents * 100 : 0;
    return {
        id: `SF2_${Date.now()}`,
        date: config.dateRange.startDate,
        grade: config.filters.grades?.[0] || 'All',
        section: config.filters.sections?.[0] || 'All',
        teacher,
        students: sf2Students,
        summary,
        schoolInfo: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["schoolInfo"],
        generatedAt: new Date().toISOString()
    };
}
function generateSF4Report(config, students, attendanceRecords) {
    const { students: filteredStudents, records: filteredRecords } = applyReportFilters(students, attendanceRecords, config.filters);
    const startDate = new Date(config.dateRange.startDate);
    const endDate = new Date(config.dateRange.endDate);
    // Mock enrollment data (in real implementation, this would come from enrollment records)
    const enrollment = {
        beginningOfMonth: filteredStudents.length,
        newAdmissions: [],
        transfers: {
            transferredIn: [],
            transferredOut: filteredStudents.filter((s)=>s.status === 'Transferred').map((s)=>({
                    studentId: s.id,
                    name: `${s.firstName} ${s.lastName}`,
                    dateOfAction: config.dateRange.endDate,
                    reason: 'Transfer to another school'
                }))
        },
        dropouts: [],
        endOfMonth: filteredStudents.filter((s)=>s.status === 'Active').length
    };
    // Calculate movement statistics
    const movement = {
        totalEnrolled: enrollment.beginningOfMonth,
        maleCount: filteredStudents.filter((s)=>s.gender === 'Male').length,
        femaleCount: filteredStudents.filter((s)=>s.gender === 'Female').length,
        newAdmissions: enrollment.newAdmissions.length,
        transfersIn: enrollment.transfers.transferredIn.length,
        transfersOut: enrollment.transfers.transferredOut.length,
        dropouts: enrollment.dropouts.length
    };
    // Calculate attendance statistics
    const stats = calculateAttendanceStatistics(filteredRecords);
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const attendance = {
        totalSchoolDays: totalDays,
        averageAttendance: stats.presentCount + stats.lateCount,
        attendanceRate: stats.attendanceRate,
        chronicAbsentees: filteredStudents.filter((s)=>calculateStudentAttendanceRate(s.id, filteredRecords) < 80).map((s)=>s.id),
        perfectAttendance: filteredStudents.filter((s)=>calculateStudentAttendanceRate(s.id, filteredRecords) === 100).map((s)=>s.id)
    };
    return {
        id: `SF4_${Date.now()}`,
        month: startDate.toLocaleDateString('en-US', {
            month: 'long'
        }),
        year: startDate.getFullYear().toString(),
        grade: config.filters.grades?.[0] || 'All',
        section: config.filters.sections?.[0],
        enrollment,
        movement,
        attendance,
        schoolInfo: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["schoolInfo"],
        principalReview: {
            reviewedBy: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["schoolInfo"].principalName,
            reviewDate: new Date().toISOString().split('T')[0],
            approved: false
        },
        generatedAt: new Date().toISOString()
    };
}
function generateReportMetadata(config, students, attendanceRecords) {
    const { students: filteredStudents, records: filteredRecords } = applyReportFilters(students, attendanceRecords, config.filters);
    const stats = calculateAttendanceStatistics(filteredRecords);
    return {
        totalStudents: filteredStudents.length,
        totalRecords: filteredRecords.length,
        dateRange: config.dateRange,
        statistics: {
            presentCount: stats.presentCount,
            lateCount: stats.lateCount,
            absentCount: stats.absentCount,
            attendanceRate: stats.attendanceRate
        },
        schoolInfo: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["schoolInfo"]
    };
}
}),
"[project]/lib/data/mock-data.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "findPeriodById": ()=>findPeriodById,
    "findStudentById": ()=>findStudentById,
    "findStudentByQRCode": ()=>findStudentByQRCode,
    "findSubjectById": ()=>findSubjectById,
    "getStudentAttendanceRecords": ()=>getStudentAttendanceRecords,
    "getTodayAttendanceRecord": ()=>getTodayAttendanceRecord,
    "mockAttendanceRecords": ()=>mockAttendanceRecords,
    "mockDashboardStats": ()=>mockDashboardStats,
    "mockRecentActivity": ()=>mockRecentActivity,
    "mockStudents": ()=>mockStudents,
    "mockSubjects": ()=>mockSubjects,
    "mockTimePeriods": ()=>mockTimePeriods
});
const mockStudents = [
    // Grade 7 Students
    {
        id: "STU001",
        name: "Maria Cristina Santos",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "1st Year",
        section: "Grade 7-A",
        grade: "7",
        status: "Active",
        photo: "https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU001_2025"
    },
    {
        id: "STU002",
        name: "Juan Carlos Dela Cruz",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "1st Year",
        section: "Grade 7-B",
        grade: "7",
        status: "Active",
        photo: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU002_2025"
    },
    {
        id: "STU003",
        name: "Ana Marie Reyes",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "1st Year",
        section: "Grade 7-A",
        grade: "7",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU003_2025"
    },
    // Grade 8 Students
    {
        id: "STU004",
        name: "Jose Miguel Rodriguez",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "2nd Year",
        section: "Grade 8-A",
        grade: "8",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU004_2025"
    },
    {
        id: "STU005",
        name: "Princess Mae Garcia",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "2nd Year",
        section: "Grade 8-B",
        grade: "8",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU005_2025"
    },
    // Grade 9 Students
    {
        id: "STU006",
        name: "Mark Anthony Villanueva",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "3rd Year",
        section: "Grade 9-A",
        grade: "9",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU006_2025"
    },
    {
        id: "STU007",
        name: "Angelica Mae Torres",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "3rd Year",
        section: "Grade 9-B",
        grade: "9",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU007_2025"
    },
    // Grade 10 Students
    {
        id: "STU008",
        name: "Christian Paul Mendoza",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "4th Year",
        section: "Grade 10-A",
        grade: "10",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU008_2025"
    },
    {
        id: "STU009",
        name: "Kimberly Rose Flores",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "4th Year",
        section: "Grade 10-B",
        grade: "10",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU009_2025"
    },
    // Grade 11 Students (Senior High School)
    {
        id: "STU010",
        name: "John Michael Cruz",
        email: "<EMAIL>",
        course: "Information and Communications Technology",
        year: "1st Year Senior High",
        section: "ICT 11-A",
        grade: "11",
        status: "Active",
        photo: "https://images.unsplash.com/photo-*************-f4e0f30006d5?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU010_2025"
    },
    {
        id: "STU011",
        name: "Mary Grace Aquino",
        email: "<EMAIL>",
        course: "Accountancy, Business and Management",
        year: "1st Year Senior High",
        section: "ABM 11-A",
        grade: "11",
        status: "Active",
        photo: "https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU011_2025"
    },
    // Grade 12 Students (Senior High School)
    {
        id: "STU012",
        name: "Ryan James Bautista",
        email: "<EMAIL>",
        course: "Information and Communications Technology",
        year: "2nd Year Senior High",
        section: "ICT 12-A",
        grade: "12",
        status: "Active",
        photo: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU012_2025"
    },
    {
        id: "STU013",
        name: "Sarah Jane Morales",
        email: "<EMAIL>",
        course: "Humanities and Social Sciences",
        year: "2nd Year Senior High",
        section: "HUMSS 12-A",
        grade: "12",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU013_2025"
    }
];
const mockSubjects = [
    {
        id: "SUBJ001",
        name: "Programming Fundamentals",
        code: "IT101",
        instructor: "Prof. Martinez",
        schedule: [
            {
                day: "Monday",
                startTime: "08:00",
                endTime: "10:00"
            },
            {
                day: "Wednesday",
                startTime: "08:00",
                endTime: "10:00"
            },
            {
                day: "Friday",
                startTime: "08:00",
                endTime: "10:00"
            }
        ]
    },
    {
        id: "SUBJ002",
        name: "Database Management",
        code: "IT201",
        instructor: "Prof. Rodriguez",
        schedule: [
            {
                day: "Tuesday",
                startTime: "10:00",
                endTime: "12:00"
            },
            {
                day: "Thursday",
                startTime: "10:00",
                endTime: "12:00"
            }
        ]
    },
    {
        id: "SUBJ003",
        name: "Web Development",
        code: "IT301",
        instructor: "Prof. Santos",
        schedule: [
            {
                day: "Monday",
                startTime: "13:00",
                endTime: "15:00"
            },
            {
                day: "Wednesday",
                startTime: "13:00",
                endTime: "15:00"
            }
        ]
    },
    {
        id: "SUBJ004",
        name: "Data Structures",
        code: "CS201",
        instructor: "Prof. Reyes",
        schedule: [
            {
                day: "Tuesday",
                startTime: "08:00",
                endTime: "10:00"
            },
            {
                day: "Thursday",
                startTime: "08:00",
                endTime: "10:00"
            }
        ]
    },
    {
        id: "SUBJ005",
        name: "Software Engineering",
        code: "CS301",
        instructor: "Prof. Cruz",
        schedule: [
            {
                day: "Monday",
                startTime: "15:00",
                endTime: "17:00"
            },
            {
                day: "Friday",
                startTime: "15:00",
                endTime: "17:00"
            }
        ]
    }
];
const mockTimePeriods = [
    {
        id: "PERIOD001",
        name: "1st Period",
        startTime: "08:00",
        endTime: "10:00",
        type: "morning"
    },
    {
        id: "PERIOD002",
        name: "2nd Period",
        startTime: "10:00",
        endTime: "12:00",
        type: "morning"
    },
    {
        id: "PERIOD003",
        name: "3rd Period",
        startTime: "13:00",
        endTime: "15:00",
        type: "afternoon"
    },
    {
        id: "PERIOD004",
        name: "4th Period",
        startTime: "15:00",
        endTime: "17:00",
        type: "afternoon"
    },
    {
        id: "PERIOD005",
        name: "Evening Class",
        startTime: "18:00",
        endTime: "20:00",
        type: "evening"
    }
];
const mockAttendanceRecords = [
    // Today's attendance records
    {
        id: "ATT001",
        studentId: "STU001",
        studentName: "Maria Cristina Santos",
        course: "Junior High School",
        checkIn: "7:45 AM",
        checkOut: "4:30 PM",
        date: new Date().toISOString().split('T')[0],
        status: "Present",
        type: "gate",
        timestamp: new Date(new Date().setHours(7, 45, 0))
    },
    {
        id: "ATT002",
        studentId: "STU002",
        studentName: "Juan Carlos Dela Cruz",
        course: "Junior High School",
        checkIn: "7:50 AM",
        checkOut: "4:25 PM",
        date: new Date().toISOString().split('T')[0],
        status: "Present",
        type: "gate",
        timestamp: new Date(new Date().setHours(7, 50, 0))
    },
    {
        id: "ATT003",
        studentId: "STU003",
        studentName: "Ana Marie Reyes",
        course: "Junior High School",
        checkIn: "8:15 AM",
        checkOut: "4:35 PM",
        date: new Date().toISOString().split('T')[0],
        status: "Late",
        type: "gate",
        timestamp: new Date(new Date().setHours(8, 15, 0))
    },
    {
        id: "ATT004",
        studentId: "STU004",
        studentName: "Jose Miguel Rodriguez",
        course: "Junior High School",
        date: new Date().toISOString().split('T')[0],
        status: "Absent",
        type: "subject",
        subject: "Mathematics",
        period: "1st Period",
        timestamp: new Date(new Date().setHours(8, 0, 0))
    },
    {
        id: "ATT005",
        studentId: "STU005",
        studentName: "Princess Mae Garcia",
        course: "Junior High School",
        checkIn: "7:55 AM",
        checkOut: "4:20 PM",
        date: new Date().toISOString().split('T')[0],
        status: "Present",
        type: "gate",
        timestamp: new Date(new Date().setHours(7, 55, 0))
    },
    {
        id: "ATT006",
        studentId: "STU010",
        studentName: "John Michael Cruz",
        course: "Information and Communications Technology",
        checkIn: "7:40 AM",
        checkOut: "5:00 PM",
        date: new Date().toISOString().split('T')[0],
        status: "Present",
        type: "gate",
        timestamp: new Date(new Date().setHours(7, 40, 0))
    },
    {
        id: "ATT007",
        studentId: "STU012",
        studentName: "Ryan James Bautista",
        course: "Information and Communications Technology",
        checkIn: "8:10 AM",
        checkOut: "5:05 PM",
        date: new Date().toISOString().split('T')[0],
        status: "Late",
        type: "gate",
        timestamp: new Date(new Date().setHours(8, 10, 0))
    }
];
const mockDashboardStats = {
    totalStudents: 1234,
    presentToday: 1105,
    lateToday: 23,
    absentToday: 106,
    attendanceRate: 89.5,
    weeklyTrend: [
        {
            day: 'Mon',
            present: 1150,
            late: 15,
            absent: 69
        },
        {
            day: 'Tue',
            present: 1120,
            late: 28,
            absent: 86
        },
        {
            day: 'Wed',
            present: 1105,
            late: 23,
            absent: 106
        },
        {
            day: 'Thu',
            present: 1140,
            late: 18,
            absent: 76
        },
        {
            day: 'Fri',
            present: 1095,
            late: 35,
            absent: 104
        }
    ],
    gradeBreakdown: [
        {
            grade: '7',
            total: 180,
            present: 165,
            late: 3,
            absent: 12
        },
        {
            grade: '8',
            total: 175,
            present: 158,
            late: 4,
            absent: 13
        },
        {
            grade: '9',
            total: 170,
            present: 155,
            late: 2,
            absent: 13
        },
        {
            grade: '10',
            total: 165,
            present: 148,
            late: 5,
            absent: 12
        },
        {
            grade: '11',
            total: 272,
            present: 245,
            late: 6,
            absent: 21
        },
        {
            grade: '12',
            total: 272,
            present: 234,
            late: 3,
            absent: 35
        }
    ]
};
const mockRecentActivity = [
    {
        id: "ACT001",
        type: "scan",
        studentName: "Maria Cristina Santos",
        action: "Check In",
        time: "2 minutes ago",
        status: "success"
    },
    {
        id: "ACT002",
        type: "scan",
        studentName: "Juan Carlos Dela Cruz",
        action: "Check Out",
        time: "5 minutes ago",
        status: "success"
    },
    {
        id: "ACT003",
        type: "alert",
        studentName: "Jose Miguel Rodriguez",
        action: "Marked Absent",
        time: "15 minutes ago",
        status: "warning"
    },
    {
        id: "ACT004",
        type: "scan",
        studentName: "Princess Mae Garcia",
        action: "Late Arrival",
        time: "25 minutes ago",
        status: "warning"
    }
];
function findStudentById(id) {
    return mockStudents.find((student)=>student.id === id);
}
function findStudentByQRCode(qrCode) {
    return mockStudents.find((student)=>student.qrCode === qrCode);
}
function findSubjectById(id) {
    return mockSubjects.find((subject)=>subject.id === id);
}
function findPeriodById(id) {
    return mockTimePeriods.find((period)=>period.id === id);
}
function getStudentAttendanceRecords(studentId) {
    return mockAttendanceRecords.filter((record)=>record.studentId === studentId);
}
function getTodayAttendanceRecord(studentId) {
    const today = new Date().toISOString().split('T')[0];
    return mockAttendanceRecords.find((record)=>record.studentId === studentId && record.date === today);
}
}),
"[project]/lib/utils/export-utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ReportExcelGenerator": ()=>ReportExcelGenerator,
    "ReportExportManager": ()=>ReportExportManager,
    "ReportPDFGenerator": ()=>ReportPDFGenerator,
    "exportToCSV": ()=>exportToCSV,
    "generateDailyReport": ()=>generateDailyReport,
    "generateSummaryStats": ()=>generateSummaryStats,
    "printDailyReport": ()=>printDailyReport
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-ssr] (ecmascript) <locals>");
;
function exportToCSV(data, filename) {
    const headers = [
        "Student ID",
        "Student Name",
        "Grade",
        "Section",
        "Course",
        "Date",
        "Check In",
        "Check Out",
        "Status",
        "Type",
        "Subject",
        "Period",
        "Reason"
    ];
    const csvContent = [
        headers.join(","),
        ...data.map((record)=>[
                record.studentId,
                `"${record.studentName}"`,
                record.grade,
                `"${record.section}"`,
                `"${record.course}"`,
                record.date,
                record.checkIn || "",
                record.checkOut || "",
                record.status,
                record.type,
                record.subject || "",
                record.period || "",
                record.reason ? `"${record.reason}"` : ""
            ].join(","))
    ].join("\n");
    downloadFile(csvContent, filename || `attendance-report-${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), "yyyy-MM-dd")}.csv`, "text/csv");
}
function generateDailyReport(data, date) {
    const dateStr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, "yyyy-MM-dd");
    const dayData = data.filter((record)=>record.date === dateStr);
    const summary = {
        date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, "MMMM d, yyyy"),
        totalStudents: dayData.length,
        present: dayData.filter((r)=>r.status === "Present").length,
        late: dayData.filter((r)=>r.status === "Late").length,
        absent: dayData.filter((r)=>r.status === "Absent").length,
        attendanceRate: 0
    };
    if (summary.totalStudents > 0) {
        summary.attendanceRate = (summary.present + summary.late) / summary.totalStudents * 100;
    }
    const gradeBreakdown = dayData.reduce((acc, record)=>{
        const grade = record.grade;
        if (!acc[grade]) {
            acc[grade] = {
                total: 0,
                present: 0,
                late: 0,
                absent: 0
            };
        }
        acc[grade].total++;
        acc[grade][record.status.toLowerCase()]++;
        return acc;
    }, {});
    return {
        summary,
        gradeBreakdown,
        records: dayData
    };
}
function printDailyReport(data, date) {
    const report = generateDailyReport(data, date);
    const printContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Daily Attendance Report - ${report.summary.date}</title>
      <style>
        body { 
          font-family: Arial, sans-serif; 
          margin: 20px; 
          color: #333;
        }
        .header { 
          text-align: center; 
          margin-bottom: 30px;
          border-bottom: 2px solid #333;
          padding-bottom: 20px;
        }
        .school-name {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .report-title {
          font-size: 18px;
          color: #666;
        }
        .summary { 
          display: grid; 
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }
        .summary-card {
          border: 1px solid #ddd;
          padding: 15px;
          border-radius: 8px;
          text-align: center;
        }
        .summary-value {
          font-size: 32px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .summary-label {
          color: #666;
          font-size: 14px;
        }
        .present { color: #22c55e; }
        .late { color: #f59e0b; }
        .absent { color: #ef4444; }
        .rate { color: #3b82f6; }
        
        .grade-breakdown {
          margin-bottom: 30px;
        }
        .grade-breakdown h3 {
          margin-bottom: 15px;
          color: #333;
        }
        .grade-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        .grade-table th,
        .grade-table td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: center;
        }
        .grade-table th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        
        .records-table {
          width: 100%;
          border-collapse: collapse;
          font-size: 12px;
        }
        .records-table th,
        .records-table td {
          border: 1px solid #ddd;
          padding: 6px;
          text-align: left;
        }
        .records-table th {
          background-color: #f5f5f5;
          font-weight: bold;
        }
        
        .footer {
          margin-top: 40px;
          text-align: center;
          color: #666;
          font-size: 12px;
          border-top: 1px solid #ddd;
          padding-top: 20px;
        }
        
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="school-name">QRSAMS - Tanauan National High School</div>
        <div class="report-title">Daily Attendance Report</div>
        <div style="margin-top: 10px; font-size: 16px;">${report.summary.date}</div>
      </div>
      
      <div class="summary">
        <div class="summary-card">
          <div class="summary-value">${report.summary.totalStudents}</div>
          <div class="summary-label">Total Students</div>
        </div>
        <div class="summary-card">
          <div class="summary-value present">${report.summary.present}</div>
          <div class="summary-label">Present</div>
        </div>
        <div class="summary-card">
          <div class="summary-value late">${report.summary.late}</div>
          <div class="summary-label">Late</div>
        </div>
        <div class="summary-card">
          <div class="summary-value absent">${report.summary.absent}</div>
          <div class="summary-label">Absent</div>
        </div>
        <div class="summary-card">
          <div class="summary-value rate">${report.summary.attendanceRate.toFixed(1)}%</div>
          <div class="summary-label">Attendance Rate</div>
        </div>
      </div>
      
      <div class="grade-breakdown">
        <h3>Grade Level Breakdown</h3>
        <table class="grade-table">
          <thead>
            <tr>
              <th>Grade</th>
              <th>Total</th>
              <th>Present</th>
              <th>Late</th>
              <th>Absent</th>
              <th>Rate</th>
            </tr>
          </thead>
          <tbody>
            ${Object.entries(report.gradeBreakdown).map(([grade, stats])=>`
              <tr>
                <td>Grade ${grade}</td>
                <td>${stats.total}</td>
                <td class="present">${stats.present}</td>
                <td class="late">${stats.late}</td>
                <td class="absent">${stats.absent}</td>
                <td>${((stats.present + stats.late) / stats.total * 100).toFixed(1)}%</td>
              </tr>
            `).join("")}
          </tbody>
        </table>
      </div>
      
      <div>
        <h3>Detailed Records</h3>
        <table class="records-table">
          <thead>
            <tr>
              <th>Student ID</th>
              <th>Name</th>
              <th>Grade</th>
              <th>Section</th>
              <th>Check In</th>
              <th>Check Out</th>
              <th>Status</th>
              <th>Notes</th>
            </tr>
          </thead>
          <tbody>
            ${report.records.map((record)=>`
              <tr>
                <td>${record.studentId}</td>
                <td>${record.studentName}</td>
                <td>${record.grade}</td>
                <td>${record.section}</td>
                <td>${record.checkIn || "-"}</td>
                <td>${record.checkOut || "-"}</td>
                <td class="${record.status.toLowerCase()}">${record.status}</td>
                <td>${record.reason || "-"}</td>
              </tr>
            `).join("")}
          </tbody>
        </table>
      </div>
      
      <div class="footer">
        Generated on ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), "MMMM d, yyyy 'at' h:mm a")} by QRSAMS
      </div>
    </body>
    </html>
  `;
    const printWindow = window.open("", "_blank");
    if (printWindow) {
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
        printWindow.close();
    }
}
// Utility function to download files
function downloadFile(content, filename, mimeType) {
    const blob = new Blob([
        content
    ], {
        type: mimeType
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}
function generateSummaryStats(data) {
    const total = data.length;
    const present = data.filter((r)=>r.status === "Present").length;
    const late = data.filter((r)=>r.status === "Late").length;
    const absent = data.filter((r)=>r.status === "Absent").length;
    const attendanceRate = total > 0 ? (present + late) / total * 100 : 0;
    return {
        total,
        present,
        late,
        absent,
        attendanceRate
    };
}
class ReportPDFGenerator {
    static async generateSF2PDF(report) {
        const htmlContent = this.generateSF2HTML(report);
        // Simulate PDF generation delay
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        // In a real implementation, this would use a PDF library like jsPDF or Puppeteer
        // For now, we'll create a mock PDF blob
        const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(SF2 Daily Attendance Report) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000206 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF`;
        return new Blob([
            pdfContent
        ], {
            type: 'application/pdf'
        });
    }
    static async generateSF4PDF(report) {
        const htmlContent = this.generateSF4HTML(report);
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 792 612]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 50
>>
stream
BT
/F1 12 Tf
72 720 Td
(SF4 Monthly Learner's Movement) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000206 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
305
%%EOF`;
        return new Blob([
            pdfContent
        ], {
            type: 'application/pdf'
        });
    }
    static async generateCustomPDF(report) {
        const htmlContent = this.generateCustomHTML(report);
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 35
>>
stream
BT
/F1 12 Tf
72 720 Td
(Custom Report) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000206 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
290
%%EOF`;
        return new Blob([
            pdfContent
        ], {
            type: 'application/pdf'
        });
    }
    static generateSF2HTML(report) {
        return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>SF2 Daily Attendance Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .school-info { margin-bottom: 20px; }
        .attendance-table { width: 100%; border-collapse: collapse; }
        .attendance-table th, .attendance-table td { border: 1px solid #000; padding: 8px; text-align: center; }
        .signature-section { margin-top: 40px; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Republic of the Philippines</h1>
        <h2>Department of Education</h2>
        <h3>${report.schoolInfo.region}</h3>
        <h4>${report.schoolInfo.division}</h4>
        <h5>${report.schoolInfo.schoolName}</h5>
        <h2>SCHOOL FORM 2 (SF2)</h2>
        <h3>DAILY ATTENDANCE REPORT OF LEARNERS</h3>
      </div>

      <div class="school-info">
        <p><strong>Grade & Section:</strong> Grade ${report.grade} - ${report.section}</p>
        <p><strong>Subject:</strong> ${report.subject || 'All Subjects'}</p>
        <p><strong>Teacher:</strong> ${report.teacher.name}</p>
        <p><strong>Date:</strong> ${new Date(report.date).toLocaleDateString('en-PH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        })}</p>
      </div>

      <table class="attendance-table">
        <thead>
          <tr>
            <th>No.</th>
            <th>LEARNER'S NAME</th>
            <th>ATTENDANCE</th>
            <th>REMARKS</th>
          </tr>
        </thead>
        <tbody>
          ${report.students.map((student, index)=>`
            <tr>
              <td>${index + 1}</td>
              <td>${student.studentName}</td>
              <td>${student.dailyStatus}</td>
              <td>${student.remarks || ''}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>

      <div class="signature-section">
        <p><strong>Summary:</strong></p>
        <p>Total Students: ${report.summary.totalStudents}</p>
        <p>Present: ${report.summary.presentCount}</p>
        <p>Late: ${report.summary.lateCount}</p>
        <p>Absent: ${report.summary.absentCount}</p>
        <p>Attendance Rate: ${report.summary.attendanceRate.toFixed(1)}%</p>

        <div style="margin-top: 60px;">
          <div style="display: inline-block; width: 300px; text-align: center;">
            <div style="border-bottom: 1px solid #000; height: 40px;"></div>
            <p>Teacher's Signature</p>
            <p>${report.teacher.name}</p>
          </div>
          <div style="display: inline-block; width: 300px; text-align: center; margin-left: 50px;">
            <div style="border-bottom: 1px solid #000; height: 40px;"></div>
            <p>Principal's Signature</p>
            <p>${report.schoolInfo.principalName}</p>
          </div>
        </div>
      </div>
    </body>
    </html>
    `;
    }
    static generateSF4HTML(report) {
        return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>SF4 Monthly Learner's Movement</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .info-section { margin-bottom: 20px; }
        .movement-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .movement-table th, .movement-table td { border: 1px solid #000; padding: 8px; }
        .signature-section { margin-top: 40px; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Republic of the Philippines</h1>
        <h2>Department of Education</h2>
        <h3>${report.schoolInfo.region}</h3>
        <h4>${report.schoolInfo.division}</h4>
        <h5>${report.schoolInfo.schoolName}</h5>
        <h2>SCHOOL FORM 4 (SF4)</h2>
        <h3>MONTHLY REPORT ON LEARNER'S MOVEMENT</h3>
      </div>

      <div class="info-section">
        <p><strong>Month:</strong> ${report.month} ${report.year}</p>
        <p><strong>Grade:</strong> ${report.grade}</p>
        ${report.section ? `<p><strong>Section:</strong> ${report.section}</p>` : ''}
      </div>

      <h4>ENROLLMENT SUMMARY</h4>
      <table class="movement-table">
        <tr>
          <td>Beginning of Month</td>
          <td>${report.enrollment.beginningOfMonth}</td>
        </tr>
        <tr>
          <td>New Admissions</td>
          <td>${report.enrollment.newAdmissions.length}</td>
        </tr>
        <tr>
          <td>Transfers Out</td>
          <td>${report.enrollment.transfers.transferredOut.length}</td>
        </tr>
        <tr>
          <td>Dropouts</td>
          <td>${report.enrollment.dropouts.length}</td>
        </tr>
        <tr>
          <td><strong>End of Month</strong></td>
          <td><strong>${report.enrollment.endOfMonth}</strong></td>
        </tr>
      </table>

      <h4>ATTENDANCE STATISTICS</h4>
      <table class="movement-table">
        <tr>
          <td>Total School Days</td>
          <td>${report.attendance.totalSchoolDays}</td>
        </tr>
        <tr>
          <td>Average Daily Attendance</td>
          <td>${report.attendance.averageAttendance}</td>
        </tr>
        <tr>
          <td>Attendance Rate</td>
          <td>${report.attendance.attendanceRate.toFixed(1)}%</td>
        </tr>
      </table>

      <div class="signature-section">
        <div style="margin-top: 60px;">
          <div style="display: inline-block; width: 300px; text-align: center;">
            <div style="border-bottom: 1px solid #000; height: 40px;"></div>
            <p>Prepared by</p>
            <p>Class Adviser/Teacher</p>
          </div>
          <div style="display: inline-block; width: 300px; text-align: center; margin-left: 50px;">
            <div style="border-bottom: 1px solid #000; height: 40px;"></div>
            <p>Principal's Signature</p>
            <p>${report.schoolInfo.principalName}</p>
          </div>
        </div>
      </div>
    </body>
    </html>
    `;
    }
    static generateCustomHTML(report) {
        return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>${report.name}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .report-table { width: 100%; border-collapse: collapse; }
        .report-table th, .report-table td { border: 1px solid #000; padding: 8px; }
        @media print { body { margin: 0; } }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>${report.name}</h1>
        <p>${report.description}</p>
        <p>Generated on ${new Date(report.generatedAt).toLocaleDateString()}</p>
      </div>

      <table class="report-table">
        <thead>
          <tr>
            ${report.query.fields.map((field)=>`<th>${field}</th>`).join('')}
          </tr>
        </thead>
        <tbody>
          ${report.data.map((row)=>`
            <tr>
              ${report.query.fields.map((field)=>`<td>${row[field] || ''}</td>`).join('')}
            </tr>
          `).join('')}
        </tbody>
      </table>
    </body>
    </html>
    `;
    }
}
class ReportExcelGenerator {
    static async generateSF2Excel(report) {
        // In a real implementation, this would use a library like SheetJS or ExcelJS
        const csvContent = this.generateSF2CSV(report);
        await new Promise((resolve)=>setTimeout(resolve, 500));
        return new Blob([
            csvContent
        ], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
    }
    static async generateSF4Excel(report) {
        const csvContent = this.generateSF4CSV(report);
        await new Promise((resolve)=>setTimeout(resolve, 500));
        return new Blob([
            csvContent
        ], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
    }
    static async generateCustomExcel(report) {
        const csvContent = this.generateCustomCSV(report);
        await new Promise((resolve)=>setTimeout(resolve, 500));
        return new Blob([
            csvContent
        ], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
    }
    static generateSF2CSV(report) {
        const headers = [
            'No.',
            'Student Name',
            'Attendance Status',
            'Remarks'
        ];
        const rows = report.students.map((student, index)=>[
                index + 1,
                student.studentName,
                student.dailyStatus,
                student.remarks || ''
            ]);
        return [
            `SF2 Daily Attendance Report - ${report.date}`,
            `Grade ${report.grade} - ${report.section}`,
            `Teacher: ${report.teacher.name}`,
            '',
            headers.join(','),
            ...rows.map((row)=>row.join(','))
        ].join('\n');
    }
    static generateSF4CSV(report) {
        return [
            `SF4 Monthly Learner's Movement - ${report.month} ${report.year}`,
            `Grade ${report.grade}`,
            '',
            'Enrollment Summary',
            `Beginning of Month,${report.enrollment.beginningOfMonth}`,
            `New Admissions,${report.enrollment.newAdmissions.length}`,
            `Transfers Out,${report.enrollment.transfers.transferredOut.length}`,
            `Dropouts,${report.enrollment.dropouts.length}`,
            `End of Month,${report.enrollment.endOfMonth}`,
            '',
            'Attendance Statistics',
            `Total School Days,${report.attendance.totalSchoolDays}`,
            `Average Daily Attendance,${report.attendance.averageAttendance}`,
            `Attendance Rate,${report.attendance.attendanceRate.toFixed(1)}%`
        ].join('\n');
    }
    static generateCustomCSV(report) {
        const headers = report.query.fields;
        const rows = report.data.map((row)=>headers.map((field)=>row[field] || '').join(','));
        return [
            report.name,
            report.description,
            `Generated on ${new Date(report.generatedAt).toLocaleDateString()}`,
            '',
            headers.join(','),
            ...rows
        ].join('\n');
    }
}
class ReportExportManager {
    static async exportReport(report, format) {
        let blob;
        let filename;
        // Determine report type
        const reportType = 'students' in report ? 'SF2' : 'enrollment' in report ? 'SF4' : 'CUSTOM';
        switch(format){
            case 'PDF':
                if (reportType === 'SF2') {
                    blob = await ReportPDFGenerator.generateSF2PDF(report);
                    filename = `SF2_${report.date}_Grade${report.grade}.pdf`;
                } else if (reportType === 'SF4') {
                    blob = await ReportPDFGenerator.generateSF4PDF(report);
                    filename = `SF4_${report.month}_${report.year}_Grade${report.grade}.pdf`;
                } else {
                    blob = await ReportPDFGenerator.generateCustomPDF(report);
                    filename = `${report.name.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
                }
                break;
            case 'EXCEL':
                if (reportType === 'SF2') {
                    blob = await ReportExcelGenerator.generateSF2Excel(report);
                    filename = `SF2_${report.date}_Grade${report.grade}.xlsx`;
                } else if (reportType === 'SF4') {
                    blob = await ReportExcelGenerator.generateSF4Excel(report);
                    filename = `SF4_${report.month}_${report.year}_Grade${report.grade}.xlsx`;
                } else {
                    blob = await ReportExcelGenerator.generateCustomExcel(report);
                    filename = `${report.name.replace(/[^a-zA-Z0-9]/g, '_')}.xlsx`;
                }
                break;
            case 'CSV':
                if (reportType === 'SF2') {
                    const csvContent = ReportExcelGenerator['generateSF2CSV'](report);
                    blob = new Blob([
                        csvContent
                    ], {
                        type: 'text/csv'
                    });
                    filename = `SF2_${report.date}_Grade${report.grade}.csv`;
                } else if (reportType === 'SF4') {
                    const csvContent = ReportExcelGenerator['generateSF4CSV'](report);
                    blob = new Blob([
                        csvContent
                    ], {
                        type: 'text/csv'
                    });
                    filename = `SF4_${report.month}_${report.year}_Grade${report.grade}.csv`;
                } else {
                    const csvContent = ReportExcelGenerator['generateCustomCSV'](report);
                    blob = new Blob([
                        csvContent
                    ], {
                        type: 'text/csv'
                    });
                    filename = `${report.name.replace(/[^a-zA-Z0-9]/g, '_')}.csv`;
                }
                break;
            default:
                throw new Error(`Unsupported export format: ${format}`);
        }
        return {
            blob,
            filename
        };
    }
    static downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
}
}),
"[project]/app/(dashboard)/reports/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>ReportsPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/tabs.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$type$2d$selector$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/report-type-selector.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$filters$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/report-filters.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$reports$2d$library$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/reports-library.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$sf2$2d$report$2d$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/sf2-report-dialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$sf4$2d$report$2d$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/sf4-report-dialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$custom$2d$report$2d$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/custom-report-dialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$scheduler$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/report-scheduler.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$archive$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/report-archive.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$wizard$2d$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/report-wizard-dialog.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$bulk$2d$report$2d$generator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/bulk-report-generator.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$analytics$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/report-analytics.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/data/reports-mock-data.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-ssr] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-ssr] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-ssr] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-ssr] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-ssr] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-ssr] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function ReportsPage() {
    const [selectedReportType, setSelectedReportType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("SF2");
    const [reportFilters, setReportFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("generate");
    const [showSF2Dialog, setShowSF2Dialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showSF4Dialog, setShowSF4Dialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showCustomDialog, setShowCustomDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showWizardDialog, setShowWizardDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Mock analytics data
    const totalReports = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockGeneratedReports"].length;
    const readyReports = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockGeneratedReports"].filter((r)=>r.status === "READY").length;
    const totalDownloads = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockGeneratedReports"].reduce((sum, r)=>sum + r.downloadCount, 0);
    const scheduledReports = 8 // Mock data
    ;
    const handleGenerateReport = ()=>{
        if (selectedReportType === "SF2") {
            setShowSF2Dialog(true);
        } else if (selectedReportType === "SF4") {
            setShowSF4Dialog(true);
        } else if (selectedReportType === "CUSTOM") {
            setShowCustomDialog(true);
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Report generation started", {
                description: "Your report will be ready in a few minutes"
            });
        }
    };
    const handleDownload = (reportId, format)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(`Downloading report in ${format} format`);
    };
    const handlePreview = (reportId)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].info("Opening report preview");
    };
    const handleDelete = (reportId)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Report deleted successfully");
    };
    const handleArchive = (reportId)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Report archived successfully");
    };
    const handleShare = (reportId)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Report sharing link copied to clipboard");
    };
    const handleRegenerate = (reportId)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Report regeneration started");
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-3xl font-bold tracking-tight",
                                children: "Reports Dashboard"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 91,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground",
                                children: "Generate Philippine DepEd forms and custom attendance reports"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 92,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 90,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                size: "sm",
                                onClick: ()=>setShowWizardDialog(true),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                        className: "mr-2 h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 98,
                                        columnNumber: 13
                                    }, this),
                                    "Wizard"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 97,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                onClick: handleGenerateReport,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                        className: "mr-2 h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 102,
                                        columnNumber: 13
                                    }, this),
                                    "New Report"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 101,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 96,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 89,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid gap-4 md:grid-cols-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-sm font-medium",
                                        children: "Total Reports"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 112,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                                        className: "h-4 w-4 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 113,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 111,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-2xl font-bold",
                                        children: totalReports
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 116,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: [
                                            readyReports,
                                            " ready to download"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 117,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 115,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 110,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-sm font-medium",
                                        children: "Downloads"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 125,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                        className: "h-4 w-4 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 126,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 124,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-2xl font-bold",
                                        children: totalDownloads
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 129,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: "Total downloads"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 130,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 128,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 123,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-sm font-medium",
                                        children: "Scheduled Reports"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 138,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                        className: "h-4 w-4 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 139,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 137,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-2xl font-bold",
                                        children: scheduledReports
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 142,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: "Auto-generated"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 143,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 141,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 136,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardHeader"], {
                                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-sm font-medium",
                                        children: "DepEd Compliance"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 151,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                        className: "h-4 w-4 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 152,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 150,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-2xl font-bold",
                                        children: "100%"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 155,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: "SF2 & SF4 ready"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 156,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 154,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 149,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 109,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tabs"], {
                value: activeTab,
                onValueChange: setActiveTab,
                className: "space-y-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsList"], {
                        className: "grid w-full grid-cols-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "generate",
                                children: "Generate Report"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 166,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "library",
                                children: "Reports Library"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 167,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "bulk",
                                children: "Bulk Generate"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 168,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "scheduled",
                                children: "Scheduled"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 169,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "archive",
                                children: "Archive"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 170,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "analytics",
                                children: "Analytics"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 171,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 165,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "generate",
                        className: "space-y-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid gap-6 lg:grid-cols-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "lg:col-span-2",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$type$2d$selector$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportTypeSelector"], {
                                            selectedType: selectedReportType,
                                            onTypeSelect: (type)=>{
                                                setSelectedReportType(type);
                                                if (type === "SF2") {
                                                    setShowSF2Dialog(true);
                                                } else if (type === "SF4") {
                                                    setShowSF4Dialog(true);
                                                } else if (type === "CUSTOM") {
                                                    setShowCustomDialog(true);
                                                }
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                            lineNumber: 177,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 176,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$filters$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportFilters"], {
                                            filters: reportFilters,
                                            onFiltersChange: setReportFilters,
                                            reportType: selectedReportType
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                            lineNumber: 192,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 191,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 175,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                                    className: "pt-6",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "text-lg font-medium",
                                                        children: "Ready to Generate"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                                        lineNumber: 205,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-muted-foreground",
                                                        children: [
                                                            selectedReportType,
                                                            " report with ",
                                                            Object.keys(reportFilters).length,
                                                            " filters applied"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                                        lineNumber: 206,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                                lineNumber: 204,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                                size: "lg",
                                                onClick: handleGenerateReport,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                                                        className: "mr-2 h-5 w-5"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                                        lineNumber: 211,
                                                        columnNumber: 19
                                                    }, this),
                                                    "Generate Report"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                                lineNumber: 210,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 203,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                    lineNumber: 202,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 201,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 174,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "library",
                        className: "space-y-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$reports$2d$library$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportsLibrary"], {
                            reports: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["mockGeneratedReports"],
                            onDownload: handleDownload,
                            onPreview: handlePreview,
                            onDelete: handleDelete,
                            onArchive: handleArchive,
                            onShare: handleShare,
                            onRegenerate: handleRegenerate
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                            lineNumber: 220,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 219,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "bulk",
                        className: "space-y-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$bulk$2d$report$2d$generator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BulkReportGenerator"], {
                            onGenerate: (configs)=>{
                                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(`Bulk generation started for ${configs.length} reports`);
                            }
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                            lineNumber: 232,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 231,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "scheduled",
                        className: "space-y-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$scheduler$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportScheduler"], {}, void 0, false, {
                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                            lineNumber: 240,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 239,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "archive",
                        className: "space-y-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$archive$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportArchive"], {}, void 0, false, {
                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                            lineNumber: 244,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 243,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "analytics",
                        className: "space-y-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$analytics$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportAnalytics"], {}, void 0, false, {
                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                            lineNumber: 248,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 247,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 164,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$sf2$2d$report$2d$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SF2ReportDialog"], {
                open: showSF2Dialog,
                onOpenChange: setShowSF2Dialog,
                config: {
                    id: `temp-${Date.now()}`,
                    name: `SF2 Report - ${selectedReportType}`,
                    type: selectedReportType,
                    description: "SF2 Daily Attendance Report",
                    dateRange: {
                        startDate: new Date().toISOString().split('T')[0],
                        endDate: new Date().toISOString().split('T')[0]
                    },
                    filters: reportFilters,
                    settings: {
                        includeSignatures: true,
                        pageOrientation: "portrait"
                    },
                    createdBy: "current-user",
                    createdAt: new Date().toISOString(),
                    lastModified: new Date().toISOString()
                }
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 253,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$sf4$2d$report$2d$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SF4ReportDialog"], {
                open: showSF4Dialog,
                onOpenChange: setShowSF4Dialog,
                config: {
                    id: `temp-${Date.now()}`,
                    name: `SF4 Report - ${selectedReportType}`,
                    type: selectedReportType,
                    description: "SF4 Monthly Learner's Movement Report",
                    dateRange: {
                        startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
                        endDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString().split('T')[0]
                    },
                    filters: reportFilters,
                    settings: {
                        includeSignatures: true,
                        pageOrientation: "landscape"
                    },
                    createdBy: "current-user",
                    createdAt: new Date().toISOString(),
                    lastModified: new Date().toISOString()
                }
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 277,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$custom$2d$report$2d$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CustomReportDialog"], {
                open: showCustomDialog,
                onOpenChange: setShowCustomDialog
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 301,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$wizard$2d$dialog$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReportWizardDialog"], {
                open: showWizardDialog,
                onOpenChange: setShowWizardDialog,
                onComplete: (config)=>{
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success("Report wizard completed", {
                        description: `${config.name} is being generated`
                    });
                }
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 307,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/reports/page.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=_a613419d._.js.map