{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,KAA8D;QAA9D,EAAE,GAAG,OAAyD,GAA9D;IACb,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,KAEgC;QAFhC,EAClB,GAAG,OAC+C,GAFhC;IAGlB,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,KAGgC;QAHhC,EACpB,SAAS,EACT,GAAG,OACiD,GAHhC;IAIpB,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,KAOrB;QAPqB,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ,GAPqB;IAQpB,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAGgC;QAHhC,EAClB,SAAS,EACT,GAAG,OAC+C,GAHhC;IAIlB,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport { cn } from \"@/lib/utils\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { \n  LayoutDashboard, \n  Users, \n  ClipboardCheck, \n  FileText, \n  BarChart3, \n  QrCode,\n  GraduationCap\n} from \"lucide-react\"\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\n\nconst sidebarItems = [\n  {\n    title: \"Dashboard\",\n    href: \"/dashboard\",\n    icon: LayoutDashboard\n  },\n  {\n    title: \"Students\",\n    href: \"/students\", \n    icon: Users\n  },\n  {\n    title: \"Attendance\",\n    href: \"/attendance\",\n    icon: ClipboardCheck\n  },\n  {\n    title: \"Reports\",\n    href: \"/reports\",\n    icon: FileText\n  },\n  {\n    title: \"Analytics\",\n    href: \"/analytics\",\n    icon: BarChart3\n  },\n  {\n    title: \"Scanner\",\n    href: \"/scanner\",\n    icon: QrCode\n  }\n]\n\nexport function AppSidebar() {\n  const pathname = usePathname()\n\n  return (\n    <div className=\"flex h-full w-64 flex-col bg-background border-r\">\n      {/* Logo Section */}\n      <div className=\"flex items-center gap-3 p-6 border-b\">\n        <div className=\"h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center\">\n          <GraduationCap className=\"h-5 w-5 text-primary\" />\n        </div>\n        <div>\n          <h2 className=\"text-lg font-semibold\">QRSAMS</h2>\n          <p className=\"text-xs text-muted-foreground\">Tanauan School</p>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 p-4 space-y-2\">\n        {sidebarItems.map((item) => {\n          const Icon = item.icon\n          const isActive = pathname === item.href\n          \n          return (\n            <Button\n              key={item.href}\n              variant={isActive ? \"secondary\" : \"ghost\"}\n              className={cn(\n                \"w-full justify-start gap-3 h-11\",\n                isActive && \"bg-secondary\"\n              )}\n              asChild\n            >\n              <Link href={item.href}>\n                <Icon className=\"h-4 w-4\" />\n                {item.title}\n              </Link>\n            </Button>\n          )\n        })}\n      </nav>\n\n      {/* Footer */}\n      <div className=\"p-4 border-t\">\n        <div className=\"text-center\">\n          <p className=\"text-xs text-muted-foreground\">\n            Tanauan School of Arts and Trade\n          </p>\n          <p className=\"text-xs text-muted-foreground\">\n            Brgy. Cabuynan, Tanauan, Leyte\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAdA;;;;;;AAgBA,MAAM,eAAe;IACnB;QACE,OAAO;QACP,MAAM;QACN,MAAM,+NAAA,CAAA,kBAAe;IACvB;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,6NAAA,CAAA,iBAAc;IACtB;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;IAChB;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,qNAAA,CAAA,YAAS;IACjB;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,6MAAA,CAAA,SAAM;IACd;CACD;AAEM,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;kCAE3B,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;0BAKjD,6LAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC;oBACjB,MAAM,OAAO,KAAK,IAAI;oBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;oBAEvC,qBACE,6LAAC,8HAAA,CAAA,SAAM;wBAEL,SAAS,WAAW,cAAc;wBAClC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mCACA,YAAY;wBAEd,OAAO;kCAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,KAAK,IAAI;;8CACnB,6LAAC;oCAAK,WAAU;;;;;;gCACf,KAAK,KAAK;;;;;;;uBAVR,KAAK,IAAI;;;;;gBAcpB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,6LAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;;;;;;;AAOvD;GAtDgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,6JAAA,CAAA,aAAgB,MAGjC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,6JAAA,CAAA,aAAgB,CAGhC,QAAoD;QAAnD,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO;yBAClD,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,6KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/dashboard/notification-center.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { ScrollArea } from \"@/components/ui/scroll-area\"\nimport { \n  Bell, \n  BellRing, \n  X, \n  AlertTriangle, \n  CheckCircle, \n  Clock, \n  User,\n  MessageSquare,\n  Wifi,\n  WifiOff\n} from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { format } from \"date-fns\"\n\ninterface Notification {\n  id: string\n  type: \"success\" | \"warning\" | \"error\" | \"info\"\n  title: string\n  message: string\n  timestamp: Date\n  read: boolean\n  action?: {\n    label: string\n    onClick: () => void\n  }\n}\n\ninterface NotificationCenterProps {\n  className?: string\n}\n\n// Static initial notifications to prevent hydration issues\nconst getInitialNotifications = (): Notification[] => [\n  {\n    id: \"1\",\n    type: \"warning\",\n    title: \"High Absence Rate\",\n    message: \"Grade 8-A has 15% absence rate today\",\n    timestamp: new Date(\"2024-01-01T10:00:00Z\"),\n    read: false\n  },\n  {\n    id: \"2\",\n    type: \"success\",\n    title: \"Attendance Goal Reached\",\n    message: \"Overall attendance rate reached 90% target\",\n    timestamp: new Date(\"2024-01-01T09:45:00Z\"),\n    read: false\n  },\n  {\n    id: \"3\",\n    type: \"info\",\n    title: \"New Student Registered\",\n    message: \"Maria Santos has been added to Grade 7-A\",\n    timestamp: new Date(\"2024-01-01T09:30:00Z\"),\n    read: true\n  },\n  {\n    id: \"4\",\n    type: \"error\",\n    title: \"Scanner Offline\",\n    message: \"Gate scanner #2 is not responding\",\n    timestamp: new Date(\"2024-01-01T09:15:00Z\"),\n    read: false\n  }\n]\n\nexport function NotificationCenter({ className }: NotificationCenterProps) {\n  const [notifications, setNotifications] = useState<Notification[]>(getInitialNotifications())\n\n  const [isOpen, setIsOpen] = useState(false)\n\n  const unreadCount = notifications.filter(n => !n.read).length\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev => \n      prev.map(notification => \n        notification.id === id \n          ? { ...notification, read: true }\n          : notification\n      )\n    )\n  }\n\n  const markAllAsRead = () => {\n    setNotifications(prev => \n      prev.map(notification => ({ ...notification, read: true }))\n    )\n  }\n\n  const removeNotification = (id: string) => {\n    setNotifications(prev => prev.filter(n => n.id !== id))\n  }\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case \"success\":\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n      case \"warning\":\n        return <AlertTriangle className=\"h-4 w-4 text-yellow-500\" />\n      case \"error\":\n        return <AlertTriangle className=\"h-4 w-4 text-red-500\" />\n      case \"info\":\n        return <MessageSquare className=\"h-4 w-4 text-blue-500\" />\n      default:\n        return <Bell className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getNotificationColor = (type: string) => {\n    switch (type) {\n      case \"success\":\n        return \"border-l-green-500\"\n      case \"warning\":\n        return \"border-l-yellow-500\"\n      case \"error\":\n        return \"border-l-red-500\"\n      case \"info\":\n        return \"border-l-blue-500\"\n      default:\n        return \"border-l-gray-500\"\n    }\n  }\n\n  // Simulate real-time notifications\n  useEffect(() => {\n    // Initialize with current timestamps on client side only\n    setNotifications(prev => prev.map(notification => ({\n      ...notification,\n      timestamp: new Date(Date.now() - (parseInt(notification.id) * 15 * 60 * 1000))\n    })))\n\n    const interval = setInterval(() => {\n      // Randomly add new notifications\n      if (Math.random() < 0.1) { // 10% chance every 5 seconds\n        const newNotification: Notification = {\n          id: Date.now().toString(),\n          type: [\"success\", \"warning\", \"info\"][Math.floor(Math.random() * 3)] as any,\n          title: \"New Activity\",\n          message: \"Student check-in detected\",\n          timestamp: new Date(),\n          read: false\n        }\n        setNotifications(prev => [newNotification, ...prev.slice(0, 9)]) // Keep only 10 notifications\n      }\n    }, 5000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  return (\n    <div className={cn(\"relative\", className)}>\n      <Button\n        variant=\"ghost\"\n        size=\"sm\"\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative\"\n      >\n        {unreadCount > 0 ? (\n          <BellRing className=\"h-5 w-5\" />\n        ) : (\n          <Bell className=\"h-5 w-5\" />\n        )}\n        {unreadCount > 0 && (\n          <Badge \n            variant=\"destructive\" \n            className=\"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs\"\n          >\n            {unreadCount > 9 ? \"9+\" : unreadCount}\n          </Badge>\n        )}\n      </Button>\n\n      {isOpen && (\n        <Card className=\"absolute right-0 top-full mt-2 w-80 z-50 shadow-lg\">\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-lg\">Notifications</CardTitle>\n            <div className=\"flex items-center space-x-2\">\n              {unreadCount > 0 && (\n                <Button variant=\"ghost\" size=\"sm\" onClick={markAllAsRead}>\n                  Mark all read\n                </Button>\n              )}\n              <Button variant=\"ghost\" size=\"sm\" onClick={() => setIsOpen(false)}>\n                <X className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </CardHeader>\n          <CardContent className=\"p-0\">\n            <ScrollArea className=\"h-96\">\n              {notifications.length === 0 ? (\n                <div className=\"p-4 text-center text-muted-foreground\">\n                  No notifications\n                </div>\n              ) : (\n                <div className=\"space-y-1\">\n                  {notifications.map((notification) => (\n                    <div\n                      key={notification.id}\n                      className={cn(\n                        \"p-3 border-l-4 hover:bg-muted/50 cursor-pointer\",\n                        getNotificationColor(notification.type),\n                        !notification.read && \"bg-muted/30\"\n                      )}\n                      onClick={() => markAsRead(notification.id)}\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex items-start space-x-2 flex-1\">\n                          {getNotificationIcon(notification.type)}\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"flex items-center justify-between\">\n                              <p className=\"text-sm font-medium truncate\">\n                                {notification.title}\n                              </p>\n                              {!notification.read && (\n                                <div className=\"w-2 h-2 bg-blue-500 rounded-full ml-2\" />\n                              )}\n                            </div>\n                            <p className=\"text-xs text-muted-foreground mt-1\">\n                              {notification.message}\n                            </p>\n                            <p className=\"text-xs text-muted-foreground mt-1\">\n                              {format(notification.timestamp, \"MMM d, HH:mm\")}\n                            </p>\n                          </div>\n                        </div>\n                        <Button\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            removeNotification(notification.id)\n                          }}\n                          className=\"h-6 w-6 p-0 ml-2\"\n                        >\n                          <X className=\"h-3 w-3\" />\n                        </Button>\n                      </div>\n                      {notification.action && (\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          className=\"mt-2\"\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            notification.action!.onClick()\n                          }}\n                        >\n                          {notification.action.label}\n                        </Button>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </ScrollArea>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n\n// System Health Indicator Component\nexport function SystemHealthIndicator() {\n  const [systemStatus, setSystemStatus] = useState({\n    scanner: \"online\",\n    database: \"online\",\n    sms: \"pending\",\n    network: \"online\"\n  })\n\n  useEffect(() => {\n    // Simulate system status updates\n    const interval = setInterval(() => {\n      setSystemStatus(prev => ({\n        ...prev,\n        network: Math.random() > 0.1 ? \"online\" : \"offline\"\n      }))\n    }, 10000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case \"online\":\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n      case \"offline\":\n        return <WifiOff className=\"h-4 w-4 text-red-500\" />\n      case \"pending\":\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />\n      default:\n        return <AlertTriangle className=\"h-4 w-4 text-gray-500\" />\n    }\n  }\n\n  const getStatusBadge = (status: string) => {\n    const variant = status === \"online\" ? \"default\" : \n                   status === \"pending\" ? \"secondary\" : \"destructive\"\n    return <Badge variant={variant} className=\"text-xs\">{status}</Badge>\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"text-lg flex items-center\">\n          <Wifi className=\"mr-2 h-5 w-5\" />\n          System Health\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-3\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            {getStatusIcon(systemStatus.scanner)}\n            <span className=\"text-sm\">Scanner</span>\n          </div>\n          {getStatusBadge(systemStatus.scanner)}\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            {getStatusIcon(systemStatus.database)}\n            <span className=\"text-sm\">Database</span>\n          </div>\n          {getStatusBadge(systemStatus.database)}\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            {getStatusIcon(systemStatus.sms)}\n            <span className=\"text-sm\">SMS Gateway</span>\n          </div>\n          {getStatusBadge(systemStatus.sms)}\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            {getStatusIcon(systemStatus.network)}\n            <span className=\"text-sm\">Network</span>\n          </div>\n          {getStatusBadge(systemStatus.network)}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;;;AApBA;;;;;;;;;AAuCA,2DAA2D;AAC3D,MAAM,0BAA0B,IAAsB;QACpD;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,WAAW,IAAI,KAAK;YACpB,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,WAAW,IAAI,KAAK;YACpB,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,WAAW,IAAI,KAAK;YACpB,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,WAAW,IAAI,KAAK;YACpB,MAAM;QACR;KACD;AAEM,SAAS,mBAAmB,KAAsC;QAAtC,EAAE,SAAS,EAA2B,GAAtC;;IACjC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAEnE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;IAE7D,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,KAChB;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,IAC9B;IAGV;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,CAAC;IAE7D;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,mCAAmC;IACnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,yDAAyD;YACzD;gDAAiB,CAAA,OAAQ,KAAK,GAAG;wDAAC,CAAA,eAAgB,CAAC;gCACjD,GAAG,YAAY;gCACf,WAAW,IAAI,KAAK,KAAK,GAAG,KAAM,SAAS,aAAa,EAAE,IAAI,KAAK,KAAK;4BAC1E,CAAC;;;YAED,MAAM,WAAW;yDAAY;oBAC3B,iCAAiC;oBACjC,IAAI,KAAK,MAAM,KAAK,KAAK;wBACvB,MAAM,kBAAgC;4BACpC,IAAI,KAAK,GAAG,GAAG,QAAQ;4BACvB,MAAM;gCAAC;gCAAW;gCAAW;6BAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;4BACnE,OAAO;4BACP,SAAS;4BACT,WAAW,IAAI;4BACf,MAAM;wBACR;wBACA;qEAAiB,CAAA,OAAQ;oCAAC;uCAAoB,KAAK,KAAK,CAAC,GAAG;iCAAG;qEAAE,6BAA6B;oBAChG;gBACF;wDAAG;YAEH;gDAAO,IAAM,cAAc;;QAC7B;uCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;0BAC7B,6LAAC,8HAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;oBAET,cAAc,kBACb,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;6CAEpB,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAEjB,cAAc,mBACb,6LAAC,6HAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,WAAU;kCAET,cAAc,IAAI,OAAO;;;;;;;;;;;;YAK/B,wBACC,6LAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,4HAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;0CAC/B,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,mBACb,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,SAAS;kDAAe;;;;;;kDAI5D,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,SAAS,IAAM,UAAU;kDACzD,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAInB,6LAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC,sIAAA,CAAA,aAAU;4BAAC,WAAU;sCACnB,cAAc,MAAM,KAAK,kBACxB,6LAAC;gCAAI,WAAU;0CAAwC;;;;;qDAIvD,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,6LAAC;wCAEC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mDACA,qBAAqB,aAAa,IAAI,GACtC,CAAC,aAAa,IAAI,IAAI;wCAExB,SAAS,IAAM,WAAW,aAAa,EAAE;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,oBAAoB,aAAa,IAAI;0EACtC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FACV,aAAa,KAAK;;;;;;4EAEpB,CAAC,aAAa,IAAI,kBACjB,6LAAC;gFAAI,WAAU;;;;;;;;;;;;kFAGnB,6LAAC;wEAAE,WAAU;kFACV,aAAa,OAAO;;;;;;kFAEvB,6LAAC;wEAAE,WAAU;kFACV,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,aAAa,SAAS,EAAE;;;;;;;;;;;;;;;;;;kEAItC,6LAAC,8HAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,mBAAmB,aAAa,EAAE;wDACpC;wDACA,WAAU;kEAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;;;;;;4CAGhB,aAAa,MAAM,kBAClB,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,aAAa,MAAM,CAAE,OAAO;gDAC9B;0DAEC,aAAa,MAAM,CAAC,KAAK;;;;;;;uCAlDzB,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+D1C;GAlMgB;KAAA;AAqMT,SAAS;;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,SAAS;QACT,UAAU;QACV,KAAK;QACL,SAAS;IACX;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,iCAAiC;YACjC,MAAM,WAAW;4DAAY;oBAC3B;oEAAgB,CAAA,OAAQ,CAAC;gCACvB,GAAG,IAAI;gCACP,SAAS,KAAK,MAAM,KAAK,MAAM,WAAW;4BAC5C,CAAC;;gBACH;2DAAG;YAEH;mDAAO,IAAM,cAAc;;QAC7B;0CAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;QACpC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,UAAU,WAAW,WAAW,YACvB,WAAW,YAAY,cAAc;QACpD,qBAAO,6LAAC,6HAAA,CAAA,QAAK;YAAC,SAAS;YAAS,WAAU;sBAAW;;;;;;IACvD;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;;0BACH,6LAAC,4HAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;0BAIrC,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,aAAa,OAAO;kDACnC,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;4BAE3B,eAAe,aAAa,OAAO;;;;;;;kCAEtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,aAAa,QAAQ;kDACpC,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;4BAE3B,eAAe,aAAa,QAAQ;;;;;;;kCAEvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,aAAa,GAAG;kDAC/B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;4BAE3B,eAAe,aAAa,GAAG;;;;;;;kCAElC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,aAAa,OAAO;kDACnC,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;4BAE3B,eAAe,aAAa,OAAO;;;;;;;;;;;;;;;;;;;AAK9C;IA/EgB;MAAA", "debugId": null}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/layout/header.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Sheet, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from \"@/components/ui/sheet\"\nimport { Menu, Moon, Sun, LogOut } from \"lucide-react\"\nimport { useTheme } from \"next-themes\"\nimport { signOut, useSession } from \"next-auth/react\"\nimport { AppSidebar } from \"./sidebar\"\nimport { NotificationCenter } from \"@/components/dashboard/notification-center\"\n\nexport function Header() {\n  const { setTheme, theme } = useTheme()\n  const { data: session } = useSession()\n\n  const handleSignOut = () => {\n    signOut({ callbackUrl: \"/login\" })\n  }\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container flex h-16 items-center justify-between px-4\">\n        {/* Mobile Menu */}\n        <div className=\"flex items-center gap-4 md:hidden\">\n          <Sheet>\n            <SheetTrigger asChild>\n              <Button variant=\"ghost\" size=\"icon\">\n                <Menu className=\"h-5 w-5\" />\n              </Button>\n            </SheetTrigger>\n            <SheetContent side=\"left\" className=\"p-0\">\n              <AppSidebar />\n            </SheetContent>\n          </Sheet>\n        </div>\n\n        {/* Logo and Title */}\n        <div className=\"flex items-center gap-3\">\n          <div className=\"h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center\">\n            <div className=\"h-6 w-6 rounded bg-primary\" />\n          </div>\n          <div className=\"hidden sm:block\">\n            <h1 className=\"text-lg font-semibold\">QRSAMS</h1>\n            <p className=\"text-xs text-muted-foreground\">Tanauan School of Arts and Trade</p>\n          </div>\n        </div>\n\n        {/* Right Side Actions */}\n        <div className=\"flex items-center gap-2\">\n          {/* Notifications */}\n          <NotificationCenter />\n\n          {/* Theme Toggle */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={() => setTheme(theme === \"light\" ? \"dark\" : \"light\")}\n          >\n            <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n            <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n            <span className=\"sr-only\">Toggle theme</span>\n          </Button>\n\n          {/* User Info and Logout */}\n          {session && (\n            <div className=\"flex items-center gap-2\">\n              <div className=\"hidden sm:block text-right\">\n                <p className=\"text-sm font-medium\">{session.user.name}</p>\n                <p className=\"text-xs text-muted-foreground capitalize\">{session.user.role}</p>\n              </div>\n              <Button variant=\"ghost\" size=\"icon\" onClick={handleSignOut}>\n                <LogOut className=\"h-4 w-4\" />\n                <span className=\"sr-only\">Sign out</span>\n              </Button>\n            </div>\n          )}\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAEnC,MAAM,gBAAgB;QACpB,CAAA,GAAA,wIAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAS;IAClC;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;;0CACJ,6LAAC,6HAAA,CAAA,eAAY;gCAAC,OAAO;0CACnB,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGpB,6LAAC,6HAAA,CAAA,eAAY;gCAAC,MAAK;gCAAO,WAAU;0CAClC,cAAA,6LAAC,mIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;8BAMjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAKjD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,qJAAA,CAAA,qBAAkB;;;;;sCAGnB,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;;8CAErD,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;wBAI3B,yBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAuB,QAAQ,IAAI,CAAC,IAAI;;;;;;sDACrD,6LAAC;4CAAE,WAAU;sDAA4C,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;8CAE5E,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,SAAS;;sDAC3C,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C;GArEgB;;QACc,mJAAA,CAAA,WAAQ;QACV,wIAAA,CAAA,aAAU;;;KAFtB", "debugId": null}}]}