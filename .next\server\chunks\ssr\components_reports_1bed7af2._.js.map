{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/report-type-selector.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { ReportType } from \"@/lib/types/reports\"\nimport { \n  FileText, \n  Calendar, \n  Users, \n  BarChart3, \n  ClipboardList,\n  TrendingUp,\n  BookOpen,\n  UserCheck\n} from \"lucide-react\"\n\ninterface ReportTypeOption {\n  type: ReportType\n  title: string\n  description: string\n  icon: React.ComponentType<{ className?: string }>\n  badge?: string\n  features: string[]\n  recommended?: boolean\n}\n\nconst reportTypeOptions: ReportTypeOption[] = [\n  {\n    type: \"SF2\",\n    title: \"SF2 Daily Attendance\",\n    description: \"Official DepEd SF2 Daily Attendance Report for class records\",\n    icon: ClipboardList,\n    badge: \"DepEd Official\",\n    features: [\n      \"Daily attendance tracking\",\n      \"Teacher signature fields\",\n      \"Class schedule integration\",\n      \"Absence reason coding\",\n      \"Print-ready format\"\n    ],\n    recommended: true\n  },\n  {\n    type: \"SF4\",\n    title: \"SF4 Monthly Movement\",\n    description: \"Official DepEd SF4 Monthly Learner's Movement Report\",\n    icon: TrendingUp,\n    badge: \"DepEd Official\",\n    features: [\n      \"Monthly enrollment summary\",\n      \"Transfer student tracking\",\n      \"Dropout identification\",\n      \"Statistical summaries\",\n      \"Principal review interface\"\n    ],\n    recommended: true\n  },\n  {\n    type: \"DAILY\",\n    title: \"Daily Summary Report\",\n    description: \"Comprehensive daily attendance summary for all grades\",\n    icon: Calendar,\n    features: [\n      \"All grades overview\",\n      \"Real-time statistics\",\n      \"Late arrival tracking\",\n      \"Quick export options\",\n      \"Mobile-friendly format\"\n    ]\n  },\n  {\n    type: \"WEEKLY\",\n    title: \"Weekly Attendance Report\",\n    description: \"Weekly attendance trends and patterns analysis\",\n    icon: BarChart3,\n    features: [\n      \"7-day attendance trends\",\n      \"Grade-level comparisons\",\n      \"Pattern identification\",\n      \"Statistical analysis\",\n      \"Visual charts included\"\n    ]\n  },\n  {\n    type: \"MONTHLY\",\n    title: \"Monthly Summary Report\",\n    description: \"Comprehensive monthly attendance and performance overview\",\n    icon: FileText,\n    features: [\n      \"Monthly statistics\",\n      \"Attendance rate trends\",\n      \"Student performance metrics\",\n      \"Comparative analysis\",\n      \"Executive summary\"\n    ]\n  },\n  {\n    type: \"CUSTOM\",\n    title: \"Custom Report Builder\",\n    description: \"Build custom reports with flexible filters and formats\",\n    icon: Users,\n    badge: \"Flexible\",\n    features: [\n      \"Custom date ranges\",\n      \"Advanced filtering\",\n      \"Multiple export formats\",\n      \"Template saving\",\n      \"Scheduled generation\"\n    ]\n  }\n]\n\ninterface ReportTypeSelectorProps {\n  selectedType?: ReportType\n  onTypeSelect: (type: ReportType) => void\n  className?: string\n}\n\nexport function ReportTypeSelector({ \n  selectedType, \n  onTypeSelect, \n  className \n}: ReportTypeSelectorProps) {\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle>Select Report Type</CardTitle>\n        <CardDescription>\n          Choose the type of report you want to generate\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n          {reportTypeOptions.map((option) => {\n            const Icon = option.icon\n            const isSelected = selectedType === option.type\n            \n            return (\n              <Card \n                key={option.type}\n                className={`cursor-pointer transition-all hover:shadow-md ${\n                  isSelected \n                    ? 'ring-2 ring-primary border-primary' \n                    : 'hover:border-primary/50'\n                } ${option.recommended ? 'relative' : ''}`}\n                onClick={() => onTypeSelect(option.type)}\n              >\n                {option.recommended && (\n                  <div className=\"absolute -top-2 -right-2\">\n                    <Badge variant=\"default\" className=\"text-xs\">\n                      Recommended\n                    </Badge>\n                  </div>\n                )}\n                \n                <CardHeader className=\"pb-3\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`p-2 rounded-lg ${\n                        isSelected \n                          ? 'bg-primary text-primary-foreground' \n                          : 'bg-muted'\n                      }`}>\n                        <Icon className=\"h-5 w-5\" />\n                      </div>\n                      <div>\n                        <CardTitle className=\"text-base\">{option.title}</CardTitle>\n                        {option.badge && (\n                          <Badge variant=\"secondary\" className=\"text-xs mt-1\">\n                            {option.badge}\n                          </Badge>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                  <CardDescription className=\"text-sm\">\n                    {option.description}\n                  </CardDescription>\n                </CardHeader>\n                \n                <CardContent className=\"pt-0\">\n                  <div className=\"space-y-2\">\n                    <h4 className=\"text-sm font-medium text-muted-foreground\">\n                      Key Features:\n                    </h4>\n                    <ul className=\"space-y-1\">\n                      {option.features.slice(0, 3).map((feature, index) => (\n                        <li key={index} className=\"text-xs text-muted-foreground flex items-center gap-2\">\n                          <div className=\"w-1 h-1 bg-muted-foreground rounded-full\" />\n                          {feature}\n                        </li>\n                      ))}\n                      {option.features.length > 3 && (\n                        <li className=\"text-xs text-muted-foreground\">\n                          +{option.features.length - 3} more features\n                        </li>\n                      )}\n                    </ul>\n                  </div>\n                  \n                  <Button \n                    variant={isSelected ? \"default\" : \"outline\"} \n                    size=\"sm\" \n                    className=\"w-full mt-4\"\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      onTypeSelect(option.type)\n                    }}\n                  >\n                    {isSelected ? 'Selected' : 'Select'}\n                  </Button>\n                </CardContent>\n              </Card>\n            )\n          })}\n        </div>\n        \n        {/* Quick Actions for Popular Reports */}\n        <div className=\"mt-6 pt-6 border-t\">\n          <h3 className=\"text-sm font-medium mb-3\">Quick Actions</h3>\n          <div className=\"flex flex-wrap gap-2\">\n            <Button \n              variant=\"outline\" \n              size=\"sm\"\n              onClick={() => onTypeSelect(\"SF2\")}\n              className=\"text-xs\"\n            >\n              <ClipboardList className=\"h-3 w-3 mr-1\" />\n              Today's SF2\n            </Button>\n            <Button \n              variant=\"outline\" \n              size=\"sm\"\n              onClick={() => onTypeSelect(\"SF4\")}\n              className=\"text-xs\"\n            >\n              <TrendingUp className=\"h-3 w-3 mr-1\" />\n              This Month's SF4\n            </Button>\n            <Button \n              variant=\"outline\" \n              size=\"sm\"\n              onClick={() => onTypeSelect(\"WEEKLY\")}\n              className=\"text-xs\"\n            >\n              <BarChart3 className=\"h-3 w-3 mr-1\" />\n              Weekly Summary\n            </Button>\n            <Button \n              variant=\"outline\" \n              size=\"sm\"\n              onClick={() => onTypeSelect(\"CUSTOM\")}\n              className=\"text-xs\"\n            >\n              <Users className=\"h-3 w-3 mr-1\" />\n              Custom Report\n            </Button>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Helper function to get report type info\nexport function getReportTypeInfo(type: ReportType): ReportTypeOption | undefined {\n  return reportTypeOptions.find(option => option.type === type)\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AA2BA,MAAM,oBAAwC;IAC5C;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,wNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,kNAAA,CAAA,aAAU;QAChB,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,0MAAA,CAAA,WAAQ;QACd,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,kNAAA,CAAA,YAAS;QACf,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,8MAAA,CAAA,WAAQ;QACd,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;CACD;AAQM,SAAS,mBAAmB,EACjC,YAAY,EACZ,YAAY,EACZ,SAAS,EACe;IACxB,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,yHAAA,CAAA,aAAU;;kCACT,8OAAC,yHAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,yHAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,yHAAA,CAAA,cAAW;;kCACV,8OAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC;4BACtB,MAAM,OAAO,OAAO,IAAI;4BACxB,MAAM,aAAa,iBAAiB,OAAO,IAAI;4BAE/C,qBACE,8OAAC,yHAAA,CAAA,OAAI;gCAEH,WAAW,CAAC,8CAA8C,EACxD,aACI,uCACA,0BACL,CAAC,EAAE,OAAO,WAAW,GAAG,aAAa,IAAI;gCAC1C,SAAS,IAAM,aAAa,OAAO,IAAI;;oCAEtC,OAAO,WAAW,kBACjB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAU;;;;;;;;;;;kDAMjD,8OAAC,yHAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,eAAe,EAC9B,aACI,uCACA,YACJ;sEACA,cAAA,8OAAC;gEAAK,WAAU;;;;;;;;;;;sEAElB,8OAAC;;8EACC,8OAAC,yHAAA,CAAA,YAAS;oEAAC,WAAU;8EAAa,OAAO,KAAK;;;;;;gEAC7C,OAAO,KAAK,kBACX,8OAAC,0HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;8EAClC,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;0DAMvB,8OAAC,yHAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,OAAO,WAAW;;;;;;;;;;;;kDAIvB,8OAAC,yHAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAG1D,8OAAC;wDAAG,WAAU;;4DACX,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;oEAAe,WAAU;;sFACxB,8OAAC;4EAAI,WAAU;;;;;;wEACd;;mEAFM;;;;;4DAKV,OAAO,QAAQ,CAAC,MAAM,GAAG,mBACxB,8OAAC;gEAAG,WAAU;;oEAAgC;oEAC1C,OAAO,QAAQ,CAAC,MAAM,GAAG;oEAAE;;;;;;;;;;;;;;;;;;;0DAMrC,8OAAC,2HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,YAAY;gDAClC,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,aAAa,OAAO,IAAI;gDAC1B;0DAEC,aAAa,aAAa;;;;;;;;;;;;;+BAtE1B,OAAO,IAAI;;;;;wBA2EtB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG5C,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGzC,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;AAGO,SAAS,kBAAkB,IAAgB;IAChD,OAAO,kBAAkB,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAC1D", "debugId": null}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/report-filters.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { DateRangePicker, DateRange, dateRangePresets } from \"@/components/ui/date-range-picker\"\nimport { ReportFilters as ReportFiltersType, ReportType } from \"@/lib/types/reports\"\nimport { Filter, X, Calendar } from \"lucide-react\"\n\ninterface ReportFiltersProps {\n  filters: ReportFiltersType\n  onFiltersChange: (filters: ReportFiltersType) => void\n  reportType: ReportType\n  className?: string\n}\n\n// Mock data for filter options\nconst gradeOptions = [\"7\", \"8\", \"9\", \"10\", \"11\", \"12\"]\nconst sectionOptions = [\"A\", \"B\", \"C\", \"D\", \"E\"]\nconst courseOptions = [\"Junior High School\", \"Senior High School - STEM\", \"Senior High School - ABM\", \"Senior High School - HUMSS\", \"Senior High School - GAS\"]\nconst subjectOptions = [\"Mathematics\", \"English\", \"Science\", \"Filipino\", \"Social Studies\", \"PE\", \"TLE\", \"Arts\", \"Music\"]\nconst teacherOptions = [\"Prof. Santos\", \"Mrs. Reyes\", \"Mr. Torres\", \"Ms. Garcia\", \"Dr. Rodriguez\"]\nconst attendanceStatusOptions = [\"Present\", \"Late\", \"Absent\"]\n\nexport function ReportFilters({ \n  filters, \n  onFiltersChange, \n  reportType,\n  className \n}: ReportFiltersProps) {\n  const [dateRange, setDateRange] = useState<DateRange>({\n    from: filters.dateRange ? new Date(filters.dateRange.startDate) : undefined,\n    to: filters.dateRange ? new Date(filters.dateRange.endDate) : undefined\n  })\n\n  const handleDateRangeChange = (range: DateRange) => {\n    setDateRange(range)\n    if (range.from && range.to) {\n      onFiltersChange({\n        ...filters,\n        dateRange: {\n          startDate: range.from.toISOString().split('T')[0],\n          endDate: range.to.toISOString().split('T')[0]\n        }\n      })\n    }\n  }\n\n  const handleArrayFilterChange = (key: keyof ReportFiltersType, value: string, checked: boolean) => {\n    const currentArray = (filters[key] as string[]) || []\n    let newArray: string[]\n    \n    if (checked) {\n      newArray = [...currentArray, value]\n    } else {\n      newArray = currentArray.filter(item => item !== value)\n    }\n    \n    onFiltersChange({\n      ...filters,\n      [key]: newArray.length > 0 ? newArray : undefined\n    })\n  }\n\n  const handleBooleanFilterChange = (key: keyof ReportFiltersType, checked: boolean) => {\n    onFiltersChange({\n      ...filters,\n      [key]: checked\n    })\n  }\n\n  const clearFilters = () => {\n    onFiltersChange({})\n    setDateRange({ from: undefined, to: undefined })\n  }\n\n  const applyPreset = (preset: typeof dateRangePresets[0]) => {\n    const range = preset.value()\n    setDateRange(range)\n    onFiltersChange({\n      ...filters,\n      dateRange: {\n        startDate: range.from.toISOString().split('T')[0],\n        endDate: range.to.toISOString().split('T')[0]\n      }\n    })\n  }\n\n  const getActiveFiltersCount = () => {\n    let count = 0\n    if (filters.grades?.length) count++\n    if (filters.sections?.length) count++\n    if (filters.courses?.length) count++\n    if (filters.students?.length) count++\n    if (filters.subjects?.length) count++\n    if (filters.teachers?.length) count++\n    if (filters.attendanceStatus?.length) count++\n    if (filters.includeTransferred) count++\n    if (filters.includeInactive) count++\n    return count\n  }\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-2\">\n            <Filter className=\"h-5 w-5\" />\n            Report Filters\n            {getActiveFiltersCount() > 0 && (\n              <Badge variant=\"secondary\">{getActiveFiltersCount()} active</Badge>\n            )}\n          </div>\n          <Button variant=\"ghost\" size=\"sm\" onClick={clearFilters}>\n            <X className=\"h-4 w-4 mr-1\" />\n            Clear All\n          </Button>\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Date Range */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Date Range</Label>\n          <DateRangePicker\n            value={dateRange}\n            onChange={handleDateRangeChange}\n            className=\"w-full\"\n          />\n          \n          {/* Quick Date Presets */}\n          <div className=\"flex flex-wrap gap-2\">\n            {dateRangePresets.map((preset) => (\n              <Button\n                key={preset.label}\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => applyPreset(preset)}\n                className=\"text-xs\"\n              >\n                <Calendar className=\"h-3 w-3 mr-1\" />\n                {preset.label}\n              </Button>\n            ))}\n          </div>\n        </div>\n\n        {/* Grade Levels */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Grade Levels</Label>\n          <div className=\"grid grid-cols-3 gap-2\">\n            {gradeOptions.map((grade) => (\n              <div key={grade} className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id={`grade-${grade}`}\n                  checked={filters.grades?.includes(grade) || false}\n                  onCheckedChange={(checked) => \n                    handleArrayFilterChange('grades', grade, checked as boolean)\n                  }\n                />\n                <Label htmlFor={`grade-${grade}`} className=\"text-sm\">\n                  Grade {grade}\n                </Label>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Sections */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Sections</Label>\n          <div className=\"grid grid-cols-5 gap-2\">\n            {sectionOptions.map((section) => (\n              <div key={section} className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id={`section-${section}`}\n                  checked={filters.sections?.includes(section) || false}\n                  onCheckedChange={(checked) => \n                    handleArrayFilterChange('sections', section, checked as boolean)\n                  }\n                />\n                <Label htmlFor={`section-${section}`} className=\"text-sm\">\n                  {section}\n                </Label>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Courses */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Courses</Label>\n          <div className=\"space-y-2\">\n            {courseOptions.map((course) => (\n              <div key={course} className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id={`course-${course}`}\n                  checked={filters.courses?.includes(course) || false}\n                  onCheckedChange={(checked) => \n                    handleArrayFilterChange('courses', course, checked as boolean)\n                  }\n                />\n                <Label htmlFor={`course-${course}`} className=\"text-sm\">\n                  {course}\n                </Label>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Subjects (for subject-specific reports) */}\n        {(reportType === 'CUSTOM' || reportType === 'SF2') && (\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium\">Subjects</Label>\n            <div className=\"space-y-2\">\n              {subjectOptions.map((subject) => (\n                <div key={subject} className=\"flex items-center space-x-2\">\n                  <Checkbox\n                    id={`subject-${subject}`}\n                    checked={filters.subjects?.includes(subject) || false}\n                    onCheckedChange={(checked) => \n                      handleArrayFilterChange('subjects', subject, checked as boolean)\n                    }\n                  />\n                  <Label htmlFor={`subject-${subject}`} className=\"text-sm\">\n                    {subject}\n                  </Label>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Attendance Status */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Attendance Status</Label>\n          <div className=\"space-y-2\">\n            {attendanceStatusOptions.map((status) => (\n              <div key={status} className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id={`status-${status}`}\n                  checked={filters.attendanceStatus?.includes(status as any) || false}\n                  onCheckedChange={(checked) => \n                    handleArrayFilterChange('attendanceStatus', status, checked as boolean)\n                  }\n                />\n                <Label htmlFor={`status-${status}`} className=\"text-sm\">\n                  {status}\n                </Label>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Additional Options */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Additional Options</Label>\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-transferred\"\n                checked={filters.includeTransferred || false}\n                onCheckedChange={(checked) => \n                  handleBooleanFilterChange('includeTransferred', checked as boolean)\n                }\n              />\n              <Label htmlFor=\"include-transferred\" className=\"text-sm\">\n                Include transferred students\n              </Label>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-inactive\"\n                checked={filters.includeInactive || false}\n                onCheckedChange={(checked) => \n                  handleBooleanFilterChange('includeInactive', checked as boolean)\n                }\n              />\n              <Label htmlFor=\"include-inactive\" className=\"text-sm\">\n                Include inactive students\n              </Label>\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAZA;;;;;;;;;;AAqBA,+BAA+B;AAC/B,MAAM,eAAe;IAAC;IAAK;IAAK;IAAK;IAAM;IAAM;CAAK;AACtD,MAAM,iBAAiB;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI;AAChD,MAAM,gBAAgB;IAAC;IAAsB;IAA6B;IAA4B;IAA8B;CAA2B;AAC/J,MAAM,iBAAiB;IAAC;IAAe;IAAW;IAAW;IAAY;IAAkB;IAAM;IAAO;IAAQ;CAAQ;AACxH,MAAM,iBAAiB;IAAC;IAAgB;IAAc;IAAc;IAAc;CAAgB;AAClG,MAAM,0BAA0B;IAAC;IAAW;IAAQ;CAAS;AAEtD,SAAS,cAAc,EAC5B,OAAO,EACP,eAAe,EACf,UAAU,EACV,SAAS,EACU;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM,QAAQ,SAAS,GAAG,IAAI,KAAK,QAAQ,SAAS,CAAC,SAAS,IAAI;QAClE,IAAI,QAAQ,SAAS,GAAG,IAAI,KAAK,QAAQ,SAAS,CAAC,OAAO,IAAI;IAChE;IAEA,MAAM,wBAAwB,CAAC;QAC7B,aAAa;QACb,IAAI,MAAM,IAAI,IAAI,MAAM,EAAE,EAAE;YAC1B,gBAAgB;gBACd,GAAG,OAAO;gBACV,WAAW;oBACT,WAAW,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACjD,SAAS,MAAM,EAAE,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC/C;YACF;QACF;IACF;IAEA,MAAM,0BAA0B,CAAC,KAA8B,OAAe;QAC5E,MAAM,eAAe,AAAC,OAAO,CAAC,IAAI,IAAiB,EAAE;QACrD,IAAI;QAEJ,IAAI,SAAS;YACX,WAAW;mBAAI;gBAAc;aAAM;QACrC,OAAO;YACL,WAAW,aAAa,MAAM,CAAC,CAAA,OAAQ,SAAS;QAClD;QAEA,gBAAgB;YACd,GAAG,OAAO;YACV,CAAC,IAAI,EAAE,SAAS,MAAM,GAAG,IAAI,WAAW;QAC1C;IACF;IAEA,MAAM,4BAA4B,CAAC,KAA8B;QAC/D,gBAAgB;YACd,GAAG,OAAO;YACV,CAAC,IAAI,EAAE;QACT;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB,CAAC;QACjB,aAAa;YAAE,MAAM;YAAW,IAAI;QAAU;IAChD;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,OAAO,KAAK;QAC1B,aAAa;QACb,gBAAgB;YACd,GAAG,OAAO;YACV,WAAW;gBACT,WAAW,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACjD,SAAS,MAAM,EAAE,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC/C;QACF;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,QAAQ;QACZ,IAAI,QAAQ,MAAM,EAAE,QAAQ;QAC5B,IAAI,QAAQ,QAAQ,EAAE,QAAQ;QAC9B,IAAI,QAAQ,OAAO,EAAE,QAAQ;QAC7B,IAAI,QAAQ,QAAQ,EAAE,QAAQ;QAC9B,IAAI,QAAQ,QAAQ,EAAE,QAAQ;QAC9B,IAAI,QAAQ,QAAQ,EAAE,QAAQ;QAC9B,IAAI,QAAQ,gBAAgB,EAAE,QAAQ;QACtC,IAAI,QAAQ,kBAAkB,EAAE;QAChC,IAAI,QAAQ,eAAe,EAAE;QAC7B,OAAO;IACT;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,yHAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;gCAE7B,0BAA0B,mBACzB,8OAAC,0HAAA,CAAA,QAAK;oCAAC,SAAQ;;wCAAa;wCAAwB;;;;;;;;;;;;;sCAGxD,8OAAC,2HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,SAAS;;8CACzC,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAKpC,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,8OAAC,4IAAA,CAAA,kBAAe;gCACd,OAAO;gCACP,UAAU;gCACV,WAAU;;;;;;0CAIZ,8OAAC;gCAAI,WAAU;0CACZ,4IAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,uBACrB,8OAAC,2HAAA,CAAA,SAAM;wCAEL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,OAAO,KAAK;;uCAPR,OAAO,KAAK;;;;;;;;;;;;;;;;kCAczB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC,6HAAA,CAAA,WAAQ;gDACP,IAAI,CAAC,MAAM,EAAE,OAAO;gDACpB,SAAS,QAAQ,MAAM,EAAE,SAAS,UAAU;gDAC5C,iBAAiB,CAAC,UAChB,wBAAwB,UAAU,OAAO;;;;;;0DAG7C,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAS,CAAC,MAAM,EAAE,OAAO;gDAAE,WAAU;;oDAAU;oDAC7C;;;;;;;;uCATD;;;;;;;;;;;;;;;;kCAiBhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,8OAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;wCAAkB,WAAU;;0DAC3B,8OAAC,6HAAA,CAAA,WAAQ;gDACP,IAAI,CAAC,QAAQ,EAAE,SAAS;gDACxB,SAAS,QAAQ,QAAQ,EAAE,SAAS,YAAY;gDAChD,iBAAiB,CAAC,UAChB,wBAAwB,YAAY,SAAS;;;;;;0DAGjD,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAS,CAAC,QAAQ,EAAE,SAAS;gDAAE,WAAU;0DAC7C;;;;;;;uCATK;;;;;;;;;;;;;;;;kCAiBhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;wCAAiB,WAAU;;0DAC1B,8OAAC,6HAAA,CAAA,WAAQ;gDACP,IAAI,CAAC,OAAO,EAAE,QAAQ;gDACtB,SAAS,QAAQ,OAAO,EAAE,SAAS,WAAW;gDAC9C,iBAAiB,CAAC,UAChB,wBAAwB,WAAW,QAAQ;;;;;;0DAG/C,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAS,CAAC,OAAO,EAAE,QAAQ;gDAAE,WAAU;0DAC3C;;;;;;;uCATK;;;;;;;;;;;;;;;;oBAiBf,CAAC,eAAe,YAAY,eAAe,KAAK,mBAC/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,8OAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;wCAAkB,WAAU;;0DAC3B,8OAAC,6HAAA,CAAA,WAAQ;gDACP,IAAI,CAAC,QAAQ,EAAE,SAAS;gDACxB,SAAS,QAAQ,QAAQ,EAAE,SAAS,YAAY;gDAChD,iBAAiB,CAAC,UAChB,wBAAwB,YAAY,SAAS;;;;;;0DAGjD,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAS,CAAC,QAAQ,EAAE,SAAS;gDAAE,WAAU;0DAC7C;;;;;;;uCATK;;;;;;;;;;;;;;;;kCAkBlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,8OAAC;gCAAI,WAAU;0CACZ,wBAAwB,GAAG,CAAC,CAAC,uBAC5B,8OAAC;wCAAiB,WAAU;;0DAC1B,8OAAC,6HAAA,CAAA,WAAQ;gDACP,IAAI,CAAC,OAAO,EAAE,QAAQ;gDACtB,SAAS,QAAQ,gBAAgB,EAAE,SAAS,WAAkB;gDAC9D,iBAAiB,CAAC,UAChB,wBAAwB,oBAAoB,QAAQ;;;;;;0DAGxD,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAS,CAAC,OAAO,EAAE,QAAQ;gDAAE,WAAU;0DAC3C;;;;;;;uCATK;;;;;;;;;;;;;;;;kCAiBhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,SAAS,QAAQ,kBAAkB,IAAI;gDACvC,iBAAiB,CAAC,UAChB,0BAA0B,sBAAsB;;;;;;0DAGpD,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAsB,WAAU;0DAAU;;;;;;;;;;;;kDAI3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,SAAS,QAAQ,eAAe,IAAI;gDACpC,iBAAiB,CAAC,UAChB,0BAA0B,mBAAmB;;;;;;0DAGjD,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAmB,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE", "debugId": null}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/reports-library.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Input } from \"@/components/ui/input\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\"\nimport { GeneratedReport, ReportStatus, ReportType, ExportFormat } from \"@/lib/types/reports\"\nimport { \n  Download, \n  Eye, \n  MoreHorizontal, \n  Search, \n  Filter,\n  FileText,\n  Calendar,\n  Clock,\n  Users,\n  Trash2,\n  Share,\n  Archive,\n  RefreshCw\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\ninterface ReportsLibraryProps {\n  reports: GeneratedReport[]\n  onDownload: (reportId: string, format: ExportFormat) => void\n  onPreview: (reportId: string) => void\n  onDelete: (reportId: string) => void\n  onArchive: (reportId: string) => void\n  onShare: (reportId: string) => void\n  onRegenerate: (reportId: string) => void\n  className?: string\n}\n\nexport function ReportsLibrary({\n  reports,\n  onDownload,\n  onPreview,\n  onDelete,\n  onArchive,\n  onShare,\n  onRegenerate,\n  className\n}: ReportsLibraryProps) {\n  const [searchQuery, setSearchQuery] = useState(\"\")\n  const [statusFilter, setStatusFilter] = useState<ReportStatus | \"ALL\">(\"ALL\")\n  const [typeFilter, setTypeFilter] = useState<ReportType | \"ALL\">(\"ALL\")\n  const [sortBy, setSortBy] = useState<\"date\" | \"name\" | \"type\" | \"downloads\">(\"date\")\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"desc\")\n\n  // Filter and sort reports\n  const filteredReports = reports\n    .filter(report => {\n      const matchesSearch = report.config.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                           report.config.description.toLowerCase().includes(searchQuery.toLowerCase())\n      const matchesStatus = statusFilter === \"ALL\" || report.status === statusFilter\n      const matchesType = typeFilter === \"ALL\" || report.config.type === typeFilter\n      return matchesSearch && matchesStatus && matchesType\n    })\n    .sort((a, b) => {\n      let comparison = 0\n      switch (sortBy) {\n        case \"date\":\n          comparison = new Date(a.generatedAt).getTime() - new Date(b.generatedAt).getTime()\n          break\n        case \"name\":\n          comparison = a.config.name.localeCompare(b.config.name)\n          break\n        case \"type\":\n          comparison = a.config.type.localeCompare(b.config.type)\n          break\n        case \"downloads\":\n          comparison = a.downloadCount - b.downloadCount\n          break\n      }\n      return sortOrder === \"desc\" ? -comparison : comparison\n    })\n\n  const getStatusBadge = (status: ReportStatus) => {\n    const variants = {\n      READY: \"default\",\n      GENERATING: \"secondary\",\n      DRAFT: \"outline\",\n      FAILED: \"destructive\",\n      ARCHIVED: \"secondary\"\n    } as const\n\n    const colors = {\n      READY: \"text-green-600\",\n      GENERATING: \"text-blue-600\",\n      DRAFT: \"text-gray-600\",\n      FAILED: \"text-red-600\",\n      ARCHIVED: \"text-gray-500\"\n    } as const\n\n    return (\n      <Badge variant={variants[status]} className={colors[status]}>\n        {status === \"GENERATING\" && <RefreshCw className=\"h-3 w-3 mr-1 animate-spin\" />}\n        {status}\n      </Badge>\n    )\n  }\n\n  const getTypeBadge = (type: ReportType) => {\n    const colors = {\n      SF2: \"bg-blue-100 text-blue-800\",\n      SF4: \"bg-green-100 text-green-800\",\n      DAILY: \"bg-purple-100 text-purple-800\",\n      WEEKLY: \"bg-orange-100 text-orange-800\",\n      MONTHLY: \"bg-pink-100 text-pink-800\",\n      ANNUAL: \"bg-indigo-100 text-indigo-800\",\n      CUSTOM: \"bg-gray-100 text-gray-800\"\n    } as const\n\n    return (\n      <Badge variant=\"secondary\" className={colors[type]}>\n        {type}\n      </Badge>\n    )\n  }\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes'\n    const k = 1024\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\">\n              <FileText className=\"h-5 w-5\" />\n              Generated Reports\n              <Badge variant=\"secondary\">{filteredReports.length}</Badge>\n            </CardTitle>\n            <CardDescription>\n              View, download, and manage your generated reports\n            </CardDescription>\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {/* Filters and Search */}\n        <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Search reports...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n          \n          <div className=\"flex gap-2\">\n            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as ReportStatus | \"ALL\")}>\n              <SelectTrigger className=\"w-32\">\n                <SelectValue placeholder=\"Status\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"ALL\">All Status</SelectItem>\n                <SelectItem value=\"READY\">Ready</SelectItem>\n                <SelectItem value=\"GENERATING\">Generating</SelectItem>\n                <SelectItem value=\"DRAFT\">Draft</SelectItem>\n                <SelectItem value=\"FAILED\">Failed</SelectItem>\n                <SelectItem value=\"ARCHIVED\">Archived</SelectItem>\n              </SelectContent>\n            </Select>\n\n            <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as ReportType | \"ALL\")}>\n              <SelectTrigger className=\"w-32\">\n                <SelectValue placeholder=\"Type\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"ALL\">All Types</SelectItem>\n                <SelectItem value=\"SF2\">SF2</SelectItem>\n                <SelectItem value=\"SF4\">SF4</SelectItem>\n                <SelectItem value=\"DAILY\">Daily</SelectItem>\n                <SelectItem value=\"WEEKLY\">Weekly</SelectItem>\n                <SelectItem value=\"MONTHLY\">Monthly</SelectItem>\n                <SelectItem value=\"CUSTOM\">Custom</SelectItem>\n              </SelectContent>\n            </Select>\n\n            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {\n              const [field, order] = value.split('-')\n              setSortBy(field as typeof sortBy)\n              setSortOrder(order as typeof sortOrder)\n            }}>\n              <SelectTrigger className=\"w-40\">\n                <SelectValue placeholder=\"Sort by\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"date-desc\">Newest First</SelectItem>\n                <SelectItem value=\"date-asc\">Oldest First</SelectItem>\n                <SelectItem value=\"name-asc\">Name A-Z</SelectItem>\n                <SelectItem value=\"name-desc\">Name Z-A</SelectItem>\n                <SelectItem value=\"downloads-desc\">Most Downloaded</SelectItem>\n                <SelectItem value=\"downloads-asc\">Least Downloaded</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </div>\n\n        {/* Reports Table */}\n        <div className=\"rounded-md border\">\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Report</TableHead>\n                <TableHead>Type</TableHead>\n                <TableHead>Status</TableHead>\n                <TableHead>Generated</TableHead>\n                <TableHead>Size</TableHead>\n                <TableHead>Downloads</TableHead>\n                <TableHead className=\"text-right\">Actions</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {filteredReports.length === 0 ? (\n                <TableRow>\n                  <TableCell colSpan={7} className=\"text-center py-8 text-muted-foreground\">\n                    No reports found matching your criteria\n                  </TableCell>\n                </TableRow>\n              ) : (\n                filteredReports.map((report) => (\n                  <TableRow key={report.id}>\n                    <TableCell>\n                      <div className=\"space-y-1\">\n                        <div className=\"font-medium\">{report.config.name}</div>\n                        <div className=\"text-sm text-muted-foreground\">\n                          {report.config.description}\n                        </div>\n                        <div className=\"text-xs text-muted-foreground\">\n                          {format(new Date(report.config.dateRange.startDate), \"MMM dd\")} - {format(new Date(report.config.dateRange.endDate), \"MMM dd, yyyy\")}\n                        </div>\n                      </div>\n                    </TableCell>\n                    <TableCell>\n                      {getTypeBadge(report.config.type)}\n                    </TableCell>\n                    <TableCell>\n                      {getStatusBadge(report.status)}\n                    </TableCell>\n                    <TableCell>\n                      <div className=\"flex items-center gap-1 text-sm text-muted-foreground\">\n                        <Clock className=\"h-3 w-3\" />\n                        {format(new Date(report.generatedAt), \"MMM dd, HH:mm\")}\n                      </div>\n                    </TableCell>\n                    <TableCell>\n                      <div className=\"text-sm\">\n                        {report.fileSize ? formatFileSize(report.fileSize) : '-'}\n                      </div>\n                    </TableCell>\n                    <TableCell>\n                      <div className=\"flex items-center gap-1 text-sm\">\n                        <Download className=\"h-3 w-3\" />\n                        {report.downloadCount}\n                      </div>\n                    </TableCell>\n                    <TableCell className=\"text-right\">\n                      <div className=\"flex items-center justify-end gap-2\">\n                        {report.status === \"READY\" && (\n                          <>\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={() => onPreview(report.id)}\n                            >\n                              <Eye className=\"h-4 w-4\" />\n                            </Button>\n                            <DropdownMenu>\n                              <DropdownMenuTrigger asChild>\n                                <Button variant=\"ghost\" size=\"sm\">\n                                  <Download className=\"h-4 w-4\" />\n                                </Button>\n                              </DropdownMenuTrigger>\n                              <DropdownMenuContent align=\"end\">\n                                <DropdownMenuItem onClick={() => onDownload(report.id, \"PDF\")}>\n                                  Download PDF\n                                </DropdownMenuItem>\n                                <DropdownMenuItem onClick={() => onDownload(report.id, \"EXCEL\")}>\n                                  Download Excel\n                                </DropdownMenuItem>\n                                <DropdownMenuItem onClick={() => onDownload(report.id, \"CSV\")}>\n                                  Download CSV\n                                </DropdownMenuItem>\n                              </DropdownMenuContent>\n                            </DropdownMenu>\n                          </>\n                        )}\n                        \n                        <DropdownMenu>\n                          <DropdownMenuTrigger asChild>\n                            <Button variant=\"ghost\" size=\"sm\">\n                              <MoreHorizontal className=\"h-4 w-4\" />\n                            </Button>\n                          </DropdownMenuTrigger>\n                          <DropdownMenuContent align=\"end\">\n                            <DropdownMenuItem onClick={() => onShare(report.id)}>\n                              <Share className=\"h-4 w-4 mr-2\" />\n                              Share\n                            </DropdownMenuItem>\n                            <DropdownMenuItem onClick={() => onRegenerate(report.id)}>\n                              <RefreshCw className=\"h-4 w-4 mr-2\" />\n                              Regenerate\n                            </DropdownMenuItem>\n                            <DropdownMenuItem onClick={() => onArchive(report.id)}>\n                              <Archive className=\"h-4 w-4 mr-2\" />\n                              Archive\n                            </DropdownMenuItem>\n                            <DropdownMenuItem \n                              onClick={() => onDelete(report.id)}\n                              className=\"text-destructive\"\n                            >\n                              <Trash2 className=\"h-4 w-4 mr-2\" />\n                              Delete\n                            </DropdownMenuItem>\n                          </DropdownMenuContent>\n                        </DropdownMenu>\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))\n              )}\n            </TableBody>\n          </Table>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AA1BA;;;;;;;;;;;;AAuCO,SAAS,eAAe,EAC7B,OAAO,EACP,UAAU,EACV,SAAS,EACT,QAAQ,EACR,SAAS,EACT,OAAO,EACP,YAAY,EACZ,SAAS,EACW;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0C;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,0BAA0B;IAC1B,MAAM,kBAAkB,QACrB,MAAM,CAAC,CAAA;QACN,MAAM,gBAAgB,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAClE,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAC7F,MAAM,gBAAgB,iBAAiB,SAAS,OAAO,MAAM,KAAK;QAClE,MAAM,cAAc,eAAe,SAAS,OAAO,MAAM,CAAC,IAAI,KAAK;QACnE,OAAO,iBAAiB,iBAAiB;IAC3C,GACC,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,aAAa,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;gBAChF;YACF,KAAK;gBACH,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,IAAI;gBACtD;YACF,KAAK;gBACH,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,IAAI;gBACtD;YACF,KAAK;gBACH,aAAa,EAAE,aAAa,GAAG,EAAE,aAAa;gBAC9C;QACJ;QACA,OAAO,cAAc,SAAS,CAAC,aAAa;IAC9C;IAEF,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW;YACf,OAAO;YACP,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;QAEA,MAAM,SAAS;YACb,OAAO;YACP,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;QAEA,qBACE,8OAAC,0HAAA,CAAA,QAAK;YAAC,SAAS,QAAQ,CAAC,OAAO;YAAE,WAAW,MAAM,CAAC,OAAO;;gBACxD,WAAW,8BAAgB,8OAAC,gNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;gBAChD;;;;;;;IAGP;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YACb,KAAK;YACL,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QAEA,qBACE,8OAAC,0HAAA,CAAA,QAAK;YAAC,SAAQ;YAAY,WAAW,MAAM,CAAC,KAAK;sBAC/C;;;;;;IAGP;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,8OAAC,yHAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,yHAAA,CAAA,aAAU;0BACT,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;0CACC,8OAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;kDAEhC,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAa,gBAAgB,MAAM;;;;;;;;;;;;0CAEpD,8OAAC,yHAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;;;;;;;;;;;0BAMvB,8OAAC,yHAAA,CAAA,cAAW;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,0HAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAc,eAAe,CAAC,QAAU,gBAAgB;;0DACrE,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,2HAAA,CAAA,gBAAa;;kEACZ,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAa;;;;;;kEAC/B,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,8OAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe,CAAC,QAAU,cAAc;;0DACjE,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,2HAAA,CAAA,gBAAa;;kEACZ,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;;;;;;;;;;;;;kDAI/B,8OAAC,2HAAA,CAAA,SAAM;wCAAC,OAAO,GAAG,OAAO,CAAC,EAAE,WAAW;wCAAE,eAAe,CAAC;4CACvD,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,KAAK,CAAC;4CACnC,UAAU;4CACV,aAAa;wCACf;;0DACE,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,2HAAA,CAAA,gBAAa;;kEACZ,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;kEAC7B,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;kEAC7B,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAiB;;;;;;kEACnC,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;8CACJ,8OAAC,0HAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;;0DACP,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,0HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,0HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAa;;;;;;;;;;;;;;;;;8CAGtC,8OAAC,0HAAA,CAAA,YAAS;8CACP,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC,0HAAA,CAAA,WAAQ;kDACP,cAAA,8OAAC,0HAAA,CAAA,YAAS;4CAAC,SAAS;4CAAG,WAAU;sDAAyC;;;;;;;;;;+CAK5E,gBAAgB,GAAG,CAAC,CAAC,uBACnB,8OAAC,0HAAA,CAAA,WAAQ;;8DACP,8OAAC,0HAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAe,OAAO,MAAM,CAAC,IAAI;;;;;;0EAChD,8OAAC;gEAAI,WAAU;0EACZ,OAAO,MAAM,CAAC,WAAW;;;;;;0EAE5B,8OAAC;gEAAI,WAAU;;oEACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,MAAM,CAAC,SAAS,CAAC,SAAS,GAAG;oEAAU;oEAAI,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG;;;;;;;;;;;;;;;;;;8DAI3H,8OAAC,0HAAA,CAAA,YAAS;8DACP,aAAa,OAAO,MAAM,CAAC,IAAI;;;;;;8DAElC,8OAAC,0HAAA,CAAA,YAAS;8DACP,eAAe,OAAO,MAAM;;;;;;8DAE/B,8OAAC,0HAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,WAAW,GAAG;;;;;;;;;;;;8DAG1C,8OAAC,0HAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;kEACZ,OAAO,QAAQ,GAAG,eAAe,OAAO,QAAQ,IAAI;;;;;;;;;;;8DAGzD,8OAAC,0HAAA,CAAA,YAAS;8DACR,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,OAAO,aAAa;;;;;;;;;;;;8DAGzB,8OAAC,0HAAA,CAAA,YAAS;oDAAC,WAAU;8DACnB,cAAA,8OAAC;wDAAI,WAAU;;4DACZ,OAAO,MAAM,KAAK,yBACjB;;kFACE,8OAAC,2HAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,UAAU,OAAO,EAAE;kFAElC,cAAA,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;kFAEjB,8OAAC,qIAAA,CAAA,eAAY;;0FACX,8OAAC,qIAAA,CAAA,sBAAmB;gFAAC,OAAO;0FAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;oFAAC,SAAQ;oFAAQ,MAAK;8FAC3B,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGxB,8OAAC,qIAAA,CAAA,sBAAmB;gFAAC,OAAM;;kGACzB,8OAAC,qIAAA,CAAA,mBAAgB;wFAAC,SAAS,IAAM,WAAW,OAAO,EAAE,EAAE;kGAAQ;;;;;;kGAG/D,8OAAC,qIAAA,CAAA,mBAAgB;wFAAC,SAAS,IAAM,WAAW,OAAO,EAAE,EAAE;kGAAU;;;;;;kGAGjE,8OAAC,qIAAA,CAAA,mBAAgB;wFAAC,SAAS,IAAM,WAAW,OAAO,EAAE,EAAE;kGAAQ;;;;;;;;;;;;;;;;;;;;0EAQvE,8OAAC,qIAAA,CAAA,eAAY;;kFACX,8OAAC,qIAAA,CAAA,sBAAmB;wEAAC,OAAO;kFAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;4EAAC,SAAQ;4EAAQ,MAAK;sFAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;gFAAC,WAAU;;;;;;;;;;;;;;;;kFAG9B,8OAAC,qIAAA,CAAA,sBAAmB;wEAAC,OAAM;;0FACzB,8OAAC,qIAAA,CAAA,mBAAgB;gFAAC,SAAS,IAAM,QAAQ,OAAO,EAAE;;kGAChD,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGpC,8OAAC,qIAAA,CAAA,mBAAgB;gFAAC,SAAS,IAAM,aAAa,OAAO,EAAE;;kGACrD,8OAAC,gNAAA,CAAA,YAAS;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGxC,8OAAC,qIAAA,CAAA,mBAAgB;gFAAC,SAAS,IAAM,UAAU,OAAO,EAAE;;kGAClD,8OAAC,wMAAA,CAAA,UAAO;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGtC,8OAAC,qIAAA,CAAA,mBAAgB;gFACf,SAAS,IAAM,SAAS,OAAO,EAAE;gFACjC,WAAU;;kGAEV,8OAAC,0MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CA1FhC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0G1C", "debugId": null}}, {"offset": {"line": 2000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/sf2-report-generator.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { SF2Report, ReportConfig, TeacherInfo } from \"@/lib/types/reports\"\nimport { schoolInfo, mockTeachers } from \"@/lib/data/reports-mock-data\"\nimport { generateSF2Report } from \"@/lib/utils/report-utils\"\nimport { mockStudents, mockAttendanceRecords } from \"@/lib/data/mock-data\"\nimport { \n  FileText, \n  Calendar, \n  User, \n  School, \n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Download,\n  Eye,\n  Printer\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\ninterface SF2ReportGeneratorProps {\n  config: ReportConfig\n  onGenerate: (report: SF2Report) => void\n  onPreview: (report: SF2Report) => void\n  className?: string\n}\n\nexport function SF2ReportGenerator({ \n  config, \n  onGenerate, \n  onPreview, \n  className \n}: SF2ReportGeneratorProps) {\n  const [selectedTeacher, setSelectedTeacher] = useState<TeacherInfo>(mockTeachers[0])\n  const [selectedSubject, setSelectedSubject] = useState(\"\")\n  const [classSchedule, setClassSchedule] = useState({\n    startTime: \"08:00\",\n    endTime: \"09:00\",\n    room: \"Room 101\"\n  })\n  const [reportSettings, setReportSettings] = useState({\n    includeSignatures: true,\n    includeRemarks: true,\n    includeAbsenteeReasons: true,\n    showStatistics: true\n  })\n  const [isGenerating, setIsGenerating] = useState(false)\n  const [generatedReport, setGeneratedReport] = useState<SF2Report | null>(null)\n\n  // Mock subjects for the selected grade/section\n  const subjects = [\n    \"Mathematics\",\n    \"English\",\n    \"Science\", \n    \"Filipino\",\n    \"Social Studies\",\n    \"Physical Education\",\n    \"Technology and Livelihood Education\",\n    \"Arts\",\n    \"Music\"\n  ]\n\n  const handleGenerateReport = async () => {\n    setIsGenerating(true)\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      // Generate the SF2 report\n      const report = generateSF2Report(\n        config,\n        mockStudents,\n        mockAttendanceRecords as any[]\n      )\n      \n      // Add additional SF2-specific data\n      const enhancedReport: SF2Report = {\n        ...report,\n        subject: selectedSubject,\n        teacher: {\n          ...selectedTeacher,\n          dateChecked: format(new Date(), \"yyyy-MM-dd\")\n        }\n      }\n      \n      setGeneratedReport(enhancedReport)\n      onGenerate(enhancedReport)\n    } catch (error) {\n      console.error(\"Error generating SF2 report:\", error)\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  const handlePreviewReport = () => {\n    if (generatedReport) {\n      onPreview(generatedReport)\n    }\n  }\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* SF2 Report Header */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <FileText className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div>\n                <CardTitle>SF2 Daily Attendance Report</CardTitle>\n                <CardDescription>\n                  Official DepEd School Form 2 - Daily Attendance Report of Learners\n                </CardDescription>\n              </div>\n            </div>\n            <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800\">\n              DepEd Official\n            </Badge>\n          </div>\n        </CardHeader>\n      </Card>\n\n      {/* School Information */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <School className=\"h-5 w-5\" />\n            School Information\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <Label className=\"text-sm font-medium\">School Name</Label>\n              <p className=\"text-sm text-muted-foreground\">{schoolInfo.schoolName}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium\">School ID</Label>\n              <p className=\"text-sm text-muted-foreground\">{schoolInfo.schoolId}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium\">Division</Label>\n              <p className=\"text-sm text-muted-foreground\">{schoolInfo.division}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium\">Region</Label>\n              <p className=\"text-sm text-muted-foreground\">{schoolInfo.region}</p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Class Information */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <User className=\"h-5 w-5\" />\n            Class Information\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"teacher-select\">Teacher</Label>\n              <Select \n                value={selectedTeacher.id} \n                onValueChange={(value) => {\n                  const teacher = mockTeachers.find(t => t.id === value)\n                  if (teacher) setSelectedTeacher(teacher)\n                }}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select teacher\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {mockTeachers.map((teacher) => (\n                    <SelectItem key={teacher.id} value={teacher.id}>\n                      {teacher.name} - {teacher.position}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"subject-select\">Subject</Label>\n              <Select value={selectedSubject} onValueChange={setSelectedSubject}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select subject\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {subjects.map((subject) => (\n                    <SelectItem key={subject} value={subject}>\n                      {subject}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>Grade & Section</Label>\n              <p className=\"text-sm text-muted-foreground\">\n                {config.filters.grades?.join(\", \") || \"All Grades\"} - {config.filters.sections?.join(\", \") || \"All Sections\"}\n              </p>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>Date</Label>\n              <p className=\"text-sm text-muted-foreground\">\n                {format(new Date(config.dateRange.startDate), \"MMMM dd, yyyy\")}\n              </p>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Class Schedule */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium\">Class Schedule</Label>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"start-time\" className=\"text-xs\">Start Time</Label>\n                <Input\n                  id=\"start-time\"\n                  type=\"time\"\n                  value={classSchedule.startTime}\n                  onChange={(e) => setClassSchedule({...classSchedule, startTime: e.target.value})}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"end-time\" className=\"text-xs\">End Time</Label>\n                <Input\n                  id=\"end-time\"\n                  type=\"time\"\n                  value={classSchedule.endTime}\n                  onChange={(e) => setClassSchedule({...classSchedule, endTime: e.target.value})}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"room\" className=\"text-xs\">Room</Label>\n                <Input\n                  id=\"room\"\n                  value={classSchedule.room}\n                  onChange={(e) => setClassSchedule({...classSchedule, room: e.target.value})}\n                  placeholder=\"Room number\"\n                />\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Report Settings */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <CheckCircle className=\"h-5 w-5\" />\n            Report Settings\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-signatures\"\n                checked={reportSettings.includeSignatures}\n                onCheckedChange={(checked) => \n                  setReportSettings({...reportSettings, includeSignatures: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"include-signatures\" className=\"text-sm\">\n                Include teacher signature fields\n              </Label>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-remarks\"\n                checked={reportSettings.includeRemarks}\n                onCheckedChange={(checked) => \n                  setReportSettings({...reportSettings, includeRemarks: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"include-remarks\" className=\"text-sm\">\n                Include remarks column\n              </Label>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-reasons\"\n                checked={reportSettings.includeAbsenteeReasons}\n                onCheckedChange={(checked) => \n                  setReportSettings({...reportSettings, includeAbsenteeReasons: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"include-reasons\" className=\"text-sm\">\n                Include absence reason coding\n              </Label>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"show-statistics\"\n                checked={reportSettings.showStatistics}\n                onCheckedChange={(checked) => \n                  setReportSettings({...reportSettings, showStatistics: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"show-statistics\" className=\"text-sm\">\n                Show attendance statistics summary\n              </Label>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Generation Actions */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-1\">\n              <h3 className=\"text-lg font-medium\">Generate SF2 Report</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                Create official DepEd SF2 Daily Attendance Report\n              </p>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              {generatedReport && (\n                <>\n                  <Button variant=\"outline\" onClick={handlePreviewReport}>\n                    <Eye className=\"mr-2 h-4 w-4\" />\n                    Preview\n                  </Button>\n                  <Button variant=\"outline\">\n                    <Download className=\"mr-2 h-4 w-4\" />\n                    Download\n                  </Button>\n                  <Button variant=\"outline\">\n                    <Printer className=\"mr-2 h-4 w-4\" />\n                    Print\n                  </Button>\n                </>\n              )}\n              <Button \n                onClick={handleGenerateReport} \n                disabled={isGenerating || !selectedSubject}\n                size=\"lg\"\n              >\n                {isGenerating ? (\n                  <>\n                    <Clock className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Generating...\n                  </>\n                ) : (\n                  <>\n                    <FileText className=\"mr-2 h-4 w-4\" />\n                    Generate SF2\n                  </>\n                )}\n              </Button>\n            </div>\n          </div>\n\n          {!selectedSubject && (\n            <div className=\"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n              <div className=\"flex items-center gap-2 text-yellow-800\">\n                <AlertCircle className=\"h-4 w-4\" />\n                <span className=\"text-sm\">Please select a subject to generate the SF2 report</span>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AA5BA;;;;;;;;;;;;;;;;AAqCO,SAAS,mBAAmB,EACjC,MAAM,EACN,UAAU,EACV,SAAS,EACT,SAAS,EACe;IACxB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,sIAAA,CAAA,eAAY,CAAC,EAAE;IACnF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,mBAAmB;QACnB,gBAAgB;QAChB,wBAAwB;QACxB,gBAAgB;IAClB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEzE,+CAA+C;IAC/C,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,uBAAuB;QAC3B,gBAAgB;QAEhB,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,0BAA0B;YAC1B,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAC7B,QACA,2HAAA,CAAA,eAAY,EACZ,2HAAA,CAAA,wBAAqB;YAGvB,mCAAmC;YACnC,MAAM,iBAA4B;gBAChC,GAAG,MAAM;gBACT,SAAS;gBACT,SAAS;oBACP,GAAG,eAAe;oBAClB,aAAa,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;gBAClC;YACF;YAEA,mBAAmB;YACnB,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,iBAAiB;YACnB,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,yHAAA,CAAA,aAAU;8BACT,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;;0DACC,8OAAC,yHAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,yHAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;;0CAKrB,8OAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;0BAQvE,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIlC,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,0HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAiC,sIAAA,CAAA,aAAU,CAAC,UAAU;;;;;;;;;;;;8CAErE,8OAAC;;sDACC,8OAAC,0HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAiC,sIAAA,CAAA,aAAU,CAAC,QAAQ;;;;;;;;;;;;8CAEnE,8OAAC;;sDACC,8OAAC,0HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAiC,sIAAA,CAAA,aAAU,CAAC,QAAQ;;;;;;;;;;;;8CAEnE,8OAAC;;sDACC,8OAAC,0HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAiC,sIAAA,CAAA,aAAU,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvE,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIhC,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,8OAAC,2HAAA,CAAA,SAAM;gDACL,OAAO,gBAAgB,EAAE;gDACzB,eAAe,CAAC;oDACd,MAAM,UAAU,sIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oDAChD,IAAI,SAAS,mBAAmB;gDAClC;;kEAEA,8OAAC,2HAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,2HAAA,CAAA,gBAAa;kEACX,sIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,wBACjB,8OAAC,2HAAA,CAAA,aAAU;gEAAkB,OAAO,QAAQ,EAAE;;oEAC3C,QAAQ,IAAI;oEAAC;oEAAI,QAAQ,QAAQ;;+DADnB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;kDAQnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,8OAAC,2HAAA,CAAA,SAAM;gDAAC,OAAO;gDAAiB,eAAe;;kEAC7C,8OAAC,2HAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,2HAAA,CAAA,gBAAa;kEACX,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,2HAAA,CAAA,aAAU;gEAAe,OAAO;0EAC9B;+DADc;;;;;;;;;;;;;;;;;;;;;;kDAQzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC;gDAAE,WAAU;;oDACV,OAAO,OAAO,CAAC,MAAM,EAAE,KAAK,SAAS;oDAAa;oDAAI,OAAO,OAAO,CAAC,QAAQ,EAAE,KAAK,SAAS;;;;;;;;;;;;;kDAIlG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,SAAS,GAAG;;;;;;;;;;;;;;;;;;0CAKpD,8OAAC,8HAAA,CAAA,YAAS;;;;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;wCAAC,WAAU;kDAAsB;;;;;;kDACvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAa,WAAU;kEAAU;;;;;;kEAChD,8OAAC,0HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,cAAc,SAAS;wDAC9B,UAAU,CAAC,IAAM,iBAAiB;gEAAC,GAAG,aAAa;gEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4DAAA;;;;;;;;;;;;0DAGlF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;kEAAU;;;;;;kEAC9C,8OAAC,0HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,cAAc,OAAO;wDAC5B,UAAU,CAAC,IAAM,iBAAiB;gEAAC,GAAG,aAAa;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAA;;;;;;;;;;;;0DAGhF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAO,WAAU;kEAAU;;;;;;kEAC1C,8OAAC,0HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,cAAc,IAAI;wDACzB,UAAU,CAAC,IAAM,iBAAiB;gEAAC,GAAG,aAAa;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACzE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxB,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIvC,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,iBAAiB;4CACzC,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,mBAAmB;gDAAkB;;;;;;sDAG/E,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAqB,WAAU;sDAAU;;;;;;;;;;;;8CAK1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,cAAc;4CACtC,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,gBAAgB;gDAAkB;;;;;;sDAG5E,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAkB,WAAU;sDAAU;;;;;;;;;;;;8CAKvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,sBAAsB;4CAC9C,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,wBAAwB;gDAAkB;;;;;;sDAGpF,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAkB,WAAU;sDAAU;;;;;;;;;;;;8CAKvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,cAAc;4CACtC,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,gBAAgB;gDAAkB;;;;;;sDAG5E,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAkB,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7D,8OAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,8OAAC;oCAAI,WAAU;;wCACZ,iCACC;;8DACE,8OAAC,2HAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;;sEACjC,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGlC,8OAAC,2HAAA,CAAA,SAAM;oDAAC,SAAQ;;sEACd,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,8OAAC,2HAAA,CAAA,SAAM;oDAAC,SAAQ;;sEACd,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;sDAK1C,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,gBAAgB,CAAC;4CAC3B,MAAK;sDAEJ,6BACC;;kEACE,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAA8B;;6EAIjD;;kEACE,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;wBAQ9C,CAAC,iCACA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C", "debugId": null}}, {"offset": {"line": 3015, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/sf2-report-preview.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { SF2Report, ExportFormat } from \"@/lib/types/reports\"\nimport { ReportExportManager } from \"@/lib/utils/export-utils\"\nimport { \n  FileText, \n  Download, \n  Printer, \n  School,\n  Calendar,\n  User,\n  Clock,\n  CheckCircle,\n  XCircle,\n  AlertTriangle\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\ninterface SF2ReportPreviewProps {\n  report: SF2Report\n  onDownload?: (format: ExportFormat) => void\n  onPrint?: () => void\n  className?: string\n}\n\nexport function SF2ReportPreview({\n  report,\n  onDownload,\n  onPrint,\n  className\n}: SF2ReportPreviewProps) {\n  const handleDownload = async (format: ExportFormat) => {\n    try {\n      const { blob, filename } = await ReportExportManager.exportReport(report, format)\n      ReportExportManager.downloadFile(blob, filename)\n      onDownload?.(format)\n    } catch (error) {\n      console.error('Export failed:', error)\n    }\n  }\n  const getAttendanceIcon = (status: string) => {\n    switch (status) {\n      case 'P':\n        return <CheckCircle className=\"h-4 w-4 text-green-600\" />\n      case 'L':\n        return <AlertTriangle className=\"h-4 w-4 text-yellow-600\" />\n      case 'A':\n        return <XCircle className=\"h-4 w-4 text-red-600\" />\n      case 'E':\n        return <CheckCircle className=\"h-4 w-4 text-blue-600\" />\n      default:\n        return <XCircle className=\"h-4 w-4 text-gray-400\" />\n    }\n  }\n\n  const getAttendanceLabel = (status: string) => {\n    switch (status) {\n      case 'P': return 'Present'\n      case 'L': return 'Late'\n      case 'A': return 'Absent'\n      case 'E': return 'Excused'\n      default: return 'Unknown'\n    }\n  }\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Header Actions */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"p-2 bg-blue-100 rounded-lg\">\n            <FileText className=\"h-6 w-6 text-blue-600\" />\n          </div>\n          <div>\n            <h2 className=\"text-xl font-semibold\">SF2 Report Preview</h2>\n            <p className=\"text-sm text-muted-foreground\">\n              Daily Attendance Report - {format(new Date(report.date), \"MMMM dd, yyyy\")}\n            </p>\n          </div>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button variant=\"outline\" onClick={() => handleDownload('EXCEL')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            Excel\n          </Button>\n          <Button variant=\"outline\" onClick={() => handleDownload('PDF')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            PDF\n          </Button>\n          <Button variant=\"outline\" onClick={() => handleDownload('CSV')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            CSV\n          </Button>\n          <Button onClick={onPrint}>\n            <Printer className=\"mr-2 h-4 w-4\" />\n            Print\n          </Button>\n        </div>\n      </div>\n\n      {/* SF2 Form Preview */}\n      <Card className=\"print:shadow-none print:border-none\">\n        <CardContent className=\"p-8 space-y-6\">\n          {/* Official Header */}\n          <div className=\"text-center space-y-2\">\n            <h1 className=\"text-lg font-bold\">Republic of the Philippines</h1>\n            <h2 className=\"text-base font-semibold\">Department of Education</h2>\n            <h3 className=\"text-base font-semibold\">{report.schoolInfo.region}</h3>\n            <h4 className=\"text-base font-semibold\">{report.schoolInfo.division}</h4>\n            <h5 className=\"text-base font-semibold\">{report.schoolInfo.schoolName}</h5>\n            <div className=\"pt-4\">\n              <h2 className=\"text-xl font-bold\">SCHOOL FORM 2 (SF2)</h2>\n              <h3 className=\"text-lg font-semibold\">DAILY ATTENDANCE REPORT OF LEARNERS</h3>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* School and Class Information */}\n          <div className=\"grid grid-cols-2 gap-8\">\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">School:</span>\n                <span className=\"underline\">{report.schoolInfo.schoolName}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">School ID:</span>\n                <span className=\"underline\">{report.schoolInfo.schoolId}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Division:</span>\n                <span className=\"underline\">{report.schoolInfo.division}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Region:</span>\n                <span className=\"underline\">{report.schoolInfo.region}</span>\n              </div>\n            </div>\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Grade & Section:</span>\n                <span className=\"underline\">Grade {report.grade} - {report.section}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Subject:</span>\n                <span className=\"underline\">{report.subject || 'All Subjects'}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Teacher:</span>\n                <span className=\"underline\">{report.teacher.name}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Date:</span>\n                <span className=\"underline\">{format(new Date(report.date), \"MMMM dd, yyyy\")}</span>\n              </div>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Attendance Table */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-center\">DAILY ATTENDANCE</h3>\n            \n            <div className=\"border rounded-lg overflow-hidden\">\n              <Table>\n                <TableHeader>\n                  <TableRow className=\"bg-gray-50\">\n                    <TableHead className=\"w-12 text-center border-r\">No.</TableHead>\n                    <TableHead className=\"border-r\">LEARNER'S NAME</TableHead>\n                    <TableHead className=\"w-24 text-center border-r\">ATTENDANCE</TableHead>\n                    <TableHead className=\"w-32 text-center border-r\">STATUS</TableHead>\n                    <TableHead className=\"text-center\">REMARKS</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {report.students.map((student, index) => (\n                    <TableRow key={student.studentId} className=\"border-b\">\n                      <TableCell className=\"text-center border-r font-medium\">\n                        {index + 1}\n                      </TableCell>\n                      <TableCell className=\"border-r\">\n                        {student.studentName}\n                      </TableCell>\n                      <TableCell className=\"text-center border-r\">\n                        <div className=\"flex items-center justify-center\">\n                          {getAttendanceIcon(student.dailyStatus)}\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"text-center border-r\">\n                        <Badge \n                          variant={\n                            student.dailyStatus === 'P' ? 'default' :\n                            student.dailyStatus === 'L' ? 'secondary' :\n                            student.dailyStatus === 'E' ? 'outline' : 'destructive'\n                          }\n                          className=\"text-xs\"\n                        >\n                          {getAttendanceLabel(student.dailyStatus)}\n                        </Badge>\n                      </TableCell>\n                      <TableCell className=\"text-center text-sm\">\n                        {student.remarks || '-'}\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Summary Statistics */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-center\">ATTENDANCE SUMMARY</h3>\n            \n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-blue-600\">{report.summary.totalStudents}</div>\n                <div className=\"text-sm text-muted-foreground\">Total Students</div>\n              </div>\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-green-600\">{report.summary.presentCount}</div>\n                <div className=\"text-sm text-muted-foreground\">Present</div>\n              </div>\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-yellow-600\">{report.summary.lateCount}</div>\n                <div className=\"text-sm text-muted-foreground\">Late</div>\n              </div>\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-red-600\">{report.summary.absentCount}</div>\n                <div className=\"text-sm text-muted-foreground\">Absent</div>\n              </div>\n            </div>\n\n            <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n              <div className=\"text-xl font-bold text-blue-800\">\n                {report.summary.attendanceRate.toFixed(1)}%\n              </div>\n              <div className=\"text-sm text-blue-600\">Overall Attendance Rate</div>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Signature Section */}\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-2 gap-8\">\n              <div className=\"space-y-4\">\n                <div className=\"text-center\">\n                  <div className=\"border-b border-black w-48 mx-auto mb-2 h-12\"></div>\n                  <div className=\"text-sm font-medium\">Teacher's Signature</div>\n                  <div className=\"text-xs text-muted-foreground\">{report.teacher.name}</div>\n                </div>\n              </div>\n              <div className=\"space-y-4\">\n                <div className=\"text-center\">\n                  <div className=\"border-b border-black w-48 mx-auto mb-2 h-12\"></div>\n                  <div className=\"text-sm font-medium\">Date</div>\n                  <div className=\"text-xs text-muted-foreground\">\n                    {report.teacher.dateChecked ? format(new Date(report.teacher.dateChecked), \"MMMM dd, yyyy\") : ''}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"border-b border-black w-64 mx-auto mb-2 h-12\"></div>\n              <div className=\"text-sm font-medium\">Principal's Signature</div>\n              <div className=\"text-xs text-muted-foreground\">{report.schoolInfo.principalName}</div>\n            </div>\n          </div>\n\n          {/* Footer */}\n          <div className=\"text-center text-xs text-muted-foreground pt-4 border-t\">\n            <p>SF2 - Daily Attendance Report of Learners</p>\n            <p>Generated on {format(new Date(report.generatedAt), \"MMMM dd, yyyy 'at' HH:mm\")}</p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AArBA;;;;;;;;;;AA8BO,SAAS,iBAAiB,EAC/B,MAAM,EACN,UAAU,EACV,OAAO,EACP,SAAS,EACa;IACtB,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,MAAM,+HAAA,CAAA,sBAAmB,CAAC,YAAY,CAAC,QAAQ;YAC1E,+HAAA,CAAA,sBAAmB,CAAC,YAAY,CAAC,MAAM;YACvC,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IACA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QAC9B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAE,WAAU;;4CAAgC;4CAChB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,IAAI,GAAG;;;;;;;;;;;;;;;;;;;kCAI/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoB;;;;;;8CAClC,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAG,WAAU;8CAA2B,OAAO,UAAU,CAAC,MAAM;;;;;;8CACjE,8OAAC;oCAAG,WAAU;8CAA2B,OAAO,UAAU,CAAC,QAAQ;;;;;;8CACnE,8OAAC;oCAAG,WAAU;8CAA2B,OAAO,UAAU,CAAC,UAAU;;;;;;8CACrE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoB;;;;;;sDAClC,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAI1C,8OAAC,8HAAA,CAAA,YAAS;;;;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,UAAU;;;;;;;;;;;;sDAE3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,QAAQ;;;;;;;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,QAAQ;;;;;;;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,MAAM;;;;;;;;;;;;;;;;;;8CAGzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;;wDAAY;wDAAO,OAAO,KAAK;wDAAC;wDAAI,OAAO,OAAO;;;;;;;;;;;;;sDAEpE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,OAAO,IAAI;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,OAAO,CAAC,IAAI;;;;;;;;;;;;sDAElD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,IAAI,GAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAKjE,8OAAC,8HAAA,CAAA,YAAS;;;;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;0DACJ,8OAAC,0HAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;oDAAC,WAAU;;sEAClB,8OAAC,0HAAA,CAAA,YAAS;4DAAC,WAAU;sEAA4B;;;;;;sEACjD,8OAAC,0HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAW;;;;;;sEAChC,8OAAC,0HAAA,CAAA,YAAS;4DAAC,WAAU;sEAA4B;;;;;;sEACjD,8OAAC,0HAAA,CAAA,YAAS;4DAAC,WAAU;sEAA4B;;;;;;sEACjD,8OAAC,0HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAc;;;;;;;;;;;;;;;;;0DAGvC,8OAAC,0HAAA,CAAA,YAAS;0DACP,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC,0HAAA,CAAA,WAAQ;wDAAyB,WAAU;;0EAC1C,8OAAC,0HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,QAAQ;;;;;;0EAEX,8OAAC,0HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,QAAQ,WAAW;;;;;;0EAEtB,8OAAC,0HAAA,CAAA,YAAS;gEAAC,WAAU;0EACnB,cAAA,8OAAC;oEAAI,WAAU;8EACZ,kBAAkB,QAAQ,WAAW;;;;;;;;;;;0EAG1C,8OAAC,0HAAA,CAAA,YAAS;gEAAC,WAAU;0EACnB,cAAA,8OAAC,0HAAA,CAAA,QAAK;oEACJ,SACE,QAAQ,WAAW,KAAK,MAAM,YAC9B,QAAQ,WAAW,KAAK,MAAM,cAC9B,QAAQ,WAAW,KAAK,MAAM,YAAY;oEAE5C,WAAU;8EAET,mBAAmB,QAAQ,WAAW;;;;;;;;;;;0EAG3C,8OAAC,0HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,QAAQ,OAAO,IAAI;;;;;;;uDAzBT,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAkC1C,8OAAC,8HAAA,CAAA,YAAS;;;;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC,OAAO,OAAO,CAAC,aAAa;;;;;;8DAC/E,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqC,OAAO,OAAO,CAAC,YAAY;;;;;;8DAC/E,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAsC,OAAO,OAAO,CAAC,SAAS;;;;;;8DAC7E,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAmC,OAAO,OAAO,CAAC,WAAW;;;;;;8DAC5E,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAInD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,OAAO,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC;gDAAG;;;;;;;sDAE5C,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAI3C,8OAAC,8HAAA,CAAA,YAAS;;;;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEAAsB;;;;;;kEACrC,8OAAC;wDAAI,WAAU;kEAAiC,OAAO,OAAO,CAAC,IAAI;;;;;;;;;;;;;;;;;sDAGvE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEAAsB;;;;;;kEACrC,8OAAC;wDAAI,WAAU;kEACZ,OAAO,OAAO,CAAC,WAAW,GAAG,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,OAAO,CAAC,WAAW,GAAG,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;8CAMtG,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;sDAAsB;;;;;;sDACrC,8OAAC;4CAAI,WAAU;sDAAiC,OAAO,UAAU,CAAC,aAAa;;;;;;;;;;;;;;;;;;sCAKnF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;;wCAAE;wCAAc,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlE", "debugId": null}}, {"offset": {"line": 4070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/sf2-report-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON>alogHeader, DialogTitle } from \"@/components/ui/dialog\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from \"@/components/ui/tabs\"\nimport { SF2ReportGenerator } from \"./sf2-report-generator\"\nimport { SF2ReportPreview } from \"./sf2-report-preview\"\nimport { ReportConfig, SF2Report, ExportFormat } from \"@/lib/types/reports\"\nimport { ArrowLeft, FileText, Eye } from \"lucide-react\"\nimport { toast } from \"sonner\"\n\ninterface SF2ReportDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  config: ReportConfig\n}\n\nexport function SF2ReportDialog({ open, onOpenChange, config }: SF2ReportDialogProps) {\n  const [activeTab, setActiveTab] = useState(\"generate\")\n  const [generatedReport, setGeneratedReport] = useState<SF2Report | null>(null)\n\n  const handleGenerateReport = (report: SF2Report) => {\n    setGeneratedReport(report)\n    setActiveTab(\"preview\")\n    toast.success(\"SF2 report generated successfully\", {\n      description: \"You can now preview, download, or print the report\"\n    })\n  }\n\n  const handlePreviewReport = (report: SF2Report) => {\n    setGeneratedReport(report)\n    setActiveTab(\"preview\")\n  }\n\n  const handleDownload = (format: ExportFormat) => {\n    toast.success(`Downloading SF2 report in ${format} format`, {\n      description: \"The download will start shortly\"\n    })\n  }\n\n  const handlePrint = () => {\n    window.print()\n    toast.success(\"Print dialog opened\", {\n      description: \"Please select your printer and print settings\"\n    })\n  }\n\n  const handleBackToGenerate = () => {\n    setActiveTab(\"generate\")\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-6xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            SF2 Daily Attendance Report Generator\n          </DialogTitle>\n          <DialogDescription>\n            Generate official DepEd School Form 2 - Daily Attendance Report of Learners\n          </DialogDescription>\n        </DialogHeader>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-2\">\n            <TabsTrigger value=\"generate\" className=\"flex items-center gap-2\">\n              <FileText className=\"h-4 w-4\" />\n              Generate Report\n            </TabsTrigger>\n            <TabsTrigger \n              value=\"preview\" \n              disabled={!generatedReport}\n              className=\"flex items-center gap-2\"\n            >\n              <Eye className=\"h-4 w-4\" />\n              Preview Report\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"generate\" className=\"mt-6\">\n            <SF2ReportGenerator\n              config={config}\n              onGenerate={handleGenerateReport}\n              onPreview={handlePreviewReport}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"preview\" className=\"mt-6\">\n            {generatedReport ? (\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <Button \n                    variant=\"outline\" \n                    onClick={handleBackToGenerate}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4\" />\n                    Back to Generator\n                  </Button>\n                </div>\n                \n                <SF2ReportPreview\n                  report={generatedReport}\n                  onDownload={handleDownload}\n                  onPrint={handlePrint}\n                />\n              </div>\n            ) : (\n              <div className=\"text-center py-8 text-muted-foreground\">\n                <FileText className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                <p>No report generated yet</p>\n                <p className=\"text-sm\">Generate a report first to see the preview</p>\n              </div>\n            )}\n          </TabsContent>\n        </Tabs>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AAVA;;;;;;;;;;AAkBO,SAAS,gBAAgB,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAwB;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEzE,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;QACnB,aAAa;QACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qCAAqC;YACjD,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,iBAAiB,CAAC;QACtB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,0BAA0B,EAAE,OAAO,OAAO,CAAC,EAAE;YAC1D,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,KAAK;QACZ,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;YACnC,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB;QAC3B,aAAa;IACf;IAEA,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,2HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,2HAAA,CAAA,eAAY;;sCACX,8OAAC,2HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,8OAAC,2HAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC,yHAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,8OAAC,yHAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;;sDACtC,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,8OAAC,yHAAA,CAAA,cAAW;oCACV,OAAM;oCACN,UAAU,CAAC;oCACX,WAAU;;sDAEV,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAK/B,8OAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC,oJAAA,CAAA,qBAAkB;gCACjB,QAAQ;gCACR,YAAY;gCACZ,WAAW;;;;;;;;;;;sCAIf,8OAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACpC,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;;8DAEV,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAKrC,8OAAC,kJAAA,CAAA,mBAAgB;wCACf,QAAQ;wCACR,YAAY;wCACZ,SAAS;;;;;;;;;;;qDAIb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC", "debugId": null}}, {"offset": {"line": 4332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/sf4-report-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON>alogHeader, DialogTitle } from \"@/components/ui/dialog\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from \"@/components/ui/tabs\"\nimport { SF4ReportGenerator } from \"./sf4-report-generator\"\nimport { SF4ReportPreview } from \"./sf4-report-preview\"\nimport { ReportConfig, SF4Report, ExportFormat } from \"@/lib/types/reports\"\nimport { ArrowLeft, TrendingUp, Eye } from \"lucide-react\"\nimport { toast } from \"sonner\"\n\ninterface SF4ReportDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  config: ReportConfig\n}\n\nexport function SF4ReportDialog({ open, onOpenChange, config }: SF4ReportDialogProps) {\n  const [activeTab, setActiveTab] = useState(\"generate\")\n  const [generatedReport, setGeneratedReport] = useState<SF4Report | null>(null)\n\n  const handleGenerateReport = (report: SF4Report) => {\n    setGeneratedReport(report)\n    setActiveTab(\"preview\")\n    toast.success(\"SF4 report generated successfully\", {\n      description: \"You can now preview, download, or print the report\"\n    })\n  }\n\n  const handlePreviewReport = (report: SF4Report) => {\n    setGeneratedReport(report)\n    setActiveTab(\"preview\")\n  }\n\n  const handleDownload = (format: ExportFormat) => {\n    toast.success(`Downloading SF4 report in ${format} format`, {\n      description: \"The download will start shortly\"\n    })\n  }\n\n  const handlePrint = () => {\n    window.print()\n    toast.success(\"Print dialog opened\", {\n      description: \"Please select your printer and print settings\"\n    })\n  }\n\n  const handleBackToGenerate = () => {\n    setActiveTab(\"generate\")\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-6xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <TrendingUp className=\"h-5 w-5\" />\n            SF4 Monthly Learner's Movement Generator\n          </DialogTitle>\n          <DialogDescription>\n            Generate official DepEd School Form 4 - Monthly Report on Learner's Movement\n          </DialogDescription>\n        </DialogHeader>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-2\">\n            <TabsTrigger value=\"generate\" className=\"flex items-center gap-2\">\n              <TrendingUp className=\"h-4 w-4\" />\n              Generate Report\n            </TabsTrigger>\n            <TabsTrigger \n              value=\"preview\" \n              disabled={!generatedReport}\n              className=\"flex items-center gap-2\"\n            >\n              <Eye className=\"h-4 w-4\" />\n              Preview Report\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"generate\" className=\"mt-6\">\n            <SF4ReportGenerator\n              config={config}\n              onGenerate={handleGenerateReport}\n              onPreview={handlePreviewReport}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"preview\" className=\"mt-6\">\n            {generatedReport ? (\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <Button \n                    variant=\"outline\" \n                    onClick={handleBackToGenerate}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4\" />\n                    Back to Generator\n                  </Button>\n                </div>\n                \n                <SF4ReportPreview\n                  report={generatedReport}\n                  onDownload={handleDownload}\n                  onPrint={handlePrint}\n                />\n              </div>\n            ) : (\n              <div className=\"text-center py-8 text-muted-foreground\">\n                <TrendingUp className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                <p>No report generated yet</p>\n                <p className=\"text-sm\">Generate a report first to see the preview</p>\n              </div>\n            )}\n          </TabsContent>\n        </Tabs>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AAVA;;;;;;;;;;AAkBO,SAAS,gBAAgB,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAwB;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEzE,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;QACnB,aAAa;QACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qCAAqC;YACjD,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,iBAAiB,CAAC;QACtB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,0BAA0B,EAAE,OAAO,OAAO,CAAC,EAAE;YAC1D,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,KAAK;QACZ,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;YACnC,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB;QAC3B,aAAa;IACf;IAEA,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,2HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,2HAAA,CAAA,eAAY;;sCACX,8OAAC,2HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGpC,8OAAC,2HAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC,yHAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,8OAAC,yHAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;;sDACtC,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGpC,8OAAC,yHAAA,CAAA,cAAW;oCACV,OAAM;oCACN,UAAU,CAAC;oCACX,WAAU;;sDAEV,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAK/B,8OAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC,oJAAA,CAAA,qBAAkB;gCACjB,QAAQ;gCACR,YAAY;gCACZ,WAAW;;;;;;;;;;;sCAIf,8OAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACpC,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;;8DAEV,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAKrC,8OAAC,kJAAA,CAAA,mBAAgB;wCACf,QAAQ;wCACR,YAAY;wCACZ,SAAS;;;;;;;;;;;qDAIb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC", "debugId": null}}, {"offset": {"line": 4594, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/custom-report-preview.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { CustomReport, ExportFormat } from \"@/lib/types/reports\"\nimport { ReportExportManager } from \"@/lib/utils/export-utils\"\nimport { \n  Users, \n  Download, \n  Printer, \n  BarChart3,\n  Table as TableIcon,\n  PieChart,\n  TrendingUp,\n  FileText\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\ninterface CustomReportPreviewProps {\n  report: CustomReport\n  onDownload?: (format: ExportFormat) => void\n  onPrint?: () => void\n  className?: string\n}\n\n// Mock data for preview\nconst mockData = [\n  {\n    \"students.id\": \"STU001\",\n    \"students.name\": \"<PERSON>\",\n    \"students.grade\": \"7\",\n    \"students.section\": \"A\",\n    \"attendance.status\": \"Present\",\n    \"attendance.date\": \"2025-01-02\"\n  },\n  {\n    \"students.id\": \"STU002\", \n    \"students.name\": \"<PERSON>\",\n    \"students.grade\": \"7\",\n    \"students.section\": \"B\",\n    \"attendance.status\": \"Late\",\n    \"attendance.date\": \"2025-01-02\"\n  },\n  {\n    \"students.id\": \"STU003\",\n    \"students.name\": \"Ana Marie Reyes\", \n    \"students.grade\": \"8\",\n    \"students.section\": \"A\",\n    \"attendance.status\": \"Present\",\n    \"attendance.date\": \"2025-01-02\"\n  },\n  {\n    \"students.id\": \"STU004\",\n    \"students.name\": \"Jose Miguel Rodriguez\",\n    \"students.grade\": \"8\", \n    \"students.section\": \"B\",\n    \"attendance.status\": \"Absent\",\n    \"attendance.date\": \"2025-01-02\"\n  },\n  {\n    \"students.id\": \"STU005\",\n    \"students.name\": \"Princess Mae Garcia\",\n    \"students.grade\": \"9\",\n    \"students.section\": \"A\", \n    \"attendance.status\": \"Present\",\n    \"attendance.date\": \"2025-01-02\"\n  }\n]\n\nexport function CustomReportPreview({\n  report,\n  onDownload,\n  onPrint,\n  className\n}: CustomReportPreviewProps) {\n  const handleDownload = async (format: ExportFormat) => {\n    try {\n      const { blob, filename } = await ReportExportManager.exportReport(report, format)\n      ReportExportManager.downloadFile(blob, filename)\n      onDownload?.(format)\n    } catch (error) {\n      console.error('Export failed:', error)\n    }\n  }\n  // Use mock data for preview, in real implementation this would come from report.data\n  const data = report.data.length > 0 ? report.data : mockData.slice(0, Math.min(5, report.query.limit || 5))\n\n  const getVisualizationIcon = () => {\n    if (report.visualization?.type === 'chart') {\n      switch (report.visualization.chartType) {\n        case 'bar': return <BarChart3 className=\"h-6 w-6 text-blue-600\" />\n        case 'pie': return <PieChart className=\"h-6 w-6 text-green-600\" />\n        case 'line': return <TrendingUp className=\"h-6 w-6 text-purple-600\" />\n        default: return <BarChart3 className=\"h-6 w-6 text-blue-600\" />\n      }\n    }\n    return <TableIcon className=\"h-6 w-6 text-gray-600\" />\n  }\n\n  const formatFieldName = (fieldId: string) => {\n    const parts = fieldId.split('.')\n    if (parts.length === 2) {\n      const [source, field] = parts\n      return field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1')\n    }\n    return fieldId\n  }\n\n  const formatCellValue = (value: any, fieldId: string) => {\n    if (value === null || value === undefined) return '-'\n    \n    // Format dates\n    if (fieldId.includes('date') || fieldId.includes('Date')) {\n      try {\n        return format(new Date(value), \"MMM dd, yyyy\")\n      } catch {\n        return value\n      }\n    }\n    \n    // Format status with badges\n    if (fieldId.includes('status') || fieldId.includes('Status')) {\n      const variant = value === 'Present' ? 'default' : \n                    value === 'Late' ? 'secondary' : \n                    value === 'Absent' ? 'destructive' : 'outline'\n      return <Badge variant={variant as any}>{value}</Badge>\n    }\n    \n    return value\n  }\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Header Actions */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"p-2 bg-gray-100 rounded-lg\">\n            {getVisualizationIcon()}\n          </div>\n          <div>\n            <h2 className=\"text-xl font-semibold\">{report.name || \"Custom Report\"}</h2>\n            <p className=\"text-sm text-muted-foreground\">\n              {report.description || \"Custom report preview\"}\n            </p>\n          </div>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button variant=\"outline\" onClick={() => handleDownload('CSV')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            CSV\n          </Button>\n          <Button variant=\"outline\" onClick={() => handleDownload('EXCEL')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            Excel\n          </Button>\n          <Button variant=\"outline\" onClick={() => handleDownload('PDF')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            PDF\n          </Button>\n          <Button onClick={onPrint}>\n            <Printer className=\"mr-2 h-4 w-4\" />\n            Print\n          </Button>\n        </div>\n      </div>\n\n      {/* Report Information */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            Report Information\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            <div>\n              <div className=\"text-sm font-medium text-muted-foreground\">Data Sources</div>\n              <div className=\"text-lg font-semibold\">{report.query.tables.length}</div>\n              <div className=\"text-xs text-muted-foreground\">\n                {report.query.tables.join(', ')}\n              </div>\n            </div>\n            <div>\n              <div className=\"text-sm font-medium text-muted-foreground\">Fields</div>\n              <div className=\"text-lg font-semibold\">{report.query.fields.length}</div>\n              <div className=\"text-xs text-muted-foreground\">Selected columns</div>\n            </div>\n            <div>\n              <div className=\"text-sm font-medium text-muted-foreground\">Filters</div>\n              <div className=\"text-lg font-semibold\">{report.query.conditions.length}</div>\n              <div className=\"text-xs text-muted-foreground\">Applied conditions</div>\n            </div>\n            <div>\n              <div className=\"text-sm font-medium text-muted-foreground\">Records</div>\n              <div className=\"text-lg font-semibold\">{data.length}</div>\n              <div className=\"text-xs text-muted-foreground\">\n                {data.length < (report.query.limit || 100) ? 'All records' : `Limited to ${report.query.limit}`}\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Query Details */}\n      {(report.query.conditions.length > 0 || report.query.groupBy || report.query.orderBy) && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Query Details</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            {report.query.conditions.length > 0 && (\n              <div>\n                <h4 className=\"text-sm font-medium mb-2\">Applied Filters:</h4>\n                <div className=\"space-y-1\">\n                  {report.query.conditions.map((condition, index) => (\n                    <div key={index} className=\"text-sm text-muted-foreground\">\n                      • {formatFieldName(condition.field)} {condition.operator.replace('_', ' ')} \"{condition.value}\"\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n            \n            {report.query.groupBy && report.query.groupBy.length > 0 && (\n              <div>\n                <h4 className=\"text-sm font-medium mb-2\">Grouped By:</h4>\n                <div className=\"text-sm text-muted-foreground\">\n                  {report.query.groupBy.map(formatFieldName).join(', ')}\n                </div>\n              </div>\n            )}\n            \n            {report.query.orderBy && report.query.orderBy.length > 0 && (\n              <div>\n                <h4 className=\"text-sm font-medium mb-2\">Ordered By:</h4>\n                <div className=\"text-sm text-muted-foreground\">\n                  {report.query.orderBy.map(formatFieldName).join(', ')}\n                </div>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Data Preview */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Data Preview</CardTitle>\n          <div className=\"text-sm text-muted-foreground\">\n            Showing {data.length} of {data.length} records\n            {data.length >= (report.query.limit || 100) && \" (limited)\"}\n          </div>\n        </CardHeader>\n        <CardContent>\n          {data.length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              <TableIcon className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n              <p>No data available</p>\n              <p className=\"text-sm\">Check your filters and data sources</p>\n            </div>\n          ) : (\n            <div className=\"rounded-md border overflow-x-auto\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    {report.query.fields.map((fieldId) => (\n                      <TableHead key={fieldId}>\n                        {formatFieldName(fieldId)}\n                      </TableHead>\n                    ))}\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {data.map((row, index) => (\n                    <TableRow key={index}>\n                      {report.query.fields.map((fieldId) => (\n                        <TableCell key={fieldId}>\n                          {formatCellValue(row[fieldId], fieldId)}\n                        </TableCell>\n                      ))}\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Visualization Preview */}\n      {report.visualization?.type === 'chart' && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Visualization Preview</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-center py-12 text-muted-foreground\">\n              {getVisualizationIcon()}\n              <p className=\"mt-4\">Chart visualization preview</p>\n              <p className=\"text-sm\">\n                {report.visualization.chartType?.toUpperCase()} chart with {report.visualization.xAxis} vs {report.visualization.yAxis}\n              </p>\n              <div className=\"mt-4 text-xs text-muted-foreground\">\n                Chart rendering would be implemented with a charting library like Chart.js or Recharts\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Footer */}\n      <div className=\"text-center text-xs text-muted-foreground pt-4 border-t\">\n        <p>Custom Report - Generated on {format(new Date(report.generatedAt), \"MMMM dd, yyyy 'at' HH:mm\")}</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAlBA;;;;;;;;;AA2BA,wBAAwB;AACxB,MAAM,WAAW;IACf;QACE,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,mBAAmB;IACrB;IACA;QACE,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,mBAAmB;IACrB;IACA;QACE,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,mBAAmB;IACrB;IACA;QACE,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,mBAAmB;IACrB;IACA;QACE,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,mBAAmB;IACrB;CACD;AAEM,SAAS,oBAAoB,EAClC,MAAM,EACN,UAAU,EACV,OAAO,EACP,SAAS,EACgB;IACzB,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,MAAM,+HAAA,CAAA,sBAAmB,CAAC,YAAY,CAAC,QAAQ;YAC1E,+HAAA,CAAA,sBAAmB,CAAC,YAAY,CAAC,MAAM;YACvC,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IACA,qFAAqF;IACrF,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,IAAI,GAAG,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,CAAC,KAAK,IAAI;IAExG,MAAM,uBAAuB;QAC3B,IAAI,OAAO,aAAa,EAAE,SAAS,SAAS;YAC1C,OAAQ,OAAO,aAAa,CAAC,SAAS;gBACpC,KAAK;oBAAO,qBAAO,8OAAC,kNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;gBACxC,KAAK;oBAAO,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;gBACvC,KAAK;oBAAQ,qBAAO,8OAAC,kNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;gBAC1C;oBAAS,qBAAO,8OAAC,kNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;YACvC;QACF;QACA,qBAAO,8OAAC,oMAAA,CAAA,QAAS;YAAC,WAAU;;;;;;IAC9B;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAC5B,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,MAAM,CAAC,QAAQ,MAAM,GAAG;YACxB,OAAO,MAAM,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,KAAK,CAAC,GAAG,OAAO,CAAC,YAAY;QAC5E;QACA,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC,OAAY;QACnC,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAElD,eAAe;QACf,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,SAAS;YACxD,IAAI;gBACF,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ;YACjC,EAAE,OAAM;gBACN,OAAO;YACT;QACF;QAEA,4BAA4B;QAC5B,IAAI,QAAQ,QAAQ,CAAC,aAAa,QAAQ,QAAQ,CAAC,WAAW;YAC5D,MAAM,UAAU,UAAU,YAAY,YACxB,UAAU,SAAS,cACnB,UAAU,WAAW,gBAAgB;YACnD,qBAAO,8OAAC,0HAAA,CAAA,QAAK;gBAAC,SAAS;0BAAiB;;;;;;QAC1C;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAEH,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyB,OAAO,IAAI,IAAI;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDACV,OAAO,WAAW,IAAI;;;;;;;;;;;;;;;;;;kCAI7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDAAyB,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM;;;;;;sDAClE,8OAAC;4CAAI,WAAU;sDACZ,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;;;;;;;;;;;;8CAG9B,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDAAyB,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM;;;;;;sDAClE,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDAAyB,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM;;;;;;sDACtE,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDAAyB,KAAK,MAAM;;;;;;sDACnD,8OAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM,GAAG,CAAC,OAAO,KAAK,CAAC,KAAK,IAAI,GAAG,IAAI,gBAAgB,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQxG,CAAC,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,KAAK,OAAO,KAAK,CAAC,OAAO,IAAI,OAAO,KAAK,CAAC,OAAO,mBAClF,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,mBAChC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDACZ,OAAO,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACvC,8OAAC;gDAAgB,WAAU;;oDAAgC;oDACtD,gBAAgB,UAAU,KAAK;oDAAE;oDAAE,UAAU,QAAQ,CAAC,OAAO,CAAC,KAAK;oDAAK;oDAAG,UAAU,KAAK;oDAAC;;+CADtF;;;;;;;;;;;;;;;;4BAQjB,OAAO,KAAK,CAAC,OAAO,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,mBACrD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDACZ,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC;;;;;;;;;;;;4BAKrD,OAAO,KAAK,CAAC,OAAO,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,mBACrD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDACZ,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAS5D,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;;0CACT,8OAAC,yHAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC;gCAAI,WAAU;;oCAAgC;oCACpC,KAAK,MAAM;oCAAC;oCAAK,KAAK,MAAM;oCAAC;oCACrC,KAAK,MAAM,IAAI,CAAC,OAAO,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK;;;;;;;;;;;;;kCAGnD,8OAAC,yHAAA,CAAA,cAAW;kCACT,KAAK,MAAM,KAAK,kBACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;iDAGzB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;kDACJ,8OAAC,0HAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;sDACN,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,wBACxB,8OAAC,0HAAA,CAAA,YAAS;8DACP,gBAAgB;mDADH;;;;;;;;;;;;;;;kDAMtB,8OAAC,0HAAA,CAAA,YAAS;kDACP,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC,0HAAA,CAAA,WAAQ;0DACN,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,wBACxB,8OAAC,0HAAA,CAAA,YAAS;kEACP,gBAAgB,GAAG,CAAC,QAAQ,EAAE;uDADjB;;;;;+CAFL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAgB5B,OAAO,aAAa,EAAE,SAAS,yBAC9B,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;gCACZ;8CACD,8OAAC;oCAAE,WAAU;8CAAO;;;;;;8CACpB,8OAAC;oCAAE,WAAU;;wCACV,OAAO,aAAa,CAAC,SAAS,EAAE;wCAAc;wCAAa,OAAO,aAAa,CAAC,KAAK;wCAAC;wCAAK,OAAO,aAAa,CAAC,KAAK;;;;;;;8CAExH,8OAAC;oCAAI,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;0BAS5D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;wBAAE;wBAA8B,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,WAAW,GAAG;;;;;;;;;;;;;;;;;;AAI9E", "debugId": null}}, {"offset": {"line": 5398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/custom-report-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from \"@/components/ui/dialog\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from \"@/components/ui/tabs\"\nimport { CustomReportBuilder } from \"./custom-report-builder\"\nimport { CustomReportPreview } from \"./custom-report-preview\"\nimport { CustomReport, ExportFormat } from \"@/lib/types/reports\"\nimport { ArrowLeft, Users, Eye, Save } from \"lucide-react\"\nimport { toast } from \"sonner\"\n\ninterface CustomReportDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n}\n\nexport function CustomReportDialog({ open, onOpenChange }: CustomReportDialogProps) {\n  const [activeTab, setActiveTab] = useState(\"builder\")\n  const [currentReport, setCurrentReport] = useState<CustomReport | null>(null)\n  const [savedReports, setSavedReports] = useState<CustomReport[]>([])\n\n  const handleGenerateReport = (report: CustomReport) => {\n    setCurrentReport(report)\n    setActiveTab(\"preview\")\n    toast.success(\"Custom report generated successfully\", {\n      description: \"You can now preview, download, or print the report\"\n    })\n  }\n\n  const handlePreviewReport = (report: CustomReport) => {\n    setCurrentReport(report)\n    setActiveTab(\"preview\")\n  }\n\n  const handleSaveReport = (report: CustomReport) => {\n    setSavedReports([...savedReports, report])\n    toast.success(\"Report template saved\", {\n      description: \"You can reuse this template for future reports\"\n    })\n  }\n\n  const handleDownload = (format: ExportFormat) => {\n    toast.success(`Downloading custom report in ${format} format`, {\n      description: \"The download will start shortly\"\n    })\n  }\n\n  const handlePrint = () => {\n    window.print()\n    toast.success(\"Print dialog opened\", {\n      description: \"Please select your printer and print settings\"\n    })\n  }\n\n  const handleBackToBuilder = () => {\n    setActiveTab(\"builder\")\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-7xl max-h-[95vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Users className=\"h-5 w-5\" />\n            Custom Report Builder\n          </DialogTitle>\n          <DialogDescription>\n            Create flexible reports with custom data sources, filters, and visualizations\n          </DialogDescription>\n        </DialogHeader>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-3\">\n            <TabsTrigger value=\"builder\" className=\"flex items-center gap-2\">\n              <Users className=\"h-4 w-4\" />\n              Report Builder\n            </TabsTrigger>\n            <TabsTrigger \n              value=\"preview\" \n              disabled={!currentReport}\n              className=\"flex items-center gap-2\"\n            >\n              <Eye className=\"h-4 w-4\" />\n              Preview Report\n            </TabsTrigger>\n            <TabsTrigger value=\"templates\" className=\"flex items-center gap-2\">\n              <Save className=\"h-4 w-4\" />\n              Saved Templates\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"builder\" className=\"mt-6\">\n            <CustomReportBuilder\n              onGenerate={handleGenerateReport}\n              onPreview={handlePreviewReport}\n              onSave={handleSaveReport}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"preview\" className=\"mt-6\">\n            {currentReport ? (\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <Button \n                    variant=\"outline\" \n                    onClick={handleBackToBuilder}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4\" />\n                    Back to Builder\n                  </Button>\n                </div>\n                \n                <CustomReportPreview\n                  report={currentReport}\n                  onDownload={handleDownload}\n                  onPrint={handlePrint}\n                />\n              </div>\n            ) : (\n              <div className=\"text-center py-8 text-muted-foreground\">\n                <Users className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                <p>No report generated yet</p>\n                <p className=\"text-sm\">Use the report builder to create a custom report</p>\n              </div>\n            )}\n          </TabsContent>\n\n          <TabsContent value=\"templates\" className=\"mt-6\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h3 className=\"text-lg font-medium\">Saved Report Templates</h3>\n                  <p className=\"text-sm text-muted-foreground\">\n                    Reuse previously saved report configurations\n                  </p>\n                </div>\n                <Button \n                  variant=\"outline\"\n                  onClick={() => setActiveTab(\"builder\")}\n                >\n                  <Users className=\"mr-2 h-4 w-4\" />\n                  Create New\n                </Button>\n              </div>\n\n              {savedReports.length === 0 ? (\n                <div className=\"text-center py-12 text-muted-foreground\">\n                  <Save className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                  <p>No saved templates</p>\n                  <p className=\"text-sm\">Save report configurations to reuse them later</p>\n                </div>\n              ) : (\n                <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n                  {savedReports.map((report, index) => (\n                    <div key={index} className=\"p-4 border rounded-lg space-y-3\">\n                      <div>\n                        <h4 className=\"font-medium\">{report.name}</h4>\n                        <p className=\"text-sm text-muted-foreground\">\n                          {report.description}\n                        </p>\n                      </div>\n                      \n                      <div className=\"space-y-2\">\n                        <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n                          <span>{report.query.tables.length} data sources</span>\n                          <span>•</span>\n                          <span>{report.query.fields.length} fields</span>\n                          <span>•</span>\n                          <span>{report.query.conditions.length} filters</span>\n                        </div>\n                        \n                        <div className=\"flex items-center gap-2\">\n                          <Button \n                            size=\"sm\" \n                            variant=\"outline\"\n                            onClick={() => {\n                              setCurrentReport(report)\n                              setActiveTab(\"preview\")\n                            }}\n                          >\n                            <Eye className=\"h-3 w-3 mr-1\" />\n                            Preview\n                          </Button>\n                          <Button \n                            size=\"sm\"\n                            onClick={() => handleGenerateReport(report)}\n                          >\n                            Generate\n                          </Button>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </TabsContent>\n        </Tabs>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AAVA;;;;;;;;;;AAiBO,SAAS,mBAAmB,EAAE,IAAI,EAAE,YAAY,EAA2B;IAChF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAEnE,MAAM,uBAAuB,CAAC;QAC5B,iBAAiB;QACjB,aAAa;QACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,wCAAwC;YACpD,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB;QACjB,aAAa;IACf;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;eAAI;YAAc;SAAO;QACzC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,yBAAyB;YACrC,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,6BAA6B,EAAE,OAAO,OAAO,CAAC,EAAE;YAC7D,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,KAAK;QACZ,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;YACnC,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB;QAC1B,aAAa;IACf;IAEA,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,2HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,2HAAA,CAAA,eAAY;;sCACX,8OAAC,2HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG/B,8OAAC,2HAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC,yHAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,8OAAC,yHAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;;sDACrC,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,8OAAC,yHAAA,CAAA,cAAW;oCACV,OAAM;oCACN,UAAU,CAAC;oCACX,WAAU;;sDAEV,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG7B,8OAAC,yHAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,WAAU;;sDACvC,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAKhC,8OAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACrC,cAAA,8OAAC,qJAAA,CAAA,sBAAmB;gCAClB,YAAY;gCACZ,WAAW;gCACX,QAAQ;;;;;;;;;;;sCAIZ,8OAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACpC,8BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;;8DAEV,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAKrC,8OAAC,qJAAA,CAAA,sBAAmB;wCAClB,QAAQ;wCACR,YAAY;wCACZ,SAAS;;;;;;;;;;;qDAIb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;;sCAK7B,8OAAC,yHAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;sCACvC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAsB;;;;;;kEACpC,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAI/C,8OAAC,2HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,aAAa;;kEAE5B,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;oCAKrC,aAAa,MAAM,KAAK,kBACvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAE;;;;;;0DACH,8OAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;6DAGzB,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAe,OAAO,IAAI;;;;;;0EACxC,8OAAC;gEAAE,WAAU;0EACV,OAAO,WAAW;;;;;;;;;;;;kEAIvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;4EAAM,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM;4EAAC;;;;;;;kFAClC,8OAAC;kFAAK;;;;;;kFACN,8OAAC;;4EAAM,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM;4EAAC;;;;;;;kFAClC,8OAAC;kFAAK;;;;;;kFACN,8OAAC;;4EAAM,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM;4EAAC;;;;;;;;;;;;;0EAGxC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,2HAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,SAAS;4EACP,iBAAiB;4EACjB,aAAa;wEACf;;0FAEA,8OAAC,gMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;kFAGlC,8OAAC,2HAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAS,IAAM,qBAAqB;kFACrC;;;;;;;;;;;;;;;;;;;+CAhCG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+C9B", "debugId": null}}, {"offset": {"line": 5929, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/report-archive.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\"\nimport { GeneratedReport, ReportArchive } from \"@/lib/types/reports\"\nimport { mockGeneratedReports } from \"@/lib/data/reports-mock-data\"\nimport { \n  Archive, \n  Search, \n  Download, \n  Trash2, \n  RotateCcw, \n  MoreHorizontal,\n  Calendar,\n  FileText,\n  Clock,\n  HardDrive\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\nimport { toast } from \"sonner\"\n\ninterface ReportArchiveProps {\n  className?: string\n}\n\n// Mock archived reports\nconst mockArchivedReports: (GeneratedReport & { archiveInfo: ReportArchive })[] = [\n  {\n    ...mockGeneratedReports[0],\n    id: \"ARC001\",\n    status: \"ARCHIVED\",\n    archiveInfo: {\n      id: \"ARCH001\",\n      reportId: \"RPT001\",\n      archivedAt: \"2024-12-01T10:00:00Z\",\n      archivedBy: \"admin\",\n      reason: \"Automatic archival after 30 days\",\n      retentionPeriod: 365,\n      autoDelete: true\n    }\n  },\n  {\n    ...mockGeneratedReports[1],\n    id: \"ARC002\", \n    status: \"ARCHIVED\",\n    archiveInfo: {\n      id: \"ARCH002\",\n      reportId: \"RPT002\",\n      archivedAt: \"2024-11-15T15:30:00Z\",\n      archivedBy: \"principal\",\n      reason: \"Manual archival for compliance\",\n      retentionPeriod: 2555, // 7 years\n      autoDelete: false\n    }\n  }\n]\n\nexport function ReportArchive({ className }: ReportArchiveProps) {\n  const [archivedReports, setArchivedReports] = useState(mockArchivedReports)\n  const [searchQuery, setSearchQuery] = useState(\"\")\n  const [sortBy, setSortBy] = useState<\"date\" | \"name\" | \"type\">(\"date\")\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"desc\")\n\n  // Filter and sort archived reports\n  const filteredReports = archivedReports\n    .filter(report => \n      report.config.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      report.config.description.toLowerCase().includes(searchQuery.toLowerCase())\n    )\n    .sort((a, b) => {\n      let comparison = 0\n      switch (sortBy) {\n        case \"date\":\n          comparison = new Date(a.archiveInfo.archivedAt).getTime() - new Date(b.archiveInfo.archivedAt).getTime()\n          break\n        case \"name\":\n          comparison = a.config.name.localeCompare(b.config.name)\n          break\n        case \"type\":\n          comparison = a.config.type.localeCompare(b.config.type)\n          break\n      }\n      return sortOrder === \"desc\" ? -comparison : comparison\n    })\n\n  const handleRestore = (reportId: string) => {\n    setArchivedReports(archivedReports.filter(report => report.id !== reportId))\n    toast.success(\"Report restored successfully\", {\n      description: \"The report has been moved back to the active reports list\"\n    })\n  }\n\n  const handlePermanentDelete = (reportId: string) => {\n    setArchivedReports(archivedReports.filter(report => report.id !== reportId))\n    toast.success(\"Report permanently deleted\", {\n      description: \"This action cannot be undone\"\n    })\n  }\n\n  const handleDownload = (reportId: string) => {\n    toast.success(\"Downloading archived report\")\n  }\n\n  const calculateDaysUntilDeletion = (archiveInfo: ReportArchive): number => {\n    if (!archiveInfo.autoDelete) return -1\n    \n    const archivedDate = new Date(archiveInfo.archivedAt)\n    const deletionDate = new Date(archivedDate.getTime() + (archiveInfo.retentionPeriod * 24 * 60 * 60 * 1000))\n    const now = new Date()\n    const daysLeft = Math.ceil((deletionDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000))\n    \n    return Math.max(0, daysLeft)\n  }\n\n  const getRetentionBadge = (archiveInfo: ReportArchive) => {\n    if (!archiveInfo.autoDelete) {\n      return <Badge variant=\"secondary\">Permanent</Badge>\n    }\n    \n    const daysLeft = calculateDaysUntilDeletion(archiveInfo)\n    \n    if (daysLeft === 0) {\n      return <Badge variant=\"destructive\">Expires Today</Badge>\n    } else if (daysLeft <= 7) {\n      return <Badge variant=\"destructive\">Expires in {daysLeft} days</Badge>\n    } else if (daysLeft <= 30) {\n      return <Badge variant=\"secondary\">Expires in {daysLeft} days</Badge>\n    } else {\n      return <Badge variant=\"outline\">{daysLeft} days left</Badge>\n    }\n  }\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes'\n    const k = 1024\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  const getTotalArchiveSize = () => {\n    const totalBytes = archivedReports.reduce((sum, report) => sum + (report.fileSize || 0), 0)\n    return formatFileSize(totalBytes)\n  }\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold flex items-center gap-2\">\n            <Archive className=\"h-6 w-6\" />\n            Report Archive\n          </h2>\n          <p className=\"text-muted-foreground\">\n            Manage archived reports and retention policies\n          </p>\n        </div>\n        <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\n          <div className=\"flex items-center gap-1\">\n            <FileText className=\"h-4 w-4\" />\n            {archivedReports.length} reports\n          </div>\n          <div className=\"flex items-center gap-1\">\n            <HardDrive className=\"h-4 w-4\" />\n            {getTotalArchiveSize()}\n          </div>\n        </div>\n      </div>\n\n      {/* Archive Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Total Archived</p>\n                <p className=\"text-2xl font-bold\">{archivedReports.length}</p>\n              </div>\n              <Archive className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Expiring Soon</p>\n                <p className=\"text-2xl font-bold text-orange-600\">\n                  {archivedReports.filter(r => {\n                    const days = calculateDaysUntilDeletion(r.archiveInfo)\n                    return days >= 0 && days <= 30\n                  }).length}\n                </p>\n              </div>\n              <Clock className=\"h-8 w-8 text-orange-600\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Permanent</p>\n                <p className=\"text-2xl font-bold text-blue-600\">\n                  {archivedReports.filter(r => !r.archiveInfo.autoDelete).length}\n                </p>\n              </div>\n              <FileText className=\"h-8 w-8 text-blue-600\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Storage Used</p>\n                <p className=\"text-2xl font-bold\">{getTotalArchiveSize()}</p>\n              </div>\n              <HardDrive className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Search and Filters */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"relative flex-1\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search archived reports...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n            \n            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {\n              const [field, order] = value.split('-')\n              setSortBy(field as typeof sortBy)\n              setSortOrder(order as typeof sortOrder)\n            }}>\n              <SelectTrigger className=\"w-40\">\n                <SelectValue placeholder=\"Sort by\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"date-desc\">Newest First</SelectItem>\n                <SelectItem value=\"date-asc\">Oldest First</SelectItem>\n                <SelectItem value=\"name-asc\">Name A-Z</SelectItem>\n                <SelectItem value=\"name-desc\">Name Z-A</SelectItem>\n                <SelectItem value=\"type-asc\">Type A-Z</SelectItem>\n                <SelectItem value=\"type-desc\">Type Z-A</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Archived Reports Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Archived Reports</CardTitle>\n          <CardDescription>\n            View and manage your archived reports\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {filteredReports.length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              <Archive className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n              <p>No archived reports found</p>\n              <p className=\"text-sm\">Reports will appear here when archived</p>\n            </div>\n          ) : (\n            <div className=\"rounded-md border\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>Report</TableHead>\n                    <TableHead>Archived</TableHead>\n                    <TableHead>Retention</TableHead>\n                    <TableHead>Size</TableHead>\n                    <TableHead>Reason</TableHead>\n                    <TableHead className=\"text-right\">Actions</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {filteredReports.map((report) => (\n                    <TableRow key={report.id}>\n                      <TableCell>\n                        <div className=\"space-y-1\">\n                          <div className=\"font-medium\">{report.config.name}</div>\n                          <div className=\"text-sm text-muted-foreground\">\n                            {report.config.description}\n                          </div>\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            {report.config.type}\n                          </Badge>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"text-sm\">\n                          {format(new Date(report.archiveInfo.archivedAt), \"MMM dd, yyyy\")}\n                          <div className=\"text-xs text-muted-foreground\">\n                            by {report.archiveInfo.archivedBy}\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        {getRetentionBadge(report.archiveInfo)}\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"text-sm\">\n                          {report.fileSize ? formatFileSize(report.fileSize) : '-'}\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"text-sm text-muted-foreground max-w-48 truncate\">\n                          {report.archiveInfo.reason}\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"text-right\">\n                        <div className=\"flex items-center justify-end gap-2\">\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => handleDownload(report.id)}\n                          >\n                            <Download className=\"h-4 w-4\" />\n                          </Button>\n                          \n                          <DropdownMenu>\n                            <DropdownMenuTrigger asChild>\n                              <Button variant=\"ghost\" size=\"sm\">\n                                <MoreHorizontal className=\"h-4 w-4\" />\n                              </Button>\n                            </DropdownMenuTrigger>\n                            <DropdownMenuContent align=\"end\">\n                              <DropdownMenuItem onClick={() => handleRestore(report.id)}>\n                                <RotateCcw className=\"h-4 w-4 mr-2\" />\n                                Restore\n                              </DropdownMenuItem>\n                              <DropdownMenuItem \n                                onClick={() => handlePermanentDelete(report.id)}\n                                className=\"text-destructive\"\n                              >\n                                <Trash2 className=\"h-4 w-4 mr-2\" />\n                                Delete Permanently\n                              </DropdownMenuItem>\n                            </DropdownMenuContent>\n                          </DropdownMenu>\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AA1BA;;;;;;;;;;;;;;AAgCA,wBAAwB;AACxB,MAAM,sBAA4E;IAChF;QACE,GAAG,sIAAA,CAAA,uBAAoB,CAAC,EAAE;QAC1B,IAAI;QACJ,QAAQ;QACR,aAAa;YACX,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,QAAQ;YACR,iBAAiB;YACjB,YAAY;QACd;IACF;IACA;QACE,GAAG,sIAAA,CAAA,uBAAoB,CAAC,EAAE;QAC1B,IAAI;QACJ,QAAQ;QACR,aAAa;YACX,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,QAAQ;YACR,iBAAiB;YACjB,YAAY;QACd;IACF;CACD;AAEM,SAAS,cAAc,EAAE,SAAS,EAAsB;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,mCAAmC;IACnC,MAAM,kBAAkB,gBACrB,MAAM,CAAC,CAAA,SACN,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACjE,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,KAEzE,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,aAAa,IAAI,KAAK,EAAE,WAAW,CAAC,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,CAAC,UAAU,EAAE,OAAO;gBACtG;YACF,KAAK;gBACH,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,IAAI;gBACtD;YACF,KAAK;gBACH,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,IAAI;gBACtD;QACJ;QACA,OAAO,cAAc,SAAS,CAAC,aAAa;IAC9C;IAEF,MAAM,gBAAgB,CAAC;QACrB,mBAAmB,gBAAgB,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QAClE,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,gCAAgC;YAC5C,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB,gBAAgB,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QAClE,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,8BAA8B;YAC1C,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,6BAA6B,CAAC;QAClC,IAAI,CAAC,YAAY,UAAU,EAAE,OAAO,CAAC;QAErC,MAAM,eAAe,IAAI,KAAK,YAAY,UAAU;QACpD,MAAM,eAAe,IAAI,KAAK,aAAa,OAAO,KAAM,YAAY,eAAe,GAAG,KAAK,KAAK,KAAK;QACrG,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,IAAI,CAAC,CAAC,aAAa,OAAO,KAAK,IAAI,OAAO,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI;QAE1F,OAAO,KAAK,GAAG,CAAC,GAAG;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,YAAY,UAAU,EAAE;YAC3B,qBAAO,8OAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAY;;;;;;QACpC;QAEA,MAAM,WAAW,2BAA2B;QAE5C,IAAI,aAAa,GAAG;YAClB,qBAAO,8OAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAc;;;;;;QACtC,OAAO,IAAI,YAAY,GAAG;YACxB,qBAAO,8OAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;;oBAAc;oBAAY;oBAAS;;;;;;;QAC3D,OAAO,IAAI,YAAY,IAAI;YACzB,qBAAO,8OAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;;oBAAY;oBAAY;oBAAS;;;;;;;QACzD,OAAO;YACL,qBAAO,8OAAC,0HAAA,CAAA,QAAK;gBAAC,SAAQ;;oBAAW;oBAAS;;;;;;;QAC5C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,sBAAsB;QAC1B,MAAM,aAAa,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,CAAC,OAAO,QAAQ,IAAI,CAAC,GAAG;QACzF,OAAO,eAAe;IACxB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGjC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,gBAAgB,MAAM;oCAAC;;;;;;;0CAE1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCACpB;;;;;;;;;;;;;;;;;;;0BAMP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsB,gBAAgB,MAAM;;;;;;;;;;;;kDAE3D,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKzB,8OAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DACV,gBAAgB,MAAM,CAAC,CAAA;oDACtB,MAAM,OAAO,2BAA2B,EAAE,WAAW;oDACrD,OAAO,QAAQ,KAAK,QAAQ;gDAC9B,GAAG,MAAM;;;;;;;;;;;;kDAGb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKvB,8OAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DACV,gBAAgB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,WAAW,CAAC,UAAU,EAAE,MAAM;;;;;;;;;;;;kDAGlE,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,8OAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;kDAErC,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7B,8OAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,0HAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAId,8OAAC,2HAAA,CAAA,SAAM;gCAAC,OAAO,GAAG,OAAO,CAAC,EAAE,WAAW;gCAAE,eAAe,CAAC;oCACvD,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,KAAK,CAAC;oCACnC,UAAU;oCACV,aAAa;gCACf;;kDACE,8OAAC,2HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,8OAAC,2HAAA,CAAA,gBAAa;;0DACZ,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;0DAC9B,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;0DAC9B,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,8OAAC,2HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;;0CACT,8OAAC,yHAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,yHAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,yHAAA,CAAA,cAAW;kCACT,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;iDAGzB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;kDACJ,8OAAC,0HAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;;8DACP,8OAAC,0HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,0HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,0HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,0HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,0HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,0HAAA,CAAA,YAAS;oDAAC,WAAU;8DAAa;;;;;;;;;;;;;;;;;kDAGtC,8OAAC,0HAAA,CAAA,YAAS;kDACP,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC,0HAAA,CAAA,WAAQ;;kEACP,8OAAC,0HAAA,CAAA,YAAS;kEACR,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAe,OAAO,MAAM,CAAC,IAAI;;;;;;8EAChD,8OAAC;oEAAI,WAAU;8EACZ,OAAO,MAAM,CAAC,WAAW;;;;;;8EAE5B,8OAAC,0HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;8EAChC,OAAO,MAAM,CAAC,IAAI;;;;;;;;;;;;;;;;;kEAIzB,8OAAC,0HAAA,CAAA,YAAS;kEACR,cAAA,8OAAC;4DAAI,WAAU;;gEACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,WAAW,CAAC,UAAU,GAAG;8EACjD,8OAAC;oEAAI,WAAU;;wEAAgC;wEACzC,OAAO,WAAW,CAAC,UAAU;;;;;;;;;;;;;;;;;;kEAIvC,8OAAC,0HAAA,CAAA,YAAS;kEACP,kBAAkB,OAAO,WAAW;;;;;;kEAEvC,8OAAC,0HAAA,CAAA,YAAS;kEACR,cAAA,8OAAC;4DAAI,WAAU;sEACZ,OAAO,QAAQ,GAAG,eAAe,OAAO,QAAQ,IAAI;;;;;;;;;;;kEAGzD,8OAAC,0HAAA,CAAA,YAAS;kEACR,cAAA,8OAAC;4DAAI,WAAU;sEACZ,OAAO,WAAW,CAAC,MAAM;;;;;;;;;;;kEAG9B,8OAAC,0HAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,2HAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,eAAe,OAAO,EAAE;8EAEvC,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAGtB,8OAAC,qIAAA,CAAA,eAAY;;sFACX,8OAAC,qIAAA,CAAA,sBAAmB;4EAAC,OAAO;sFAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;gFAAC,SAAQ;gFAAQ,MAAK;0FAC3B,cAAA,8OAAC,gNAAA,CAAA,iBAAc;oFAAC,WAAU;;;;;;;;;;;;;;;;sFAG9B,8OAAC,qIAAA,CAAA,sBAAmB;4EAAC,OAAM;;8FACzB,8OAAC,qIAAA,CAAA,mBAAgB;oFAAC,SAAS,IAAM,cAAc,OAAO,EAAE;;sGACtD,8OAAC,gNAAA,CAAA,YAAS;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;8FAGxC,8OAAC,qIAAA,CAAA,mBAAgB;oFACf,SAAS,IAAM,sBAAsB,OAAO,EAAE;oFAC9C,WAAU;;sGAEV,8OAAC,0MAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CA1DhC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2E5C", "debugId": null}}, {"offset": {"line": 6935, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/report-wizard.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState } from \"react\"\nimport { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { ReportTypeSelector } from \"./report-type-selector\"\nimport { ReportFilters } from \"./report-filters\"\nimport { ReportType, ReportFilters as ReportFiltersType, ReportConfig } from \"@/lib/types/reports\"\nimport { \n  ChevronLeft, \n  ChevronRight, \n  FileText, \n  Filter, \n  Settings, \n  Eye, \n  Download,\n  CheckCircle,\n  Wand2\n} from \"lucide-react\"\nimport { toast } from \"sonner\"\n\ninterface ReportWizardProps {\n  onComplete: (config: ReportConfig) => void\n  onCancel: () => void\n  className?: string\n}\n\ntype WizardStep = \"type\" | \"filters\" | \"settings\" | \"preview\"\n\nconst wizardSteps = [\n  { id: \"type\", title: \"Report Type\", description: \"Choose the type of report to generate\", icon: FileText },\n  { id: \"filters\", title: \"Filters\", description: \"Set date ranges and data filters\", icon: Filter },\n  { id: \"settings\", title: \"Settings\", description: \"Configure report options\", icon: Settings },\n  { id: \"preview\", title: \"Preview\", description: \"Review and generate report\", icon: Eye }\n]\n\nexport function ReportWizard({ onComplete, onCancel, className }: ReportWizardProps) {\n  const [currentStep, setCurrentStep] = useState<WizardStep>(\"type\")\n  const [selectedReportType, setSelectedReportType] = useState<ReportType>(\"SF2\")\n  const [reportFilters, setReportFilters] = useState<ReportFiltersType>({})\n  const [reportSettings, setReportSettings] = useState({\n    includeSignatures: true,\n    includePhotos: false,\n    includeRemarks: true,\n    showStatistics: true,\n    pageOrientation: \"portrait\" as \"portrait\" | \"landscape\",\n    fontSize: \"medium\" as \"small\" | \"medium\" | \"large\",\n    includeHeader: true,\n    includeFooter: true,\n    watermark: \"\"\n  })\n  const [reportName, setReportName] = useState(\"\")\n  const [reportDescription, setReportDescription] = useState(\"\")\n\n  const getCurrentStepIndex = () => {\n    return wizardSteps.findIndex(step => step.id === currentStep)\n  }\n\n  const getProgress = () => {\n    return ((getCurrentStepIndex() + 1) / wizardSteps.length) * 100\n  }\n\n  const canProceed = () => {\n    switch (currentStep) {\n      case \"type\":\n        return selectedReportType !== undefined\n      case \"filters\":\n        return true // Filters are optional\n      case \"settings\":\n        return reportName.trim() !== \"\"\n      case \"preview\":\n        return true\n      default:\n        return false\n    }\n  }\n\n  const handleNext = () => {\n    const currentIndex = getCurrentStepIndex()\n    if (currentIndex < wizardSteps.length - 1) {\n      setCurrentStep(wizardSteps[currentIndex + 1].id as WizardStep)\n    }\n  }\n\n  const handlePrevious = () => {\n    const currentIndex = getCurrentStepIndex()\n    if (currentIndex > 0) {\n      setCurrentStep(wizardSteps[currentIndex - 1].id as WizardStep)\n    }\n  }\n\n  const handleComplete = () => {\n    const config: ReportConfig = {\n      id: `wizard_${Date.now()}`,\n      name: reportName,\n      type: selectedReportType,\n      description: reportDescription,\n      dateRange: {\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: new Date().toISOString().split('T')[0]\n      },\n      filters: reportFilters,\n      settings: reportSettings,\n      createdBy: \"current-user\",\n      createdAt: new Date().toISOString(),\n      lastModified: new Date().toISOString()\n    }\n\n    onComplete(config)\n    toast.success(\"Report configuration completed\", {\n      description: \"Your report is being generated\"\n    })\n  }\n\n  const generateDefaultName = () => {\n    const today = new Date().toLocaleDateString()\n    const typeNames = {\n      SF2: \"SF2 Daily Attendance\",\n      SF4: \"SF4 Monthly Movement\", \n      DAILY: \"Daily Summary\",\n      WEEKLY: \"Weekly Summary\",\n      MONTHLY: \"Monthly Summary\",\n      ANNUAL: \"Annual Summary\",\n      CUSTOM: \"Custom Report\"\n    }\n    return `${typeNames[selectedReportType]} - ${today}`\n  }\n\n  const generateDefaultDescription = () => {\n    const descriptions = {\n      SF2: \"Official DepEd SF2 Daily Attendance Report of Learners\",\n      SF4: \"Official DepEd SF4 Monthly Report on Learner's Movement\",\n      DAILY: \"Daily attendance summary for all students\",\n      WEEKLY: \"Weekly attendance trends and statistics\",\n      MONTHLY: \"Monthly attendance overview and analysis\",\n      ANNUAL: \"Annual attendance and performance report\",\n      CUSTOM: \"Custom report with selected data and filters\"\n    }\n    return descriptions[selectedReportType]\n  }\n\n  // Auto-generate name and description when report type changes\n  React.useEffect(() => {\n    if (!reportName) {\n      setReportName(generateDefaultName())\n    }\n    if (!reportDescription) {\n      setReportDescription(generateDefaultDescription())\n    }\n  }, [selectedReportType])\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Wizard Header */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"p-2 bg-primary/10 rounded-lg\">\n                <Wand2 className=\"h-6 w-6 text-primary\" />\n              </div>\n              <div>\n                <CardTitle>Report Generation Wizard</CardTitle>\n                <CardDescription>\n                  Step-by-step guide to create your report\n                </CardDescription>\n              </div>\n            </div>\n            <Button variant=\"ghost\" onClick={onCancel}>\n              Cancel\n            </Button>\n          </div>\n        </CardHeader>\n        <CardContent>\n          {/* Progress Bar */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between text-sm\">\n              <span>Step {getCurrentStepIndex() + 1} of {wizardSteps.length}</span>\n              <span>{Math.round(getProgress())}% Complete</span>\n            </div>\n            <Progress value={getProgress()} className=\"w-full\" />\n          </div>\n\n          {/* Step Indicators */}\n          <div className=\"flex items-center justify-between mt-6\">\n            {wizardSteps.map((step, index) => {\n              const isActive = step.id === currentStep\n              const isCompleted = index < getCurrentStepIndex()\n              const Icon = step.icon\n\n              return (\n                <div key={step.id} className=\"flex flex-col items-center space-y-2\">\n                  <div className={`\n                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors\n                    ${isActive ? 'border-primary bg-primary text-primary-foreground' : \n                      isCompleted ? 'border-green-500 bg-green-500 text-white' : \n                      'border-muted-foreground/30 bg-background'}\n                  `}>\n                    {isCompleted ? (\n                      <CheckCircle className=\"h-5 w-5\" />\n                    ) : (\n                      <Icon className=\"h-5 w-5\" />\n                    )}\n                  </div>\n                  <div className=\"text-center\">\n                    <div className={`text-sm font-medium ${isActive ? 'text-primary' : isCompleted ? 'text-green-600' : 'text-muted-foreground'}`}>\n                      {step.title}\n                    </div>\n                    <div className=\"text-xs text-muted-foreground hidden sm:block\">\n                      {step.description}\n                    </div>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Step Content */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          {currentStep === \"type\" && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold mb-2\">Choose Report Type</h3>\n                <p className=\"text-muted-foreground mb-6\">\n                  Select the type of report you want to generate. Each type has specific features and formatting.\n                </p>\n              </div>\n              <ReportTypeSelector\n                selectedType={selectedReportType}\n                onTypeSelect={setSelectedReportType}\n              />\n            </div>\n          )}\n\n          {currentStep === \"filters\" && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold mb-2\">Configure Filters</h3>\n                <p className=\"text-muted-foreground mb-6\">\n                  Set date ranges and apply filters to customize your report data.\n                </p>\n              </div>\n              <ReportFilters\n                filters={reportFilters}\n                onFiltersChange={setReportFilters}\n                reportType={selectedReportType}\n              />\n            </div>\n          )}\n\n          {currentStep === \"settings\" && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold mb-2\">Report Settings</h3>\n                <p className=\"text-muted-foreground mb-6\">\n                  Configure report options and provide a name and description.\n                </p>\n              </div>\n              \n              {/* Report Name and Description */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Report Name</label>\n                  <input\n                    type=\"text\"\n                    value={reportName}\n                    onChange={(e) => setReportName(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-input rounded-md\"\n                    placeholder=\"Enter report name\"\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Page Orientation</label>\n                  <select\n                    value={reportSettings.pageOrientation}\n                    onChange={(e) => setReportSettings({\n                      ...reportSettings,\n                      pageOrientation: e.target.value as \"portrait\" | \"landscape\"\n                    })}\n                    className=\"w-full px-3 py-2 border border-input rounded-md\"\n                  >\n                    <option value=\"portrait\">Portrait</option>\n                    <option value=\"landscape\">Landscape</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">Description</label>\n                <textarea\n                  value={reportDescription}\n                  onChange={(e) => setReportDescription(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-input rounded-md\"\n                  rows={3}\n                  placeholder=\"Enter report description\"\n                />\n              </div>\n\n              {/* Report Options */}\n              <div className=\"space-y-4\">\n                <h4 className=\"font-medium\">Report Options</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-3\">\n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={reportSettings.includeSignatures}\n                        onChange={(e) => setReportSettings({\n                          ...reportSettings,\n                          includeSignatures: e.target.checked\n                        })}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Include signature fields</span>\n                    </label>\n                    \n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={reportSettings.includeRemarks}\n                        onChange={(e) => setReportSettings({\n                          ...reportSettings,\n                          includeRemarks: e.target.checked\n                        })}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Include remarks column</span>\n                    </label>\n                    \n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={reportSettings.showStatistics}\n                        onChange={(e) => setReportSettings({\n                          ...reportSettings,\n                          showStatistics: e.target.checked\n                        })}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Show statistics summary</span>\n                    </label>\n                  </div>\n                  \n                  <div className=\"space-y-3\">\n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={reportSettings.includeHeader}\n                        onChange={(e) => setReportSettings({\n                          ...reportSettings,\n                          includeHeader: e.target.checked\n                        })}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Include header</span>\n                    </label>\n                    \n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={reportSettings.includeFooter}\n                        onChange={(e) => setReportSettings({\n                          ...reportSettings,\n                          includeFooter: e.target.checked\n                        })}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Include footer</span>\n                    </label>\n                    \n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Font Size</label>\n                      <select\n                        value={reportSettings.fontSize}\n                        onChange={(e) => setReportSettings({\n                          ...reportSettings,\n                          fontSize: e.target.value as \"small\" | \"medium\" | \"large\"\n                        })}\n                        className=\"w-full px-3 py-2 border border-input rounded-md\"\n                      >\n                        <option value=\"small\">Small</option>\n                        <option value=\"medium\">Medium</option>\n                        <option value=\"large\">Large</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {currentStep === \"preview\" && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold mb-2\">Review Configuration</h3>\n                <p className=\"text-muted-foreground mb-6\">\n                  Review your report configuration before generating.\n                </p>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"space-y-4\">\n                  <div>\n                    <h4 className=\"font-medium text-sm text-muted-foreground\">Report Type</h4>\n                    <Badge variant=\"outline\" className=\"mt-1\">{selectedReportType}</Badge>\n                  </div>\n                  \n                  <div>\n                    <h4 className=\"font-medium text-sm text-muted-foreground\">Report Name</h4>\n                    <p className=\"text-sm\">{reportName}</p>\n                  </div>\n                  \n                  <div>\n                    <h4 className=\"font-medium text-sm text-muted-foreground\">Description</h4>\n                    <p className=\"text-sm\">{reportDescription}</p>\n                  </div>\n                </div>\n                \n                <div className=\"space-y-4\">\n                  <div>\n                    <h4 className=\"font-medium text-sm text-muted-foreground\">Filters Applied</h4>\n                    <div className=\"flex flex-wrap gap-1 mt-1\">\n                      {Object.keys(reportFilters).length === 0 ? (\n                        <Badge variant=\"secondary\">No filters</Badge>\n                      ) : (\n                        Object.entries(reportFilters).map(([key, value]) => (\n                          <Badge key={key} variant=\"secondary\" className=\"text-xs\">\n                            {key}: {Array.isArray(value) ? value.length : 1}\n                          </Badge>\n                        ))\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <h4 className=\"font-medium text-sm text-muted-foreground\">Settings</h4>\n                    <div className=\"text-sm space-y-1\">\n                      <p>Orientation: {reportSettings.pageOrientation}</p>\n                      <p>Font Size: {reportSettings.fontSize}</p>\n                      <p>Signatures: {reportSettings.includeSignatures ? 'Yes' : 'No'}</p>\n                      <p>Statistics: {reportSettings.showStatistics ? 'Yes' : 'No'}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Navigation */}\n      <div className=\"flex items-center justify-between\">\n        <Button\n          variant=\"outline\"\n          onClick={handlePrevious}\n          disabled={getCurrentStepIndex() === 0}\n        >\n          <ChevronLeft className=\"mr-2 h-4 w-4\" />\n          Previous\n        </Button>\n\n        <div className=\"flex items-center gap-2\">\n          {getCurrentStepIndex() === wizardSteps.length - 1 ? (\n            <Button onClick={handleComplete} disabled={!canProceed()}>\n              <Download className=\"mr-2 h-4 w-4\" />\n              Generate Report\n            </Button>\n          ) : (\n            <Button onClick={handleNext} disabled={!canProceed()}>\n              Next\n              <ChevronRight className=\"ml-2 h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AArBA;;;;;;;;;;;AA+BA,MAAM,cAAc;IAClB;QAAE,IAAI;QAAQ,OAAO;QAAe,aAAa;QAAyC,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACzG;QAAE,IAAI;QAAW,OAAO;QAAW,aAAa;QAAoC,MAAM,sMAAA,CAAA,SAAM;IAAC;IACjG;QAAE,IAAI;QAAY,OAAO;QAAY,aAAa;QAA4B,MAAM,0MAAA,CAAA,WAAQ;IAAC;IAC7F;QAAE,IAAI;QAAW,OAAO;QAAW,aAAa;QAA8B,MAAM,gMAAA,CAAA,MAAG;IAAC;CACzF;AAEM,SAAS,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAqB;IACjF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,mBAAmB;QACnB,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,UAAU;QACV,eAAe;QACf,eAAe;QACf,WAAW;IACb;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,sBAAsB;QAC1B,OAAO,YAAY,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACnD;IAEA,MAAM,cAAc;QAClB,OAAO,AAAC,CAAC,wBAAwB,CAAC,IAAI,YAAY,MAAM,GAAI;IAC9D;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO,uBAAuB;YAChC,KAAK;gBACH,OAAO,KAAK,uBAAuB;;YACrC,KAAK;gBACH,OAAO,WAAW,IAAI,OAAO;YAC/B,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;QACjB,MAAM,eAAe;QACrB,IAAI,eAAe,YAAY,MAAM,GAAG,GAAG;YACzC,eAAe,WAAW,CAAC,eAAe,EAAE,CAAC,EAAE;QACjD;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,eAAe;QACrB,IAAI,eAAe,GAAG;YACpB,eAAe,WAAW,CAAC,eAAe,EAAE,CAAC,EAAE;QACjD;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,SAAuB;YAC3B,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;YAC1B,MAAM;YACN,MAAM;YACN,aAAa;YACb,WAAW;gBACT,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACjD,SAAS,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACjD;YACA,SAAS;YACT,UAAU;YACV,WAAW;YACX,WAAW,IAAI,OAAO,WAAW;YACjC,cAAc,IAAI,OAAO,WAAW;QACtC;QAEA,WAAW;QACX,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kCAAkC;YAC9C,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,IAAI,OAAO,kBAAkB;QAC3C,MAAM,YAAY;YAChB,KAAK;YACL,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA,OAAO,GAAG,SAAS,CAAC,mBAAmB,CAAC,GAAG,EAAE,OAAO;IACtD;IAEA,MAAM,6BAA6B;QACjC,MAAM,eAAe;YACnB,KAAK;YACL,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA,OAAO,YAAY,CAAC,mBAAmB;IACzC;IAEA,8DAA8D;IAC9D,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,CAAC,YAAY;YACf,cAAc;QAChB;QACA,IAAI,CAAC,mBAAmB;YACtB,qBAAqB;QACvB;IACF,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,+MAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;;8DACC,8OAAC,yHAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,yHAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;;;;;;;8CAKrB,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,SAAS;8CAAU;;;;;;;;;;;;;;;;;kCAK/C,8OAAC,yHAAA,CAAA,cAAW;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAK;oDAAM,wBAAwB;oDAAE;oDAAK,YAAY,MAAM;;;;;;;0DAC7D,8OAAC;;oDAAM,KAAK,KAAK,CAAC;oDAAe;;;;;;;;;;;;;kDAEnC,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,OAAO;wCAAe,WAAU;;;;;;;;;;;;0CAI5C,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oCACtB,MAAM,WAAW,KAAK,EAAE,KAAK;oCAC7B,MAAM,cAAc,QAAQ;oCAC5B,MAAM,OAAO,KAAK,IAAI;oCAEtB,qBACE,8OAAC;wCAAkB,WAAU;;0DAC3B,8OAAC;gDAAI,WAAW,CAAC;;oBAEf,EAAE,WAAW,sDACX,cAAc,6CACd,2CAA2C;kBAC/C,CAAC;0DACE,4BACC,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,8OAAC;oDAAK,WAAU;;;;;;;;;;;0DAGpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,oBAAoB,EAAE,WAAW,iBAAiB,cAAc,mBAAmB,yBAAyB;kEAC1H,KAAK,KAAK;;;;;;kEAEb,8OAAC;wDAAI,WAAU;kEACZ,KAAK,WAAW;;;;;;;;;;;;;uCAlBb,KAAK,EAAE;;;;;gCAuBrB;;;;;;;;;;;;;;;;;;0BAMN,8OAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;wBACpB,gBAAgB,wBACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAI5C,8OAAC,oJAAA,CAAA,qBAAkB;oCACjB,cAAc;oCACd,cAAc;;;;;;;;;;;;wBAKnB,gBAAgB,2BACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAI5C,8OAAC,2IAAA,CAAA,gBAAa;oCACZ,SAAS;oCACT,iBAAiB;oCACjB,YAAY;;;;;;;;;;;;wBAKjB,gBAAgB,4BACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAM5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAsB;;;;;;8DACvC,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAGhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAsB;;;;;;8DACvC,8OAAC;oDACC,OAAO,eAAe,eAAe;oDACrC,UAAU,CAAC,IAAM,kBAAkB;4DACjC,GAAG,cAAc;4DACjB,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDACjC;oDACA,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAW;;;;;;sEACzB,8OAAC;4DAAO,OAAM;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAKhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CACpD,WAAU;4CACV,MAAM;4CACN,aAAY;;;;;;;;;;;;8CAKhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS,eAAe,iBAAiB;oEACzC,UAAU,CAAC,IAAM,kBAAkB;4EACjC,GAAG,cAAc;4EACjB,mBAAmB,EAAE,MAAM,CAAC,OAAO;wEACrC;oEACA,WAAU;;;;;;8EAEZ,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAG5B,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS,eAAe,cAAc;oEACtC,UAAU,CAAC,IAAM,kBAAkB;4EACjC,GAAG,cAAc;4EACjB,gBAAgB,EAAE,MAAM,CAAC,OAAO;wEAClC;oEACA,WAAU;;;;;;8EAEZ,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAG5B,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS,eAAe,cAAc;oEACtC,UAAU,CAAC,IAAM,kBAAkB;4EACjC,GAAG,cAAc;4EACjB,gBAAgB,EAAE,MAAM,CAAC,OAAO;wEAClC;oEACA,WAAU;;;;;;8EAEZ,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;;;;;;;8DAI9B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS,eAAe,aAAa;oEACrC,UAAU,CAAC,IAAM,kBAAkB;4EACjC,GAAG,cAAc;4EACjB,eAAe,EAAE,MAAM,CAAC,OAAO;wEACjC;oEACA,WAAU;;;;;;8EAEZ,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAG5B,8OAAC;4DAAM,WAAU;;8EACf,8OAAC;oEACC,MAAK;oEACL,SAAS,eAAe,aAAa;oEACrC,UAAU,CAAC,IAAM,kBAAkB;4EACjC,GAAG,cAAc;4EACjB,eAAe,EAAE,MAAM,CAAC,OAAO;wEACjC;oEACA,WAAU;;;;;;8EAEZ,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAG5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,8OAAC;oEACC,OAAO,eAAe,QAAQ;oEAC9B,UAAU,CAAC,IAAM,kBAAkB;4EACjC,GAAG,cAAc;4EACjB,UAAU,EAAE,MAAM,CAAC,KAAK;wEAC1B;oEACA,WAAU;;sFAEV,8OAAC;4EAAO,OAAM;sFAAQ;;;;;;sFACtB,8OAAC;4EAAO,OAAM;sFAAS;;;;;;sFACvB,8OAAC;4EAAO,OAAM;sFAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASnC,gBAAgB,2BACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4C;;;;;;sEAC1D,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAQ;;;;;;;;;;;;8DAG7C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4C;;;;;;sEAC1D,8OAAC;4DAAE,WAAU;sEAAW;;;;;;;;;;;;8DAG1B,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4C;;;;;;sEAC1D,8OAAC;4DAAE,WAAU;sEAAW;;;;;;;;;;;;;;;;;;sDAI5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4C;;;;;;sEAC1D,8OAAC;4DAAI,WAAU;sEACZ,OAAO,IAAI,CAAC,eAAe,MAAM,KAAK,kBACrC,8OAAC,0HAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAY;;;;;uEAE3B,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC7C,8OAAC,0HAAA,CAAA,QAAK;oEAAW,SAAQ;oEAAY,WAAU;;wEAC5C;wEAAI;wEAAG,MAAM,OAAO,CAAC,SAAS,MAAM,MAAM,GAAG;;mEADpC;;;;;;;;;;;;;;;;8DAQpB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA4C;;;;;;sEAC1D,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;wEAAE;wEAAc,eAAe,eAAe;;;;;;;8EAC/C,8OAAC;;wEAAE;wEAAY,eAAe,QAAQ;;;;;;;8EACtC,8OAAC;;wEAAE;wEAAa,eAAe,iBAAiB,GAAG,QAAQ;;;;;;;8EAC3D,8OAAC;;wEAAE;wEAAa,eAAe,cAAc,GAAG,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWxE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU,0BAA0B;;0CAEpC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAI1C,8OAAC;wBAAI,WAAU;kCACZ,0BAA0B,YAAY,MAAM,GAAG,kBAC9C,8OAAC,2HAAA,CAAA,SAAM;4BAAC,SAAS;4BAAgB,UAAU,CAAC;;8CAC1C,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;iDAIvC,8OAAC,2HAAA,CAAA,SAAM;4BAAC,SAAS;4BAAY,UAAU,CAAC;;gCAAc;8CAEpD,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}, {"offset": {"line": 8119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/report-wizard-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from \"@/components/ui/dialog\"\nimport { ReportWizard } from \"./report-wizard\"\nimport { ReportConfig } from \"@/lib/types/reports\"\nimport { Wand2 } from \"lucide-react\"\nimport { toast } from \"sonner\"\n\ninterface ReportWizardDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onComplete?: (config: ReportConfig) => void\n}\n\nexport function ReportWizardDialog({ \n  open, \n  onOpenChange, \n  onComplete \n}: ReportWizardDialogProps) {\n  const handleComplete = (config: ReportConfig) => {\n    onComplete?.(config)\n    onOpenChange(false)\n    toast.success(\"Report wizard completed\", {\n      description: \"Your report configuration has been saved and generation started\"\n    })\n  }\n\n  const handleCancel = () => {\n    onOpenChange(false)\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-6xl max-h-[95vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Wand2 className=\"h-5 w-5\" />\n            Report Generation Wizard\n          </DialogTitle>\n          <DialogDescription>\n            Follow the step-by-step guide to create your perfect report\n          </DialogDescription>\n        </DialogHeader>\n\n        <ReportWizard\n          onComplete={handleComplete}\n          onCancel={handleCancel}\n        />\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAcO,SAAS,mBAAmB,EACjC,IAAI,EACJ,YAAY,EACZ,UAAU,EACc;IACxB,MAAM,iBAAiB,CAAC;QACtB,aAAa;QACb,aAAa;QACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,2BAA2B;YACvC,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,aAAa;IACf;IAEA,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,2HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,2HAAA,CAAA,eAAY;;sCACX,8OAAC,2HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,+MAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG/B,8OAAC,2HAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC,0IAAA,CAAA,eAAY;oBACX,YAAY;oBACZ,UAAU;;;;;;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 8206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/bulk-report-generator.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { ReportType, ReportConfig } from \"@/lib/types/reports\"\nimport { DateRangePicker, DateRange } from \"@/components/ui/date-range-picker\"\nimport { \n  Layers, \n  Play, \n  Pause, \n  CheckCircle, \n  XCircle, \n  Clock,\n  Download,\n  FileText,\n  Calendar\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\nimport { toast } from \"sonner\"\n\ninterface BulkReportGeneratorProps {\n  onGenerate: (configs: ReportConfig[]) => void\n  className?: string\n}\n\ninterface BulkReportJob {\n  id: string\n  name: string\n  type: ReportType\n  dateRange: DateRange\n  filters: {\n    grades?: string[]\n    sections?: string[]\n  }\n  status: 'pending' | 'generating' | 'completed' | 'failed'\n  progress: number\n  error?: string\n  generatedAt?: string\n}\n\nconst gradeOptions = [\"7\", \"8\", \"9\", \"10\", \"11\", \"12\"]\nconst sectionOptions = [\"A\", \"B\", \"C\", \"D\", \"E\"]\n\nexport function BulkReportGenerator({ onGenerate, className }: BulkReportGeneratorProps) {\n  const [reportType, setReportType] = useState<ReportType>(\"SF2\")\n  const [dateRange, setDateRange] = useState<DateRange>({\n    from: new Date(),\n    to: new Date()\n  })\n  const [selectedGrades, setSelectedGrades] = useState<string[]>([])\n  const [selectedSections, setSelectedSections] = useState<string[]>([])\n  const [generateBy, setGenerateBy] = useState<\"grade\" | \"section\" | \"both\">(\"section\")\n  const [jobs, setJobs] = useState<BulkReportJob[]>([])\n  const [isGenerating, setIsGenerating] = useState(false)\n\n  const handleGradeChange = (grade: string, checked: boolean) => {\n    if (checked) {\n      setSelectedGrades([...selectedGrades, grade])\n    } else {\n      setSelectedGrades(selectedGrades.filter(g => g !== grade))\n    }\n  }\n\n  const handleSectionChange = (section: string, checked: boolean) => {\n    if (checked) {\n      setSelectedSections([...selectedSections, section])\n    } else {\n      setSelectedSections(selectedSections.filter(s => s !== section))\n    }\n  }\n\n  const generateJobList = (): BulkReportJob[] => {\n    const jobs: BulkReportJob[] = []\n\n    if (generateBy === \"grade\") {\n      selectedGrades.forEach(grade => {\n        jobs.push({\n          id: `bulk_${Date.now()}_${grade}`,\n          name: `${reportType} Report - Grade ${grade}`,\n          type: reportType,\n          dateRange,\n          filters: { grades: [grade] },\n          status: 'pending',\n          progress: 0\n        })\n      })\n    } else if (generateBy === \"section\") {\n      selectedGrades.forEach(grade => {\n        selectedSections.forEach(section => {\n          jobs.push({\n            id: `bulk_${Date.now()}_${grade}_${section}`,\n            name: `${reportType} Report - Grade ${grade}-${section}`,\n            type: reportType,\n            dateRange,\n            filters: { grades: [grade], sections: [section] },\n            status: 'pending',\n            progress: 0\n          })\n        })\n      })\n    } else { // both\n      // Generate one report per grade\n      selectedGrades.forEach(grade => {\n        jobs.push({\n          id: `bulk_${Date.now()}_grade_${grade}`,\n          name: `${reportType} Report - Grade ${grade} (All Sections)`,\n          type: reportType,\n          dateRange,\n          filters: { grades: [grade] },\n          status: 'pending',\n          progress: 0\n        })\n      })\n      \n      // Generate one report per section within each grade\n      selectedGrades.forEach(grade => {\n        selectedSections.forEach(section => {\n          jobs.push({\n            id: `bulk_${Date.now()}_section_${grade}_${section}`,\n            name: `${reportType} Report - Grade ${grade}-${section}`,\n            type: reportType,\n            dateRange,\n            filters: { grades: [grade], sections: [section] },\n            status: 'pending',\n            progress: 0\n          })\n        })\n      })\n    }\n\n    return jobs\n  }\n\n  const handleStartBulkGeneration = async () => {\n    if (!dateRange.from || !dateRange.to) {\n      toast.error(\"Please select a date range\")\n      return\n    }\n\n    if (selectedGrades.length === 0) {\n      toast.error(\"Please select at least one grade\")\n      return\n    }\n\n    if (generateBy !== \"grade\" && selectedSections.length === 0) {\n      toast.error(\"Please select at least one section\")\n      return\n    }\n\n    const newJobs = generateJobList()\n    setJobs(newJobs)\n    setIsGenerating(true)\n\n    // Simulate bulk generation process\n    for (let i = 0; i < newJobs.length; i++) {\n      const job = newJobs[i]\n      \n      // Update job status to generating\n      setJobs(prevJobs => \n        prevJobs.map(j => \n          j.id === job.id \n            ? { ...j, status: 'generating' as const }\n            : j\n        )\n      )\n\n      // Simulate generation progress\n      for (let progress = 0; progress <= 100; progress += 20) {\n        await new Promise(resolve => setTimeout(resolve, 200))\n        setJobs(prevJobs => \n          prevJobs.map(j => \n            j.id === job.id \n              ? { ...j, progress }\n              : j\n          )\n        )\n      }\n\n      // Mark as completed (or failed randomly for demo)\n      const isSuccess = Math.random() > 0.1 // 90% success rate\n      setJobs(prevJobs => \n        prevJobs.map(j => \n          j.id === job.id \n            ? { \n                ...j, \n                status: isSuccess ? 'completed' as const : 'failed' as const,\n                progress: 100,\n                generatedAt: isSuccess ? new Date().toISOString() : undefined,\n                error: isSuccess ? undefined : \"Generation failed due to data issues\"\n              }\n            : j\n        )\n      )\n    }\n\n    setIsGenerating(false)\n    \n    const successCount = newJobs.filter(j => Math.random() > 0.1).length\n    toast.success(`Bulk generation completed`, {\n      description: `${successCount} of ${newJobs.length} reports generated successfully`\n    })\n  }\n\n  const handleClearJobs = () => {\n    setJobs([])\n  }\n\n  const getStatusIcon = (status: BulkReportJob['status']) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"h-4 w-4 text-gray-500\" />\n      case 'generating':\n        return <Play className=\"h-4 w-4 text-blue-500 animate-spin\" />\n      case 'completed':\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n      case 'failed':\n        return <XCircle className=\"h-4 w-4 text-red-500\" />\n    }\n  }\n\n  const getStatusBadge = (status: BulkReportJob['status']) => {\n    const variants = {\n      pending: \"secondary\",\n      generating: \"default\", \n      completed: \"default\",\n      failed: \"destructive\"\n    } as const\n\n    const colors = {\n      pending: \"text-gray-600\",\n      generating: \"text-blue-600\",\n      completed: \"text-green-600\", \n      failed: \"text-red-600\"\n    } as const\n\n    return (\n      <Badge variant={variants[status]} className={colors[status]}>\n        {status.charAt(0).toUpperCase() + status.slice(1)}\n      </Badge>\n    )\n  }\n\n  const completedJobs = jobs.filter(j => j.status === 'completed').length\n  const totalJobs = jobs.length\n  const overallProgress = totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Header */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center gap-3\">\n            <div className=\"p-2 bg-purple-100 rounded-lg\">\n              <Layers className=\"h-6 w-6 text-purple-600\" />\n            </div>\n            <div>\n              <CardTitle>Bulk Report Generator</CardTitle>\n              <CardDescription>\n                Generate multiple reports at once for different grades and sections\n              </CardDescription>\n            </div>\n          </div>\n        </CardHeader>\n      </Card>\n\n      {/* Configuration */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Bulk Generation Settings</CardTitle>\n          <CardDescription>\n            Configure the reports you want to generate in bulk\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          {/* Report Type and Date Range */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>Report Type</Label>\n              <Select value={reportType} onValueChange={(value) => setReportType(value as ReportType)}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"SF2\">SF2 Daily Attendance</SelectItem>\n                  <SelectItem value=\"SF4\">SF4 Monthly Movement</SelectItem>\n                  <SelectItem value=\"DAILY\">Daily Summary</SelectItem>\n                  <SelectItem value=\"WEEKLY\">Weekly Summary</SelectItem>\n                  <SelectItem value=\"MONTHLY\">Monthly Summary</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>Generation Strategy</Label>\n              <Select value={generateBy} onValueChange={(value) => setGenerateBy(value as typeof generateBy)}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"grade\">One report per grade</SelectItem>\n                  <SelectItem value=\"section\">One report per section</SelectItem>\n                  <SelectItem value=\"both\">Both grade and section reports</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* Date Range */}\n          <div className=\"space-y-2\">\n            <Label>Date Range</Label>\n            <DateRangePicker\n              value={dateRange}\n              onChange={setDateRange}\n            />\n          </div>\n\n          {/* Grade Selection */}\n          <div className=\"space-y-3\">\n            <Label>Select Grades</Label>\n            <div className=\"grid grid-cols-3 md:grid-cols-6 gap-2\">\n              {gradeOptions.map((grade) => (\n                <div key={grade} className=\"flex items-center space-x-2\">\n                  <Checkbox\n                    id={`bulk-grade-${grade}`}\n                    checked={selectedGrades.includes(grade)}\n                    onCheckedChange={(checked) => handleGradeChange(grade, checked as boolean)}\n                  />\n                  <Label htmlFor={`bulk-grade-${grade}`} className=\"text-sm\">\n                    Grade {grade}\n                  </Label>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Section Selection */}\n          {generateBy !== \"grade\" && (\n            <div className=\"space-y-3\">\n              <Label>Select Sections</Label>\n              <div className=\"grid grid-cols-5 gap-2\">\n                {sectionOptions.map((section) => (\n                  <div key={section} className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id={`bulk-section-${section}`}\n                      checked={selectedSections.includes(section)}\n                      onCheckedChange={(checked) => handleSectionChange(section, checked as boolean)}\n                    />\n                    <Label htmlFor={`bulk-section-${section}`} className=\"text-sm\">\n                      Section {section}\n                    </Label>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Preview */}\n          <div className=\"p-4 bg-muted rounded-lg\">\n            <h4 className=\"font-medium mb-2\">Generation Preview</h4>\n            <p className=\"text-sm text-muted-foreground\">\n              This will generate <strong>{generateJobList().length}</strong> reports\n              {dateRange.from && dateRange.to && (\n                <> for the period from <strong>{format(dateRange.from, \"MMM dd, yyyy\")}</strong> to <strong>{format(dateRange.to, \"MMM dd, yyyy\")}</strong></>\n              )}\n            </p>\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex items-center gap-2\">\n            <Button \n              onClick={handleStartBulkGeneration}\n              disabled={isGenerating || selectedGrades.length === 0}\n            >\n              <Layers className=\"mr-2 h-4 w-4\" />\n              Start Bulk Generation\n            </Button>\n            {jobs.length > 0 && (\n              <Button variant=\"outline\" onClick={handleClearJobs} disabled={isGenerating}>\n                Clear Jobs\n              </Button>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Generation Progress */}\n      {jobs.length > 0 && (\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <CardTitle>Generation Progress</CardTitle>\n                <CardDescription>\n                  {completedJobs} of {totalJobs} reports completed\n                </CardDescription>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold\">{Math.round(overallProgress)}%</div>\n                <div className=\"text-sm text-muted-foreground\">Overall Progress</div>\n              </div>\n            </div>\n            <Progress value={overallProgress} className=\"w-full\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"rounded-md border\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>Report Name</TableHead>\n                    <TableHead>Status</TableHead>\n                    <TableHead>Progress</TableHead>\n                    <TableHead>Generated</TableHead>\n                    <TableHead className=\"text-right\">Actions</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {jobs.map((job) => (\n                    <TableRow key={job.id}>\n                      <TableCell>\n                        <div className=\"flex items-center gap-2\">\n                          {getStatusIcon(job.status)}\n                          <div>\n                            <div className=\"font-medium\">{job.name}</div>\n                            {job.error && (\n                              <div className=\"text-xs text-red-600\">{job.error}</div>\n                            )}\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        {getStatusBadge(job.status)}\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"flex items-center gap-2\">\n                          <Progress value={job.progress} className=\"w-20\" />\n                          <span className=\"text-sm\">{job.progress}%</span>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        {job.generatedAt ? (\n                          <div className=\"text-sm\">\n                            {format(new Date(job.generatedAt), \"HH:mm:ss\")}\n                          </div>\n                        ) : (\n                          <span className=\"text-sm text-muted-foreground\">-</span>\n                        )}\n                      </TableCell>\n                      <TableCell className=\"text-right\">\n                        {job.status === 'completed' && (\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <Download className=\"h-4 w-4\" />\n                          </Button>\n                        )}\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AA1BA;;;;;;;;;;;;;;;AAgDA,MAAM,eAAe;IAAC;IAAK;IAAK;IAAK;IAAM;IAAM;CAAK;AACtD,MAAM,iBAAiB;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI;AAEzC,SAAS,oBAAoB,EAAE,UAAU,EAAE,SAAS,EAA4B;IACrF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM,IAAI;QACV,IAAI,IAAI;IACV;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAC3E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,oBAAoB,CAAC,OAAe;QACxC,IAAI,SAAS;YACX,kBAAkB;mBAAI;gBAAgB;aAAM;QAC9C,OAAO;YACL,kBAAkB,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM;QACrD;IACF;IAEA,MAAM,sBAAsB,CAAC,SAAiB;QAC5C,IAAI,SAAS;YACX,oBAAoB;mBAAI;gBAAkB;aAAQ;QACpD,OAAO;YACL,oBAAoB,iBAAiB,MAAM,CAAC,CAAA,IAAK,MAAM;QACzD;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,OAAwB,EAAE;QAEhC,IAAI,eAAe,SAAS;YAC1B,eAAe,OAAO,CAAC,CAAA;gBACrB,KAAK,IAAI,CAAC;oBACR,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,OAAO;oBACjC,MAAM,GAAG,WAAW,gBAAgB,EAAE,OAAO;oBAC7C,MAAM;oBACN;oBACA,SAAS;wBAAE,QAAQ;4BAAC;yBAAM;oBAAC;oBAC3B,QAAQ;oBACR,UAAU;gBACZ;YACF;QACF,OAAO,IAAI,eAAe,WAAW;YACnC,eAAe,OAAO,CAAC,CAAA;gBACrB,iBAAiB,OAAO,CAAC,CAAA;oBACvB,KAAK,IAAI,CAAC;wBACR,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS;wBAC5C,MAAM,GAAG,WAAW,gBAAgB,EAAE,MAAM,CAAC,EAAE,SAAS;wBACxD,MAAM;wBACN;wBACA,SAAS;4BAAE,QAAQ;gCAAC;6BAAM;4BAAE,UAAU;gCAAC;6BAAQ;wBAAC;wBAChD,QAAQ;wBACR,UAAU;oBACZ;gBACF;YACF;QACF,OAAO;YACL,gCAAgC;YAChC,eAAe,OAAO,CAAC,CAAA;gBACrB,KAAK,IAAI,CAAC;oBACR,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,OAAO,EAAE,OAAO;oBACvC,MAAM,GAAG,WAAW,gBAAgB,EAAE,MAAM,eAAe,CAAC;oBAC5D,MAAM;oBACN;oBACA,SAAS;wBAAE,QAAQ;4BAAC;yBAAM;oBAAC;oBAC3B,QAAQ;oBACR,UAAU;gBACZ;YACF;YAEA,oDAAoD;YACpD,eAAe,OAAO,CAAC,CAAA;gBACrB,iBAAiB,OAAO,CAAC,CAAA;oBACvB,KAAK,IAAI,CAAC;wBACR,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,SAAS,EAAE,MAAM,CAAC,EAAE,SAAS;wBACpD,MAAM,GAAG,WAAW,gBAAgB,EAAE,MAAM,CAAC,EAAE,SAAS;wBACxD,MAAM;wBACN;wBACA,SAAS;4BAAE,QAAQ;gCAAC;6BAAM;4BAAE,UAAU;gCAAC;6BAAQ;wBAAC;wBAChD,QAAQ;wBACR,UAAU;oBACZ;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,4BAA4B;QAChC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACpC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,eAAe,WAAW,iBAAiB,MAAM,KAAK,GAAG;YAC3D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,UAAU;QAChB,QAAQ;QACR,gBAAgB;QAEhB,mCAAmC;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,MAAM,OAAO,CAAC,EAAE;YAEtB,kCAAkC;YAClC,QAAQ,CAAA,WACN,SAAS,GAAG,CAAC,CAAA,IACX,EAAE,EAAE,KAAK,IAAI,EAAE,GACX;wBAAE,GAAG,CAAC;wBAAE,QAAQ;oBAAsB,IACtC;YAIR,+BAA+B;YAC/B,IAAK,IAAI,WAAW,GAAG,YAAY,KAAK,YAAY,GAAI;gBACtD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,QAAQ,CAAA,WACN,SAAS,GAAG,CAAC,CAAA,IACX,EAAE,EAAE,KAAK,IAAI,EAAE,GACX;4BAAE,GAAG,CAAC;4BAAE;wBAAS,IACjB;YAGV;YAEA,kDAAkD;YAClD,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI,mBAAmB;;YACzD,QAAQ,CAAA,WACN,SAAS,GAAG,CAAC,CAAA,IACX,EAAE,EAAE,KAAK,IAAI,EAAE,GACX;wBACE,GAAG,CAAC;wBACJ,QAAQ,YAAY,cAAuB;wBAC3C,UAAU;wBACV,aAAa,YAAY,IAAI,OAAO,WAAW,KAAK;wBACpD,OAAO,YAAY,YAAY;oBACjC,IACA;QAGV;QAEA,gBAAgB;QAEhB,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,KAAK,MAAM,KAAK,KAAK,MAAM;QACpE,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,yBAAyB,CAAC,EAAE;YACzC,aAAa,GAAG,aAAa,IAAI,EAAE,QAAQ,MAAM,CAAC,+BAA+B,CAAC;QACpF;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ,EAAE;IACZ;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QAC9B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW;YACf,SAAS;YACT,YAAY;YACZ,WAAW;YACX,QAAQ;QACV;QAEA,MAAM,SAAS;YACb,SAAS;YACT,YAAY;YACZ,WAAW;YACX,QAAQ;QACV;QAEA,qBACE,8OAAC,0HAAA,CAAA,QAAK;YAAC,SAAS,QAAQ,CAAC,OAAO;YAAE,WAAW,MAAM,CAAC,OAAO;sBACxD,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;;;;;;IAGrD;IAEA,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;IACvE,MAAM,YAAY,KAAK,MAAM;IAC7B,MAAM,kBAAkB,YAAY,IAAI,AAAC,gBAAgB,YAAa,MAAM;IAE5E,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,yHAAA,CAAA,aAAU;8BACT,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,8OAAC;;kDACC,8OAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,yHAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzB,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;;0CACT,8OAAC,yHAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,yHAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,2HAAA,CAAA,SAAM;gDAAC,OAAO;gDAAY,eAAe,CAAC,QAAU,cAAc;;kEACjE,8OAAC,2HAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,2HAAA,CAAA,gBAAa;;0EACZ,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAKlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC,2HAAA,CAAA,SAAM;gDAAC,OAAO;gDAAY,eAAe,CAAC,QAAU,cAAc;;kEACjE,8OAAC,2HAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,2HAAA,CAAA,gBAAa;;0EACZ,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC,4IAAA,CAAA,kBAAe;wCACd,OAAO;wCACP,UAAU;;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC,6HAAA,CAAA,WAAQ;wDACP,IAAI,CAAC,WAAW,EAAE,OAAO;wDACzB,SAAS,eAAe,QAAQ,CAAC;wDACjC,iBAAiB,CAAC,UAAY,kBAAkB,OAAO;;;;;;kEAEzD,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAS,CAAC,WAAW,EAAE,OAAO;wDAAE,WAAU;;4DAAU;4DAClD;;;;;;;;+CAPD;;;;;;;;;;;;;;;;4BAef,eAAe,yBACd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;gDAAkB,WAAU;;kEAC3B,8OAAC,6HAAA,CAAA,WAAQ;wDACP,IAAI,CAAC,aAAa,EAAE,SAAS;wDAC7B,SAAS,iBAAiB,QAAQ,CAAC;wDACnC,iBAAiB,CAAC,UAAY,oBAAoB,SAAS;;;;;;kEAE7D,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAS,CAAC,aAAa,EAAE,SAAS;wDAAE,WAAU;;4DAAU;4DACpD;;;;;;;;+CAPH;;;;;;;;;;;;;;;;0CAgBlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,8OAAC;wCAAE,WAAU;;4CAAgC;0DACxB,8OAAC;0DAAQ,kBAAkB,MAAM;;;;;;4CAAU;4CAC7D,UAAU,IAAI,IAAI,UAAU,EAAE,kBAC7B;;oDAAE;kEAAqB,8OAAC;kEAAQ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,IAAI,EAAE;;;;;;oDAAyB;kEAAI,8OAAC;kEAAQ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;0CAMxH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,gBAAgB,eAAe,MAAM,KAAK;;0DAEpD,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAGpC,KAAK,MAAM,GAAG,mBACb,8OAAC,2HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;wCAAiB,UAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;YASnF,KAAK,MAAM,GAAG,mBACb,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;;0CACT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC,yHAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,yHAAA,CAAA,kBAAe;;oDACb;oDAAc;oDAAK;oDAAU;;;;;;;;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDAAsB,KAAK,KAAK,CAAC;oDAAiB;;;;;;;0DACjE,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAGnD,8OAAC,6HAAA,CAAA,WAAQ;gCAAC,OAAO;gCAAiB,WAAU;;;;;;;;;;;;kCAE9C,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;kDACJ,8OAAC,0HAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;;8DACP,8OAAC,0HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,0HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,0HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,0HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,0HAAA,CAAA,YAAS;oDAAC,WAAU;8DAAa;;;;;;;;;;;;;;;;;kDAGtC,8OAAC,0HAAA,CAAA,YAAS;kDACP,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC,0HAAA,CAAA,WAAQ;;kEACP,8OAAC,0HAAA,CAAA,YAAS;kEACR,cAAA,8OAAC;4DAAI,WAAU;;gEACZ,cAAc,IAAI,MAAM;8EACzB,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAe,IAAI,IAAI;;;;;;wEACrC,IAAI,KAAK,kBACR,8OAAC;4EAAI,WAAU;sFAAwB,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;kEAKxD,8OAAC,0HAAA,CAAA,YAAS;kEACP,eAAe,IAAI,MAAM;;;;;;kEAE5B,8OAAC,0HAAA,CAAA,YAAS;kEACR,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,6HAAA,CAAA,WAAQ;oEAAC,OAAO,IAAI,QAAQ;oEAAE,WAAU;;;;;;8EACzC,8OAAC;oEAAK,WAAU;;wEAAW,IAAI,QAAQ;wEAAC;;;;;;;;;;;;;;;;;;kEAG5C,8OAAC,0HAAA,CAAA,YAAS;kEACP,IAAI,WAAW,iBACd,8OAAC;4DAAI,WAAU;sEACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,IAAI,WAAW,GAAG;;;;;iFAGrC,8OAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;kEAGpD,8OAAC,0HAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,IAAI,MAAM,KAAK,6BACd,8OAAC,2HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;+CAjCb,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CzC", "debugId": null}}, {"offset": {"line": 9275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/report-analytics.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { mockGeneratedReports, mockReportAnalytics } from \"@/lib/data/reports-mock-data\"\nimport { \n  BarChart3, \n  TrendingUp, \n  TrendingDown, \n  Users, \n  FileText, \n  Download, \n  Clock,\n  Calendar,\n  Eye,\n  Share\n} from \"lucide-react\"\nimport { format, subDays, subMonths } from \"date-fns\"\nimport { useState } from \"react\"\n\ninterface ReportAnalyticsProps {\n  className?: string\n}\n\nexport function ReportAnalytics({ className }: ReportAnalyticsProps) {\n  const [timeRange, setTimeRange] = useState<\"7d\" | \"30d\" | \"90d\" | \"1y\">(\"30d\")\n\n  // Mock analytics data based on time range\n  const getAnalyticsData = () => {\n    const baseData = mockReportAnalytics\n    \n    // Simulate different data based on time range\n    const multipliers = {\n      \"7d\": 0.2,\n      \"30d\": 1,\n      \"90d\": 3,\n      \"1y\": 12\n    }\n    \n    const multiplier = multipliers[timeRange]\n    \n    return {\n      ...baseData,\n      totalReports: Math.round(baseData.totalReports * multiplier),\n      totalDownloads: Math.round(baseData.totalDownloads * multiplier),\n      uniqueUsers: Math.round(baseData.uniqueUsers * multiplier * 0.8),\n      averageGenerationTime: baseData.averageGenerationTime,\n      reportTypeDistribution: baseData.reportTypeDistribution.map(item => ({\n        ...item,\n        count: Math.round(item.count * multiplier)\n      })),\n      dailyActivity: baseData.dailyActivity.map(item => ({\n        ...item,\n        reports: Math.round(item.reports * multiplier * 0.1),\n        downloads: Math.round(item.downloads * multiplier * 0.1)\n      }))\n    }\n  }\n\n  const analytics = getAnalyticsData()\n\n  // Calculate trends (mock data)\n  const getTrendPercentage = (current: number, previous: number) => {\n    if (previous === 0) return 0\n    return ((current - previous) / previous) * 100\n  }\n\n  const reportsTrend = getTrendPercentage(analytics.totalReports, analytics.totalReports * 0.8)\n  const downloadsTrend = getTrendPercentage(analytics.totalDownloads, analytics.totalDownloads * 0.9)\n  const usersTrend = getTrendPercentage(analytics.uniqueUsers, analytics.uniqueUsers * 0.85)\n\n  const formatTrend = (trend: number) => {\n    const isPositive = trend >= 0\n    return {\n      value: Math.abs(trend).toFixed(1),\n      isPositive,\n      icon: isPositive ? TrendingUp : TrendingDown,\n      color: isPositive ? \"text-green-600\" : \"text-red-600\"\n    }\n  }\n\n  const getTimeRangeLabel = () => {\n    const labels = {\n      \"7d\": \"Last 7 days\",\n      \"30d\": \"Last 30 days\", \n      \"90d\": \"Last 90 days\",\n      \"1y\": \"Last year\"\n    }\n    return labels[timeRange]\n  }\n\n  // Most popular reports\n  const popularReports = mockGeneratedReports\n    .sort((a, b) => b.downloadCount - a.downloadCount)\n    .slice(0, 5)\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold flex items-center gap-2\">\n            <BarChart3 className=\"h-6 w-6\" />\n            Report Analytics\n          </h2>\n          <p className=\"text-muted-foreground\">\n            Track report usage, performance, and trends\n          </p>\n        </div>\n        <Select value={timeRange} onValueChange={(value) => setTimeRange(value as typeof timeRange)}>\n          <SelectTrigger className=\"w-40\">\n            <SelectValue />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"7d\">Last 7 days</SelectItem>\n            <SelectItem value=\"30d\">Last 30 days</SelectItem>\n            <SelectItem value=\"90d\">Last 90 days</SelectItem>\n            <SelectItem value=\"1y\">Last year</SelectItem>\n          </SelectContent>\n        </Select>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Total Reports</p>\n                <p className=\"text-2xl font-bold\">{analytics.totalReports.toLocaleString()}</p>\n                <div className=\"flex items-center gap-1 text-xs\">\n                  {(() => {\n                    const trend = formatTrend(reportsTrend)\n                    const Icon = trend.icon\n                    return (\n                      <>\n                        <Icon className={`h-3 w-3 ${trend.color}`} />\n                        <span className={trend.color}>\n                          {trend.value}% vs previous period\n                        </span>\n                      </>\n                    )\n                  })()}\n                </div>\n              </div>\n              <FileText className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Total Downloads</p>\n                <p className=\"text-2xl font-bold\">{analytics.totalDownloads.toLocaleString()}</p>\n                <div className=\"flex items-center gap-1 text-xs\">\n                  {(() => {\n                    const trend = formatTrend(downloadsTrend)\n                    const Icon = trend.icon\n                    return (\n                      <>\n                        <Icon className={`h-3 w-3 ${trend.color}`} />\n                        <span className={trend.color}>\n                          {trend.value}% vs previous period\n                        </span>\n                      </>\n                    )\n                  })()}\n                </div>\n              </div>\n              <Download className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Active Users</p>\n                <p className=\"text-2xl font-bold\">{analytics.uniqueUsers}</p>\n                <div className=\"flex items-center gap-1 text-xs\">\n                  {(() => {\n                    const trend = formatTrend(usersTrend)\n                    const Icon = trend.icon\n                    return (\n                      <>\n                        <Icon className={`h-3 w-3 ${trend.color}`} />\n                        <span className={trend.color}>\n                          {trend.value}% vs previous period\n                        </span>\n                      </>\n                    )\n                  })()}\n                </div>\n              </div>\n              <Users className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Avg Generation Time</p>\n                <p className=\"text-2xl font-bold\">{analytics.averageGenerationTime}s</p>\n                <div className=\"flex items-center gap-1 text-xs text-green-600\">\n                  <TrendingDown className=\"h-3 w-3\" />\n                  <span>12% faster</span>\n                </div>\n              </div>\n              <Clock className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Report Type Distribution */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Report Type Distribution</CardTitle>\n            <CardDescription>\n              Breakdown of reports by type for {getTimeRangeLabel().toLowerCase()}\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {analytics.reportTypeDistribution.map((item) => {\n                const percentage = analytics.totalReports > 0 \n                  ? (item.count / analytics.totalReports) * 100 \n                  : 0\n                \n                return (\n                  <div key={item.type} className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`w-3 h-3 rounded-full ${\n                        item.type === 'SF2' ? 'bg-blue-500' :\n                        item.type === 'SF4' ? 'bg-green-500' :\n                        item.type === 'DAILY' ? 'bg-purple-500' :\n                        item.type === 'WEEKLY' ? 'bg-orange-500' :\n                        item.type === 'MONTHLY' ? 'bg-pink-500' :\n                        'bg-gray-500'\n                      }`} />\n                      <span className=\"text-sm font-medium\">{item.type}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <span className=\"text-sm text-muted-foreground\">\n                        {item.count.toLocaleString()}\n                      </span>\n                      <Badge variant=\"secondary\" className=\"text-xs\">\n                        {percentage.toFixed(1)}%\n                      </Badge>\n                    </div>\n                  </div>\n                )\n              })}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Most Popular Reports</CardTitle>\n            <CardDescription>\n              Reports with the highest download counts\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {popularReports.map((report, index) => (\n                <div key={report.id} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"flex items-center justify-center w-6 h-6 rounded-full bg-muted text-xs font-medium\">\n                      {index + 1}\n                    </div>\n                    <div>\n                      <div className=\"text-sm font-medium\">{report.config.name}</div>\n                      <div className=\"text-xs text-muted-foreground\">\n                        {format(new Date(report.generatedAt), \"MMM dd, yyyy\")}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <Download className=\"h-3 w-3 text-muted-foreground\" />\n                    <span className=\"text-sm\">{report.downloadCount}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Daily Activity Chart */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Daily Activity</CardTitle>\n          <CardDescription>\n            Report generation and download activity over time\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {/* Simple bar chart representation */}\n            <div className=\"grid grid-cols-7 gap-2\">\n              {analytics.dailyActivity.slice(-7).map((day, index) => {\n                const maxValue = Math.max(...analytics.dailyActivity.map(d => d.reports + d.downloads))\n                const totalActivity = day.reports + day.downloads\n                const height = maxValue > 0 ? (totalActivity / maxValue) * 100 : 0\n                \n                return (\n                  <div key={index} className=\"flex flex-col items-center gap-2\">\n                    <div className=\"w-full bg-muted rounded-t\" style={{ height: '100px' }}>\n                      <div \n                        className=\"w-full bg-primary rounded-t transition-all\"\n                        style={{ height: `${height}%`, marginTop: `${100 - height}%` }}\n                      />\n                    </div>\n                    <div className=\"text-xs text-center\">\n                      <div className=\"font-medium\">{format(new Date(day.date), \"MMM dd\")}</div>\n                      <div className=\"text-muted-foreground\">{totalActivity}</div>\n                    </div>\n                  </div>\n                )\n              })}\n            </div>\n            \n            <div className=\"flex items-center justify-center gap-6 text-sm\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-3 h-3 bg-primary rounded\" />\n                <span>Total Activity</span>\n              </div>\n              <div className=\"text-muted-foreground\">\n                Last 7 days activity\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Performance Insights */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Performance Insights</CardTitle>\n          <CardDescription>\n            Key insights and recommendations\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"space-y-4\">\n              <h4 className=\"font-medium\">Key Insights</h4>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\" />\n                  <div>\n                    <p className=\"text-sm font-medium\">SF2 reports are most popular</p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {analytics.reportTypeDistribution.find(r => r.type === 'SF2')?.count || 0} generated this period\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\" />\n                  <div>\n                    <p className=\"text-sm font-medium\">Peak usage on weekdays</p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      Monday-Friday show highest activity\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-orange-500 rounded-full mt-2\" />\n                  <div>\n                    <p className=\"text-sm font-medium\">Generation time improved</p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      12% faster than previous period\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"space-y-4\">\n              <h4 className=\"font-medium\">Recommendations</h4>\n              <div className=\"space-y-3\">\n                <div className=\"p-3 bg-blue-50 rounded-lg\">\n                  <p className=\"text-sm font-medium text-blue-800\">Optimize SF2 templates</p>\n                  <p className=\"text-xs text-blue-600\">\n                    Consider creating more SF2 template variations\n                  </p>\n                </div>\n                \n                <div className=\"p-3 bg-green-50 rounded-lg\">\n                  <p className=\"text-sm font-medium text-green-800\">Schedule bulk reports</p>\n                  <p className=\"text-xs text-green-600\">\n                    Use bulk generation for recurring reports\n                  </p>\n                </div>\n                \n                <div className=\"p-3 bg-purple-50 rounded-lg\">\n                  <p className=\"text-sm font-medium text-purple-800\">Archive old reports</p>\n                  <p className=\"text-xs text-purple-600\">\n                    Set up automatic archival for reports older than 90 days\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AApBA;;;;;;;;;AA0BO,SAAS,gBAAgB,EAAE,SAAS,EAAwB;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAExE,0CAA0C;IAC1C,MAAM,mBAAmB;QACvB,MAAM,WAAW,sIAAA,CAAA,sBAAmB;QAEpC,8CAA8C;QAC9C,MAAM,cAAc;YAClB,MAAM;YACN,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAEA,MAAM,aAAa,WAAW,CAAC,UAAU;QAEzC,OAAO;YACL,GAAG,QAAQ;YACX,cAAc,KAAK,KAAK,CAAC,SAAS,YAAY,GAAG;YACjD,gBAAgB,KAAK,KAAK,CAAC,SAAS,cAAc,GAAG;YACrD,aAAa,KAAK,KAAK,CAAC,SAAS,WAAW,GAAG,aAAa;YAC5D,uBAAuB,SAAS,qBAAqB;YACrD,wBAAwB,SAAS,sBAAsB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACnE,GAAG,IAAI;oBACP,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG;gBACjC,CAAC;YACD,eAAe,SAAS,aAAa,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACjD,GAAG,IAAI;oBACP,SAAS,KAAK,KAAK,CAAC,KAAK,OAAO,GAAG,aAAa;oBAChD,WAAW,KAAK,KAAK,CAAC,KAAK,SAAS,GAAG,aAAa;gBACtD,CAAC;QACH;IACF;IAEA,MAAM,YAAY;IAElB,+BAA+B;IAC/B,MAAM,qBAAqB,CAAC,SAAiB;QAC3C,IAAI,aAAa,GAAG,OAAO;QAC3B,OAAO,AAAC,CAAC,UAAU,QAAQ,IAAI,WAAY;IAC7C;IAEA,MAAM,eAAe,mBAAmB,UAAU,YAAY,EAAE,UAAU,YAAY,GAAG;IACzF,MAAM,iBAAiB,mBAAmB,UAAU,cAAc,EAAE,UAAU,cAAc,GAAG;IAC/F,MAAM,aAAa,mBAAmB,UAAU,WAAW,EAAE,UAAU,WAAW,GAAG;IAErF,MAAM,cAAc,CAAC;QACnB,MAAM,aAAa,SAAS;QAC5B,OAAO;YACL,OAAO,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC;YAC/B;YACA,MAAM,aAAa,kNAAA,CAAA,aAAU,GAAG,sNAAA,CAAA,eAAY;YAC5C,OAAO,aAAa,mBAAmB;QACzC;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,SAAS;YACb,MAAM;YACN,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA,OAAO,MAAM,CAAC,UAAU;IAC1B;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,sIAAA,CAAA,uBAAoB,CACxC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa,EAChD,KAAK,CAAC,GAAG;IAEZ,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGnC,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC,2HAAA,CAAA,SAAM;wBAAC,OAAO;wBAAW,eAAe,CAAC,QAAU,aAAa;;0CAC/D,8OAAC,2HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;;;;;;;;;;0CAEd,8OAAC,2HAAA,CAAA,gBAAa;;kDACZ,8OAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,8OAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,8OAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,8OAAC,2HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsB,UAAU,YAAY,CAAC,cAAc;;;;;;0DACxE,8OAAC;gDAAI,WAAU;0DACZ,CAAC;oDACA,MAAM,QAAQ,YAAY;oDAC1B,MAAM,OAAO,MAAM,IAAI;oDACvB,qBACE;;0EACE,8OAAC;gEAAK,WAAW,CAAC,QAAQ,EAAE,MAAM,KAAK,EAAE;;;;;;0EACzC,8OAAC;gEAAK,WAAW,MAAM,KAAK;;oEACzB,MAAM,KAAK;oEAAC;;;;;;;;;gDAIrB,CAAC;;;;;;;;;;;;kDAGL,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,8OAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsB,UAAU,cAAc,CAAC,cAAc;;;;;;0DAC1E,8OAAC;gDAAI,WAAU;0DACZ,CAAC;oDACA,MAAM,QAAQ,YAAY;oDAC1B,MAAM,OAAO,MAAM,IAAI;oDACvB,qBACE;;0EACE,8OAAC;gEAAK,WAAW,CAAC,QAAQ,EAAE,MAAM,KAAK,EAAE;;;;;;0EACzC,8OAAC;gEAAK,WAAW,MAAM,KAAK;;oEACzB,MAAM,KAAK;oEAAC;;;;;;;;;gDAIrB,CAAC;;;;;;;;;;;;kDAGL,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,8OAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DAAsB,UAAU,WAAW;;;;;;0DACxD,8OAAC;gDAAI,WAAU;0DACZ,CAAC;oDACA,MAAM,QAAQ,YAAY;oDAC1B,MAAM,OAAO,MAAM,IAAI;oDACvB,qBACE;;0EACE,8OAAC;gEAAK,WAAW,CAAC,QAAQ,EAAE,MAAM,KAAK,EAAE;;;;;;0EACzC,8OAAC;gEAAK,WAAW,MAAM,KAAK;;oEACzB,MAAM,KAAK;oEAAC;;;;;;;;;gDAIrB,CAAC;;;;;;;;;;;;kDAGL,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKvB,8OAAC,yHAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;;oDAAsB,UAAU,qBAAqB;oDAAC;;;;;;;0DACnE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAGV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;;kDACT,8OAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,yHAAA,CAAA,kBAAe;;4CAAC;4CACmB,oBAAoB,WAAW;;;;;;;;;;;;;0CAGrE,8OAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,UAAU,sBAAsB,CAAC,GAAG,CAAC,CAAC;wCACrC,MAAM,aAAa,UAAU,YAAY,GAAG,IACxC,AAAC,KAAK,KAAK,GAAG,UAAU,YAAY,GAAI,MACxC;wCAEJ,qBACE,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,qBAAqB,EACpC,KAAK,IAAI,KAAK,QAAQ,gBACtB,KAAK,IAAI,KAAK,QAAQ,iBACtB,KAAK,IAAI,KAAK,UAAU,kBACxB,KAAK,IAAI,KAAK,WAAW,kBACzB,KAAK,IAAI,KAAK,YAAY,gBAC1B,eACA;;;;;;sEACF,8OAAC;4DAAK,WAAU;sEAAuB,KAAK,IAAI;;;;;;;;;;;;8DAElD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,KAAK,KAAK,CAAC,cAAc;;;;;;sEAE5B,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;;gEAClC,WAAW,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;2CAjBnB,KAAK,IAAI;;;;;oCAsBvB;;;;;;;;;;;;;;;;;kCAKN,8OAAC,yHAAA,CAAA,OAAI;;0CACH,8OAAC,yHAAA,CAAA,aAAU;;kDACT,8OAAC,yHAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,yHAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,yHAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC;4CAAoB,WAAU;;8DAC7B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ,QAAQ;;;;;;sEAEX,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAAuB,OAAO,MAAM,CAAC,IAAI;;;;;;8EACxD,8OAAC;oEAAI,WAAU;8EACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,WAAW,GAAG;;;;;;;;;;;;;;;;;;8DAI5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAW,OAAO,aAAa;;;;;;;;;;;;;2CAdzC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAwB7B,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;;0CACT,8OAAC,yHAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,yHAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,UAAU,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK;wCAC3C,MAAM,WAAW,KAAK,GAAG,IAAI,UAAU,aAAa,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,GAAG,EAAE,SAAS;wCACrF,MAAM,gBAAgB,IAAI,OAAO,GAAG,IAAI,SAAS;wCACjD,MAAM,SAAS,WAAW,IAAI,AAAC,gBAAgB,WAAY,MAAM;wCAEjE,qBACE,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;oDAA4B,OAAO;wDAAE,QAAQ;oDAAQ;8DAClE,cAAA,8OAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,QAAQ,GAAG,OAAO,CAAC,CAAC;4DAAE,WAAW,GAAG,MAAM,OAAO,CAAC,CAAC;wDAAC;;;;;;;;;;;8DAGjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAe,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG;;;;;;sEACzD,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;;2CATlC;;;;;oCAad;;;;;;8CAGF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/C,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;;0CACT,8OAAC,yHAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,yHAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,yHAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAsB;;;;;;8EACnC,8OAAC;oEAAE,WAAU;;wEACV,UAAU,sBAAsB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,SAAS;wEAAE;;;;;;;;;;;;;;;;;;;8DAKhF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAsB;;;;;;8EACnC,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;;;;;;;8DAMjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAsB;;;;;;8EACnC,8OAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQrD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAKvC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAqC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAyB;;;;;;;;;;;;8DAKxC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAsC;;;;;;sEACnD,8OAAC;4DAAE,WAAU;sEAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzD", "debugId": null}}]}