(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/lib/data/reports-mock-data.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "getReportById": ()=>getReportById,
    "getReportsByStatus": ()=>getReportsByStatus,
    "getReportsByType": ()=>getReportsByType,
    "getScheduleById": ()=>getScheduleById,
    "getTeacherById": ()=>getTeacherById,
    "getTemplateById": ()=>getTemplateById,
    "mockGeneratedReports": ()=>mockGeneratedReports,
    "mockReportAnalytics": ()=>mockReportAnalytics,
    "mockReportTemplates": ()=>mockReportTemplates,
    "mockScheduledReports": ()=>mockScheduledReports,
    "mockTeachers": ()=>mockTeachers,
    "schoolInfo": ()=>schoolInfo
});
const schoolInfo = {
    schoolId: "301216",
    schoolName: "Tanauan School of Arts and Trade",
    address: "Tanauan City, Batangas, Philippines",
    district: "Tanauan City District",
    division: "Schools Division of Batangas",
    region: "Region IV-A (CALABARZON)",
    principalName: "Dr. Maria Elena Rodriguez",
    schoolYear: "2024-2025",
    semester: "1st Semester",
    quarter: "2nd Quarter"
};
const mockTeachers = [
    {
        id: "TCH001",
        name: "Prof. Juan Carlos Santos",
        position: "Subject Teacher - Mathematics"
    },
    {
        id: "TCH002",
        name: "Mrs. Ana Marie Reyes",
        position: "Subject Teacher - English"
    },
    {
        id: "TCH003",
        name: "Mr. Jose Miguel Torres",
        position: "Subject Teacher - Science"
    },
    {
        id: "TCH004",
        name: "Ms. Princess Mae Garcia",
        position: "Subject Teacher - Filipino"
    }
];
const mockGeneratedReports = [
    {
        id: "RPT001",
        config: {
            id: "CFG001",
            name: "Daily Attendance Report - Grade 7A",
            type: "SF2",
            description: "SF2 Daily Attendance Report for Grade 7-A",
            dateRange: {
                startDate: "2025-01-02",
                endDate: "2025-01-02"
            },
            filters: {
                grades: [
                    "7"
                ],
                sections: [
                    "Grade 7-A"
                ]
            },
            settings: {
                includePhotos: false,
                includeSignatures: true,
                pageOrientation: "portrait",
                fontSize: "medium"
            },
            createdBy: "admin",
            createdAt: "2025-01-02T08:00:00Z",
            lastModified: "2025-01-02T08:00:00Z"
        },
        status: "READY",
        generatedAt: "2025-01-02T08:15:00Z",
        fileSize: 245760,
        downloadCount: 12,
        metadata: {
            totalStudents: 35,
            totalRecords: 35,
            dateRange: {
                startDate: "2025-01-02",
                endDate: "2025-01-02"
            },
            statistics: {
                presentCount: 32,
                lateCount: 2,
                absentCount: 1,
                attendanceRate: 94.3
            },
            schoolInfo
        }
    },
    {
        id: "RPT002",
        config: {
            id: "CFG002",
            name: "Monthly Learner's Movement - December 2024",
            type: "SF4",
            description: "SF4 Monthly Learner's Movement Report for December 2024",
            dateRange: {
                startDate: "2024-12-01",
                endDate: "2024-12-31"
            },
            filters: {
                grades: [
                    "7",
                    "8",
                    "9",
                    "10",
                    "11",
                    "12"
                ]
            },
            settings: {
                includeSignatures: true,
                pageOrientation: "landscape",
                fontSize: "small"
            },
            createdBy: "principal",
            createdAt: "2025-01-01T09:00:00Z",
            lastModified: "2025-01-01T09:00:00Z"
        },
        status: "READY",
        generatedAt: "2025-01-01T09:30:00Z",
        fileSize: 512000,
        downloadCount: 8,
        metadata: {
            totalStudents: 1234,
            totalRecords: 24680,
            dateRange: {
                startDate: "2024-12-01",
                endDate: "2024-12-31"
            },
            statistics: {
                presentCount: 22145,
                lateCount: 1235,
                absentCount: 1300,
                attendanceRate: 89.7
            },
            schoolInfo
        }
    },
    {
        id: "RPT003",
        config: {
            id: "CFG003",
            name: "Weekly Attendance Summary",
            type: "WEEKLY",
            description: "Weekly attendance summary for all grades",
            dateRange: {
                startDate: "2024-12-30",
                endDate: "2025-01-03"
            },
            filters: {},
            settings: {
                showStatistics: true,
                groupBy: "grade",
                pageOrientation: "landscape"
            },
            createdBy: "admin",
            createdAt: "2025-01-03T16:00:00Z",
            lastModified: "2025-01-03T16:00:00Z"
        },
        status: "GENERATING",
        generatedAt: "2025-01-03T16:15:00Z",
        downloadCount: 0,
        metadata: {
            totalStudents: 1234,
            totalRecords: 6170,
            dateRange: {
                startDate: "2024-12-30",
                endDate: "2025-01-03"
            },
            statistics: {
                presentCount: 5540,
                lateCount: 310,
                absentCount: 320,
                attendanceRate: 89.8
            },
            schoolInfo
        }
    }
];
const mockReportTemplates = [
    {
        id: "TPL001",
        name: "SF2 Daily Attendance",
        type: "SF2",
        description: "Standard SF2 Daily Attendance Report template",
        config: {
            type: "SF2",
            settings: {
                includeSignatures: true,
                pageOrientation: "portrait",
                fontSize: "medium",
                includeHeader: true,
                includeFooter: true
            }
        },
        isDefault: true,
        isPublic: true,
        createdBy: "system",
        createdAt: "2024-01-01T00:00:00Z"
    },
    {
        id: "TPL002",
        name: "SF4 Monthly Movement",
        type: "SF4",
        description: "Standard SF4 Monthly Learner's Movement Report template",
        config: {
            type: "SF4",
            settings: {
                includeSignatures: true,
                pageOrientation: "landscape",
                fontSize: "small",
                includeHeader: true,
                includeFooter: true
            }
        },
        isDefault: true,
        isPublic: true,
        createdBy: "system",
        createdAt: "2024-01-01T00:00:00Z"
    },
    {
        id: "TPL003",
        name: "Chronic Absenteeism Report",
        type: "CUSTOM",
        description: "Custom report for identifying students with chronic absenteeism",
        config: {
            type: "CUSTOM",
            filters: {
                attendanceStatus: [
                    "Absent"
                ]
            },
            settings: {
                showStatistics: true,
                groupBy: "grade",
                sortBy: "attendance"
            }
        },
        isDefault: false,
        isPublic: true,
        createdBy: "admin",
        createdAt: "2024-06-15T10:00:00Z"
    }
];
const mockScheduledReports = [
    {
        id: "SCH001",
        reportConfigId: "CFG001",
        name: "Daily SF2 Reports - All Grades",
        frequency: "daily",
        time: "07:00",
        timezone: "Asia/Manila",
        isActive: true,
        nextRun: "2025-01-03T07:00:00Z",
        recipients: [
            "<EMAIL>",
            "<EMAIL>"
        ],
        createdBy: "admin",
        createdAt: "2024-08-01T00:00:00Z"
    },
    {
        id: "SCH002",
        reportConfigId: "CFG002",
        name: "Monthly SF4 Reports",
        frequency: "monthly",
        dayOfMonth: 1,
        time: "09:00",
        timezone: "Asia/Manila",
        isActive: true,
        lastRun: "2025-01-01T09:00:00Z",
        nextRun: "2025-02-01T09:00:00Z",
        recipients: [
            "<EMAIL>",
            "<EMAIL>"
        ],
        createdBy: "principal",
        createdAt: "2024-08-01T00:00:00Z"
    }
];
const mockReportAnalytics = [
    {
        reportId: "RPT001",
        views: 45,
        downloads: 12,
        exports: {
            PDF: 8,
            EXCEL: 3,
            CSV: 1,
            PRINT: 0
        },
        lastAccessed: "2025-01-02T14:30:00Z",
        popularFilters: {
            "grades": 15,
            "sections": 12,
            "dateRange": 45
        },
        averageGenerationTime: 15.5
    },
    {
        reportId: "RPT002",
        views: 23,
        downloads: 8,
        exports: {
            PDF: 6,
            EXCEL: 2,
            CSV: 0,
            PRINT: 0
        },
        lastAccessed: "2025-01-01T16:45:00Z",
        popularFilters: {
            "grades": 8,
            "dateRange": 23
        },
        averageGenerationTime: 45.2
    }
];
function getReportById(id) {
    return mockGeneratedReports.find((report)=>report.id === id);
}
function getTemplateById(id) {
    return mockReportTemplates.find((template)=>template.id === id);
}
function getScheduleById(id) {
    return mockScheduledReports.find((schedule)=>schedule.id === id);
}
function getReportsByType(type) {
    return mockGeneratedReports.filter((report)=>report.config.type === type);
}
function getReportsByStatus(status) {
    return mockGeneratedReports.filter((report)=>report.status === status);
}
function getTeacherById(id) {
    return mockTeachers.find((teacher)=>teacher.id === id);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/utils/report-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "applyReportFilters": ()=>applyReportFilters,
    "calculateAttendanceStatistics": ()=>calculateAttendanceStatistics,
    "calculateStudentAttendanceRate": ()=>calculateStudentAttendanceRate,
    "formatDateForDepEd": ()=>formatDateForDepEd,
    "generateReportMetadata": ()=>generateReportMetadata,
    "generateSF2Report": ()=>generateSF2Report,
    "generateSF4Report": ()=>generateSF4Report,
    "getQuarter": ()=>getQuarter,
    "getSchoolYear": ()=>getSchoolYear,
    "getSemester": ()=>getSemester
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/data/reports-mock-data.ts [app-client] (ecmascript)");
;
function formatDateForDepEd(date) {
    const d = new Date(date);
    return d.toLocaleDateString('en-PH', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}
function getSchoolYear() {
    let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    // School year starts in June (month 6)
    if (month >= 6) {
        return "".concat(year, "-").concat(year + 1);
    } else {
        return "".concat(year - 1, "-").concat(year);
    }
}
function getQuarter() {
    let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();
    const month = date.getMonth() + 1;
    if (month >= 6 && month <= 8) return "1st Quarter";
    if (month >= 9 && month <= 11) return "2nd Quarter";
    if (month >= 12 || month <= 2) return "3rd Quarter";
    return "4th Quarter";
}
function getSemester() {
    let date = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : new Date();
    const month = date.getMonth() + 1;
    if (month >= 6 && month <= 10) return "1st Semester";
    return "2nd Semester";
}
function applyReportFilters(students, attendanceRecords, filters) {
    let filteredStudents = [
        ...students
    ];
    let filteredRecords = [
        ...attendanceRecords
    ];
    // Filter by grades
    if (filters.grades && filters.grades.length > 0) {
        filteredStudents = filteredStudents.filter((student)=>filters.grades.includes(student.grade));
    }
    // Filter by sections
    if (filters.sections && filters.sections.length > 0) {
        filteredStudents = filteredStudents.filter((student)=>student.section && filters.sections.includes(student.section));
    }
    // Filter by courses
    if (filters.courses && filters.courses.length > 0) {
        filteredStudents = filteredStudents.filter((student)=>filters.courses.includes(student.course));
    }
    // Filter by specific students
    if (filters.students && filters.students.length > 0) {
        filteredStudents = filteredStudents.filter((student)=>filters.students.includes(student.id));
    }
    // Filter by attendance status
    if (filters.attendanceStatus && filters.attendanceStatus.length > 0) {
        filteredRecords = filteredRecords.filter((record)=>filters.attendanceStatus.includes(record.status));
    }
    // Filter by student status
    if (!filters.includeTransferred) {
        filteredStudents = filteredStudents.filter((student)=>student.status !== 'Transferred');
    }
    if (!filters.includeInactive) {
        filteredStudents = filteredStudents.filter((student)=>student.status === 'Active');
    }
    // Filter records to match filtered students
    const studentIds = new Set(filteredStudents.map((s)=>s.id));
    filteredRecords = filteredRecords.filter((record)=>studentIds.has(record.studentId));
    return {
        students: filteredStudents,
        records: filteredRecords
    };
}
function calculateAttendanceStatistics(records) {
    const total = records.length;
    const present = records.filter((r)=>r.status === 'Present').length;
    const late = records.filter((r)=>r.status === 'Late').length;
    const absent = records.filter((r)=>r.status === 'Absent').length;
    return {
        totalRecords: total,
        presentCount: present,
        lateCount: late,
        absentCount: absent,
        attendanceRate: total > 0 ? (present + late) / total * 100 : 0
    };
}
function calculateStudentAttendanceRate(studentId, records) {
    const studentRecords = records.filter((r)=>r.studentId === studentId);
    const total = studentRecords.length;
    const present = studentRecords.filter((r)=>r.status === 'Present' || r.status === 'Late').length;
    return total > 0 ? present / total * 100 : 0;
}
function generateSF2Report(config, students, attendanceRecords) {
    var _config_filters_grades, _config_filters_sections;
    const { students: filteredStudents, records: filteredRecords } = applyReportFilters(students, attendanceRecords, config.filters);
    // Get the first teacher (in real implementation, this would be based on subject/section)
    const teacher = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockTeachers"][0];
    // Create student records for SF2
    const sf2Students = filteredStudents.map((student)=>{
        const studentRecords = filteredRecords.filter((r)=>r.studentId === student.id);
        const dailyRecord = studentRecords.find((r)=>r.date === config.dateRange.startDate);
        return {
            studentId: student.id,
            studentName: "".concat(student.firstName, " ").concat(student.middleName || '', " ").concat(student.lastName).trim(),
            attendance: {
                'Morning': (dailyRecord === null || dailyRecord === void 0 ? void 0 : dailyRecord.status) === 'Present' ? 'P' : (dailyRecord === null || dailyRecord === void 0 ? void 0 : dailyRecord.status) === 'Late' ? 'L' : (dailyRecord === null || dailyRecord === void 0 ? void 0 : dailyRecord.status) === 'Absent' ? 'A' : 'A'
            },
            dailyStatus: (dailyRecord === null || dailyRecord === void 0 ? void 0 : dailyRecord.status) === 'Present' ? 'P' : (dailyRecord === null || dailyRecord === void 0 ? void 0 : dailyRecord.status) === 'Late' ? 'L' : (dailyRecord === null || dailyRecord === void 0 ? void 0 : dailyRecord.status) === 'Absent' ? 'A' : 'A',
            remarks: dailyRecord === null || dailyRecord === void 0 ? void 0 : dailyRecord.remarks
        };
    });
    // Calculate summary
    const summary = {
        totalStudents: sf2Students.length,
        presentCount: sf2Students.filter((s)=>s.dailyStatus === 'P').length,
        lateCount: sf2Students.filter((s)=>s.dailyStatus === 'L').length,
        absentCount: sf2Students.filter((s)=>s.dailyStatus === 'A').length,
        excusedCount: sf2Students.filter((s)=>s.dailyStatus === 'E').length,
        attendanceRate: 0
    };
    summary.attendanceRate = summary.totalStudents > 0 ? (summary.presentCount + summary.lateCount) / summary.totalStudents * 100 : 0;
    return {
        id: "SF2_".concat(Date.now()),
        date: config.dateRange.startDate,
        grade: ((_config_filters_grades = config.filters.grades) === null || _config_filters_grades === void 0 ? void 0 : _config_filters_grades[0]) || 'All',
        section: ((_config_filters_sections = config.filters.sections) === null || _config_filters_sections === void 0 ? void 0 : _config_filters_sections[0]) || 'All',
        teacher,
        students: sf2Students,
        summary,
        schoolInfo: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["schoolInfo"],
        generatedAt: new Date().toISOString()
    };
}
function generateSF4Report(config, students, attendanceRecords) {
    var _config_filters_grades, _config_filters_sections;
    const { students: filteredStudents, records: filteredRecords } = applyReportFilters(students, attendanceRecords, config.filters);
    const startDate = new Date(config.dateRange.startDate);
    const endDate = new Date(config.dateRange.endDate);
    // Mock enrollment data (in real implementation, this would come from enrollment records)
    const enrollment = {
        beginningOfMonth: filteredStudents.length,
        newAdmissions: [],
        transfers: {
            transferredIn: [],
            transferredOut: filteredStudents.filter((s)=>s.status === 'Transferred').map((s)=>({
                    studentId: s.id,
                    name: "".concat(s.firstName, " ").concat(s.lastName),
                    dateOfAction: config.dateRange.endDate,
                    reason: 'Transfer to another school'
                }))
        },
        dropouts: [],
        endOfMonth: filteredStudents.filter((s)=>s.status === 'Active').length
    };
    // Calculate movement statistics
    const movement = {
        totalEnrolled: enrollment.beginningOfMonth,
        maleCount: filteredStudents.filter((s)=>s.gender === 'Male').length,
        femaleCount: filteredStudents.filter((s)=>s.gender === 'Female').length,
        newAdmissions: enrollment.newAdmissions.length,
        transfersIn: enrollment.transfers.transferredIn.length,
        transfersOut: enrollment.transfers.transferredOut.length,
        dropouts: enrollment.dropouts.length
    };
    // Calculate attendance statistics
    const stats = calculateAttendanceStatistics(filteredRecords);
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const attendance = {
        totalSchoolDays: totalDays,
        averageAttendance: stats.presentCount + stats.lateCount,
        attendanceRate: stats.attendanceRate,
        chronicAbsentees: filteredStudents.filter((s)=>calculateStudentAttendanceRate(s.id, filteredRecords) < 80).map((s)=>s.id),
        perfectAttendance: filteredStudents.filter((s)=>calculateStudentAttendanceRate(s.id, filteredRecords) === 100).map((s)=>s.id)
    };
    return {
        id: "SF4_".concat(Date.now()),
        month: startDate.toLocaleDateString('en-US', {
            month: 'long'
        }),
        year: startDate.getFullYear().toString(),
        grade: ((_config_filters_grades = config.filters.grades) === null || _config_filters_grades === void 0 ? void 0 : _config_filters_grades[0]) || 'All',
        section: (_config_filters_sections = config.filters.sections) === null || _config_filters_sections === void 0 ? void 0 : _config_filters_sections[0],
        enrollment,
        movement,
        attendance,
        schoolInfo: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["schoolInfo"],
        principalReview: {
            reviewedBy: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["schoolInfo"].principalName,
            reviewDate: new Date().toISOString().split('T')[0],
            approved: false
        },
        generatedAt: new Date().toISOString()
    };
}
function generateReportMetadata(config, students, attendanceRecords) {
    const { students: filteredStudents, records: filteredRecords } = applyReportFilters(students, attendanceRecords, config.filters);
    const stats = calculateAttendanceStatistics(filteredRecords);
    return {
        totalStudents: filteredStudents.length,
        totalRecords: filteredRecords.length,
        dateRange: config.dateRange,
        statistics: {
            presentCount: stats.presentCount,
            lateCount: stats.lateCount,
            absentCount: stats.absentCount,
            attendanceRate: stats.attendanceRate
        },
        schoolInfo: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["schoolInfo"]
    };
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/data/mock-data.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "findPeriodById": ()=>findPeriodById,
    "findStudentById": ()=>findStudentById,
    "findStudentByQRCode": ()=>findStudentByQRCode,
    "findSubjectById": ()=>findSubjectById,
    "getStudentAttendanceRecords": ()=>getStudentAttendanceRecords,
    "getTodayAttendanceRecord": ()=>getTodayAttendanceRecord,
    "mockAttendanceRecords": ()=>mockAttendanceRecords,
    "mockDashboardStats": ()=>mockDashboardStats,
    "mockRecentActivity": ()=>mockRecentActivity,
    "mockStudents": ()=>mockStudents,
    "mockSubjects": ()=>mockSubjects,
    "mockTimePeriods": ()=>mockTimePeriods
});
const mockStudents = [
    // Grade 7 Students
    {
        id: "STU001",
        name: "Maria Cristina Santos",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "1st Year",
        section: "Grade 7-A",
        grade: "7",
        status: "Active",
        photo: "https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU001_2025"
    },
    {
        id: "STU002",
        name: "Juan Carlos Dela Cruz",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "1st Year",
        section: "Grade 7-B",
        grade: "7",
        status: "Active",
        photo: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU002_2025"
    },
    {
        id: "STU003",
        name: "Ana Marie Reyes",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "1st Year",
        section: "Grade 7-A",
        grade: "7",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU003_2025"
    },
    // Grade 8 Students
    {
        id: "STU004",
        name: "Jose Miguel Rodriguez",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "2nd Year",
        section: "Grade 8-A",
        grade: "8",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU004_2025"
    },
    {
        id: "STU005",
        name: "Princess Mae Garcia",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "2nd Year",
        section: "Grade 8-B",
        grade: "8",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU005_2025"
    },
    // Grade 9 Students
    {
        id: "STU006",
        name: "Mark Anthony Villanueva",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "3rd Year",
        section: "Grade 9-A",
        grade: "9",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU006_2025"
    },
    {
        id: "STU007",
        name: "Angelica Mae Torres",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "3rd Year",
        section: "Grade 9-B",
        grade: "9",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU007_2025"
    },
    // Grade 10 Students
    {
        id: "STU008",
        name: "Christian Paul Mendoza",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "4th Year",
        section: "Grade 10-A",
        grade: "10",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU008_2025"
    },
    {
        id: "STU009",
        name: "Kimberly Rose Flores",
        email: "<EMAIL>",
        course: "Junior High School",
        year: "4th Year",
        section: "Grade 10-B",
        grade: "10",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU009_2025"
    },
    // Grade 11 Students (Senior High School)
    {
        id: "STU010",
        name: "John Michael Cruz",
        email: "<EMAIL>",
        course: "Information and Communications Technology",
        year: "1st Year Senior High",
        section: "ICT 11-A",
        grade: "11",
        status: "Active",
        photo: "https://images.unsplash.com/photo-*************-f4e0f30006d5?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU010_2025"
    },
    {
        id: "STU011",
        name: "Mary Grace Aquino",
        email: "<EMAIL>",
        course: "Accountancy, Business and Management",
        year: "1st Year Senior High",
        section: "ABM 11-A",
        grade: "11",
        status: "Active",
        photo: "https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU011_2025"
    },
    // Grade 12 Students (Senior High School)
    {
        id: "STU012",
        name: "Ryan James Bautista",
        email: "<EMAIL>",
        course: "Information and Communications Technology",
        year: "2nd Year Senior High",
        section: "ICT 12-A",
        grade: "12",
        status: "Active",
        photo: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU012_2025"
    },
    {
        id: "STU013",
        name: "Sarah Jane Morales",
        email: "<EMAIL>",
        course: "Humanities and Social Sciences",
        year: "2nd Year Senior High",
        section: "HUMSS 12-A",
        grade: "12",
        status: "Active",
        photo: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
        qrCode: "QR_STU013_2025"
    }
];
const mockSubjects = [
    {
        id: "SUBJ001",
        name: "Programming Fundamentals",
        code: "IT101",
        instructor: "Prof. Martinez",
        schedule: [
            {
                day: "Monday",
                startTime: "08:00",
                endTime: "10:00"
            },
            {
                day: "Wednesday",
                startTime: "08:00",
                endTime: "10:00"
            },
            {
                day: "Friday",
                startTime: "08:00",
                endTime: "10:00"
            }
        ]
    },
    {
        id: "SUBJ002",
        name: "Database Management",
        code: "IT201",
        instructor: "Prof. Rodriguez",
        schedule: [
            {
                day: "Tuesday",
                startTime: "10:00",
                endTime: "12:00"
            },
            {
                day: "Thursday",
                startTime: "10:00",
                endTime: "12:00"
            }
        ]
    },
    {
        id: "SUBJ003",
        name: "Web Development",
        code: "IT301",
        instructor: "Prof. Santos",
        schedule: [
            {
                day: "Monday",
                startTime: "13:00",
                endTime: "15:00"
            },
            {
                day: "Wednesday",
                startTime: "13:00",
                endTime: "15:00"
            }
        ]
    },
    {
        id: "SUBJ004",
        name: "Data Structures",
        code: "CS201",
        instructor: "Prof. Reyes",
        schedule: [
            {
                day: "Tuesday",
                startTime: "08:00",
                endTime: "10:00"
            },
            {
                day: "Thursday",
                startTime: "08:00",
                endTime: "10:00"
            }
        ]
    },
    {
        id: "SUBJ005",
        name: "Software Engineering",
        code: "CS301",
        instructor: "Prof. Cruz",
        schedule: [
            {
                day: "Monday",
                startTime: "15:00",
                endTime: "17:00"
            },
            {
                day: "Friday",
                startTime: "15:00",
                endTime: "17:00"
            }
        ]
    }
];
const mockTimePeriods = [
    {
        id: "PERIOD001",
        name: "1st Period",
        startTime: "08:00",
        endTime: "10:00",
        type: "morning"
    },
    {
        id: "PERIOD002",
        name: "2nd Period",
        startTime: "10:00",
        endTime: "12:00",
        type: "morning"
    },
    {
        id: "PERIOD003",
        name: "3rd Period",
        startTime: "13:00",
        endTime: "15:00",
        type: "afternoon"
    },
    {
        id: "PERIOD004",
        name: "4th Period",
        startTime: "15:00",
        endTime: "17:00",
        type: "afternoon"
    },
    {
        id: "PERIOD005",
        name: "Evening Class",
        startTime: "18:00",
        endTime: "20:00",
        type: "evening"
    }
];
const mockAttendanceRecords = [
    // Today's attendance records
    {
        id: "ATT001",
        studentId: "STU001",
        studentName: "Maria Cristina Santos",
        course: "Junior High School",
        checkIn: "7:45 AM",
        checkOut: "4:30 PM",
        date: new Date().toISOString().split('T')[0],
        status: "Present",
        type: "gate",
        timestamp: new Date(new Date().setHours(7, 45, 0))
    },
    {
        id: "ATT002",
        studentId: "STU002",
        studentName: "Juan Carlos Dela Cruz",
        course: "Junior High School",
        checkIn: "7:50 AM",
        checkOut: "4:25 PM",
        date: new Date().toISOString().split('T')[0],
        status: "Present",
        type: "gate",
        timestamp: new Date(new Date().setHours(7, 50, 0))
    },
    {
        id: "ATT003",
        studentId: "STU003",
        studentName: "Ana Marie Reyes",
        course: "Junior High School",
        checkIn: "8:15 AM",
        checkOut: "4:35 PM",
        date: new Date().toISOString().split('T')[0],
        status: "Late",
        type: "gate",
        timestamp: new Date(new Date().setHours(8, 15, 0))
    },
    {
        id: "ATT004",
        studentId: "STU004",
        studentName: "Jose Miguel Rodriguez",
        course: "Junior High School",
        date: new Date().toISOString().split('T')[0],
        status: "Absent",
        type: "subject",
        subject: "Mathematics",
        period: "1st Period",
        timestamp: new Date(new Date().setHours(8, 0, 0))
    },
    {
        id: "ATT005",
        studentId: "STU005",
        studentName: "Princess Mae Garcia",
        course: "Junior High School",
        checkIn: "7:55 AM",
        checkOut: "4:20 PM",
        date: new Date().toISOString().split('T')[0],
        status: "Present",
        type: "gate",
        timestamp: new Date(new Date().setHours(7, 55, 0))
    },
    {
        id: "ATT006",
        studentId: "STU010",
        studentName: "John Michael Cruz",
        course: "Information and Communications Technology",
        checkIn: "7:40 AM",
        checkOut: "5:00 PM",
        date: new Date().toISOString().split('T')[0],
        status: "Present",
        type: "gate",
        timestamp: new Date(new Date().setHours(7, 40, 0))
    },
    {
        id: "ATT007",
        studentId: "STU012",
        studentName: "Ryan James Bautista",
        course: "Information and Communications Technology",
        checkIn: "8:10 AM",
        checkOut: "5:05 PM",
        date: new Date().toISOString().split('T')[0],
        status: "Late",
        type: "gate",
        timestamp: new Date(new Date().setHours(8, 10, 0))
    }
];
const mockDashboardStats = {
    totalStudents: 1234,
    presentToday: 1105,
    lateToday: 23,
    absentToday: 106,
    attendanceRate: 89.5,
    weeklyTrend: [
        {
            day: 'Mon',
            present: 1150,
            late: 15,
            absent: 69
        },
        {
            day: 'Tue',
            present: 1120,
            late: 28,
            absent: 86
        },
        {
            day: 'Wed',
            present: 1105,
            late: 23,
            absent: 106
        },
        {
            day: 'Thu',
            present: 1140,
            late: 18,
            absent: 76
        },
        {
            day: 'Fri',
            present: 1095,
            late: 35,
            absent: 104
        }
    ],
    gradeBreakdown: [
        {
            grade: '7',
            total: 180,
            present: 165,
            late: 3,
            absent: 12
        },
        {
            grade: '8',
            total: 175,
            present: 158,
            late: 4,
            absent: 13
        },
        {
            grade: '9',
            total: 170,
            present: 155,
            late: 2,
            absent: 13
        },
        {
            grade: '10',
            total: 165,
            present: 148,
            late: 5,
            absent: 12
        },
        {
            grade: '11',
            total: 272,
            present: 245,
            late: 6,
            absent: 21
        },
        {
            grade: '12',
            total: 272,
            present: 234,
            late: 3,
            absent: 35
        }
    ]
};
const mockRecentActivity = [
    {
        id: "ACT001",
        type: "scan",
        studentName: "Maria Cristina Santos",
        action: "Check In",
        time: "2 minutes ago",
        status: "success"
    },
    {
        id: "ACT002",
        type: "scan",
        studentName: "Juan Carlos Dela Cruz",
        action: "Check Out",
        time: "5 minutes ago",
        status: "success"
    },
    {
        id: "ACT003",
        type: "alert",
        studentName: "Jose Miguel Rodriguez",
        action: "Marked Absent",
        time: "15 minutes ago",
        status: "warning"
    },
    {
        id: "ACT004",
        type: "scan",
        studentName: "Princess Mae Garcia",
        action: "Late Arrival",
        time: "25 minutes ago",
        status: "warning"
    }
];
function findStudentById(id) {
    return mockStudents.find((student)=>student.id === id);
}
function findStudentByQRCode(qrCode) {
    return mockStudents.find((student)=>student.qrCode === qrCode);
}
function findSubjectById(id) {
    return mockSubjects.find((subject)=>subject.id === id);
}
function findPeriodById(id) {
    return mockTimePeriods.find((period)=>period.id === id);
}
function getStudentAttendanceRecords(studentId) {
    return mockAttendanceRecords.filter((record)=>record.studentId === studentId);
}
function getTodayAttendanceRecord(studentId) {
    const today = new Date().toISOString().split('T')[0];
    return mockAttendanceRecords.find((record)=>record.studentId === studentId && record.date === today);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/utils/export-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReportExcelGenerator": ()=>ReportExcelGenerator,
    "ReportExportManager": ()=>ReportExportManager,
    "ReportPDFGenerator": ()=>ReportPDFGenerator,
    "exportToCSV": ()=>exportToCSV,
    "generateDailyReport": ()=>generateDailyReport,
    "generateSummaryStats": ()=>generateSummaryStats,
    "printDailyReport": ()=>printDailyReport
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-client] (ecmascript) <locals>");
;
function exportToCSV(data, filename) {
    const headers = [
        "Student ID",
        "Student Name",
        "Grade",
        "Section",
        "Course",
        "Date",
        "Check In",
        "Check Out",
        "Status",
        "Type",
        "Subject",
        "Period",
        "Reason"
    ];
    const csvContent = [
        headers.join(","),
        ...data.map((record)=>[
                record.studentId,
                '"'.concat(record.studentName, '"'),
                record.grade,
                '"'.concat(record.section, '"'),
                '"'.concat(record.course, '"'),
                record.date,
                record.checkIn || "",
                record.checkOut || "",
                record.status,
                record.type,
                record.subject || "",
                record.period || "",
                record.reason ? '"'.concat(record.reason, '"') : ""
            ].join(","))
    ].join("\n");
    downloadFile(csvContent, filename || "attendance-report-".concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), "yyyy-MM-dd"), ".csv"), "text/csv");
}
function generateDailyReport(data, date) {
    const dateStr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, "yyyy-MM-dd");
    const dayData = data.filter((record)=>record.date === dateStr);
    const summary = {
        date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, "MMMM d, yyyy"),
        totalStudents: dayData.length,
        present: dayData.filter((r)=>r.status === "Present").length,
        late: dayData.filter((r)=>r.status === "Late").length,
        absent: dayData.filter((r)=>r.status === "Absent").length,
        attendanceRate: 0
    };
    if (summary.totalStudents > 0) {
        summary.attendanceRate = (summary.present + summary.late) / summary.totalStudents * 100;
    }
    const gradeBreakdown = dayData.reduce((acc, record)=>{
        const grade = record.grade;
        if (!acc[grade]) {
            acc[grade] = {
                total: 0,
                present: 0,
                late: 0,
                absent: 0
            };
        }
        acc[grade].total++;
        acc[grade][record.status.toLowerCase()]++;
        return acc;
    }, {});
    return {
        summary,
        gradeBreakdown,
        records: dayData
    };
}
function printDailyReport(data, date) {
    const report = generateDailyReport(data, date);
    const printContent = "\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>Daily Attendance Report - ".concat(report.summary.date, '</title>\n      <style>\n        body { \n          font-family: Arial, sans-serif; \n          margin: 20px; \n          color: #333;\n        }\n        .header { \n          text-align: center; \n          margin-bottom: 30px;\n          border-bottom: 2px solid #333;\n          padding-bottom: 20px;\n        }\n        .school-name {\n          font-size: 24px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        .report-title {\n          font-size: 18px;\n          color: #666;\n        }\n        .summary { \n          display: grid; \n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 20px;\n          margin-bottom: 30px;\n        }\n        .summary-card {\n          border: 1px solid #ddd;\n          padding: 15px;\n          border-radius: 8px;\n          text-align: center;\n        }\n        .summary-value {\n          font-size: 32px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        .summary-label {\n          color: #666;\n          font-size: 14px;\n        }\n        .present { color: #22c55e; }\n        .late { color: #f59e0b; }\n        .absent { color: #ef4444; }\n        .rate { color: #3b82f6; }\n        \n        .grade-breakdown {\n          margin-bottom: 30px;\n        }\n        .grade-breakdown h3 {\n          margin-bottom: 15px;\n          color: #333;\n        }\n        .grade-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n        }\n        .grade-table th,\n        .grade-table td {\n          border: 1px solid #ddd;\n          padding: 8px;\n          text-align: center;\n        }\n        .grade-table th {\n          background-color: #f5f5f5;\n          font-weight: bold;\n        }\n        \n        .records-table {\n          width: 100%;\n          border-collapse: collapse;\n          font-size: 12px;\n        }\n        .records-table th,\n        .records-table td {\n          border: 1px solid #ddd;\n          padding: 6px;\n          text-align: left;\n        }\n        .records-table th {\n          background-color: #f5f5f5;\n          font-weight: bold;\n        }\n        \n        .footer {\n          margin-top: 40px;\n          text-align: center;\n          color: #666;\n          font-size: 12px;\n          border-top: 1px solid #ddd;\n          padding-top: 20px;\n        }\n        \n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n      </style>\n    </head>\n    <body>\n      <div class="header">\n        <div class="school-name">QRSAMS - Tanauan National High School</div>\n        <div class="report-title">Daily Attendance Report</div>\n        <div style="margin-top: 10px; font-size: 16px;">').concat(report.summary.date, '</div>\n      </div>\n      \n      <div class="summary">\n        <div class="summary-card">\n          <div class="summary-value">').concat(report.summary.totalStudents, '</div>\n          <div class="summary-label">Total Students</div>\n        </div>\n        <div class="summary-card">\n          <div class="summary-value present">').concat(report.summary.present, '</div>\n          <div class="summary-label">Present</div>\n        </div>\n        <div class="summary-card">\n          <div class="summary-value late">').concat(report.summary.late, '</div>\n          <div class="summary-label">Late</div>\n        </div>\n        <div class="summary-card">\n          <div class="summary-value absent">').concat(report.summary.absent, '</div>\n          <div class="summary-label">Absent</div>\n        </div>\n        <div class="summary-card">\n          <div class="summary-value rate">').concat(report.summary.attendanceRate.toFixed(1), '%</div>\n          <div class="summary-label">Attendance Rate</div>\n        </div>\n      </div>\n      \n      <div class="grade-breakdown">\n        <h3>Grade Level Breakdown</h3>\n        <table class="grade-table">\n          <thead>\n            <tr>\n              <th>Grade</th>\n              <th>Total</th>\n              <th>Present</th>\n              <th>Late</th>\n              <th>Absent</th>\n              <th>Rate</th>\n            </tr>\n          </thead>\n          <tbody>\n            ').concat(Object.entries(report.gradeBreakdown).map((param)=>{
        let [grade, stats] = param;
        return "\n              <tr>\n                <td>Grade ".concat(grade, "</td>\n                <td>").concat(stats.total, '</td>\n                <td class="present">').concat(stats.present, '</td>\n                <td class="late">').concat(stats.late, '</td>\n                <td class="absent">').concat(stats.absent, "</td>\n                <td>").concat(((stats.present + stats.late) / stats.total * 100).toFixed(1), "%</td>\n              </tr>\n            ");
    }).join(""), '\n          </tbody>\n        </table>\n      </div>\n      \n      <div>\n        <h3>Detailed Records</h3>\n        <table class="records-table">\n          <thead>\n            <tr>\n              <th>Student ID</th>\n              <th>Name</th>\n              <th>Grade</th>\n              <th>Section</th>\n              <th>Check In</th>\n              <th>Check Out</th>\n              <th>Status</th>\n              <th>Notes</th>\n            </tr>\n          </thead>\n          <tbody>\n            ').concat(report.records.map((record)=>"\n              <tr>\n                <td>".concat(record.studentId, "</td>\n                <td>").concat(record.studentName, "</td>\n                <td>").concat(record.grade, "</td>\n                <td>").concat(record.section, "</td>\n                <td>").concat(record.checkIn || "-", "</td>\n                <td>").concat(record.checkOut || "-", '</td>\n                <td class="').concat(record.status.toLowerCase(), '">').concat(record.status, "</td>\n                <td>").concat(record.reason || "-", "</td>\n              </tr>\n            ")).join(""), '\n          </tbody>\n        </table>\n      </div>\n      \n      <div class="footer">\n        Generated on ').concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(), "MMMM d, yyyy 'at' h:mm a"), " by QRSAMS\n      </div>\n    </body>\n    </html>\n  ");
    const printWindow = window.open("", "_blank");
    if (printWindow) {
        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
        printWindow.close();
    }
}
// Utility function to download files
function downloadFile(content, filename, mimeType) {
    const blob = new Blob([
        content
    ], {
        type: mimeType
    });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}
function generateSummaryStats(data) {
    const total = data.length;
    const present = data.filter((r)=>r.status === "Present").length;
    const late = data.filter((r)=>r.status === "Late").length;
    const absent = data.filter((r)=>r.status === "Absent").length;
    const attendanceRate = total > 0 ? (present + late) / total * 100 : 0;
    return {
        total,
        present,
        late,
        absent,
        attendanceRate
    };
}
class ReportPDFGenerator {
    static async generateSF2PDF(report) {
        const htmlContent = this.generateSF2HTML(report);
        // Simulate PDF generation delay
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        // In a real implementation, this would use a PDF library like jsPDF or Puppeteer
        // For now, we'll create a mock PDF blob
        const pdfContent = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n\n4 0 obj\n<<\n/Length 44\n>>\nstream\nBT\n/F1 12 Tf\n72 720 Td\n(SF2 Daily Attendance Report) Tj\nET\nendstream\nendobj\n\nxref\n0 5\n0000000000 65535 f\n0000000009 00000 n\n0000000058 00000 n\n0000000115 00000 n\n0000000206 00000 n\ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n299\n%%EOF";
        return new Blob([
            pdfContent
        ], {
            type: 'application/pdf'
        });
    }
    static async generateSF4PDF(report) {
        const htmlContent = this.generateSF4HTML(report);
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        const pdfContent = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 792 612]\n/Contents 4 0 R\n>>\nendobj\n\n4 0 obj\n<<\n/Length 50\n>>\nstream\nBT\n/F1 12 Tf\n72 720 Td\n(SF4 Monthly Learner's Movement) Tj\nET\nendstream\nendobj\n\nxref\n0 5\n0000000000 65535 f\n0000000009 00000 n\n0000000058 00000 n\n0000000115 00000 n\n0000000206 00000 n\ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n305\n%%EOF";
        return new Blob([
            pdfContent
        ], {
            type: 'application/pdf'
        });
    }
    static async generateCustomPDF(report) {
        const htmlContent = this.generateCustomHTML(report);
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        const pdfContent = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n\n4 0 obj\n<<\n/Length 35\n>>\nstream\nBT\n/F1 12 Tf\n72 720 Td\n(Custom Report) Tj\nET\nendstream\nendobj\n\nxref\n0 5\n0000000000 65535 f\n0000000009 00000 n\n0000000058 00000 n\n0000000115 00000 n\n0000000206 00000 n\ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n290\n%%EOF";
        return new Blob([
            pdfContent
        ], {
            type: 'application/pdf'
        });
    }
    static generateSF2HTML(report) {
        return '\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>SF2 Daily Attendance Report</title>\n      <style>\n        body { font-family: Arial, sans-serif; margin: 20px; }\n        .header { text-align: center; margin-bottom: 30px; }\n        .school-info { margin-bottom: 20px; }\n        .attendance-table { width: 100%; border-collapse: collapse; }\n        .attendance-table th, .attendance-table td { border: 1px solid #000; padding: 8px; text-align: center; }\n        .signature-section { margin-top: 40px; }\n        @media print { body { margin: 0; } }\n      </style>\n    </head>\n    <body>\n      <div class="header">\n        <h1>Republic of the Philippines</h1>\n        <h2>Department of Education</h2>\n        <h3>'.concat(report.schoolInfo.region, "</h3>\n        <h4>").concat(report.schoolInfo.division, "</h4>\n        <h5>").concat(report.schoolInfo.schoolName, '</h5>\n        <h2>SCHOOL FORM 2 (SF2)</h2>\n        <h3>DAILY ATTENDANCE REPORT OF LEARNERS</h3>\n      </div>\n\n      <div class="school-info">\n        <p><strong>Grade & Section:</strong> Grade ').concat(report.grade, " - ").concat(report.section, "</p>\n        <p><strong>Subject:</strong> ").concat(report.subject || 'All Subjects', "</p>\n        <p><strong>Teacher:</strong> ").concat(report.teacher.name, "</p>\n        <p><strong>Date:</strong> ").concat(new Date(report.date).toLocaleDateString('en-PH', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }), '</p>\n      </div>\n\n      <table class="attendance-table">\n        <thead>\n          <tr>\n            <th>No.</th>\n            <th>LEARNER\'S NAME</th>\n            <th>ATTENDANCE</th>\n            <th>REMARKS</th>\n          </tr>\n        </thead>\n        <tbody>\n          ').concat(report.students.map((student, index)=>"\n            <tr>\n              <td>".concat(index + 1, "</td>\n              <td>").concat(student.studentName, "</td>\n              <td>").concat(student.dailyStatus, "</td>\n              <td>").concat(student.remarks || '', "</td>\n            </tr>\n          ")).join(''), '\n        </tbody>\n      </table>\n\n      <div class="signature-section">\n        <p><strong>Summary:</strong></p>\n        <p>Total Students: ').concat(report.summary.totalStudents, "</p>\n        <p>Present: ").concat(report.summary.presentCount, "</p>\n        <p>Late: ").concat(report.summary.lateCount, "</p>\n        <p>Absent: ").concat(report.summary.absentCount, "</p>\n        <p>Attendance Rate: ").concat(report.summary.attendanceRate.toFixed(1), '%</p>\n\n        <div style="margin-top: 60px;">\n          <div style="display: inline-block; width: 300px; text-align: center;">\n            <div style="border-bottom: 1px solid #000; height: 40px;"></div>\n            <p>Teacher\'s Signature</p>\n            <p>').concat(report.teacher.name, '</p>\n          </div>\n          <div style="display: inline-block; width: 300px; text-align: center; margin-left: 50px;">\n            <div style="border-bottom: 1px solid #000; height: 40px;"></div>\n            <p>Principal\'s Signature</p>\n            <p>').concat(report.schoolInfo.principalName, "</p>\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n    ");
    }
    static generateSF4HTML(report) {
        return '\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>SF4 Monthly Learner\'s Movement</title>\n      <style>\n        body { font-family: Arial, sans-serif; margin: 20px; }\n        .header { text-align: center; margin-bottom: 30px; }\n        .info-section { margin-bottom: 20px; }\n        .movement-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n        .movement-table th, .movement-table td { border: 1px solid #000; padding: 8px; }\n        .signature-section { margin-top: 40px; }\n        @media print { body { margin: 0; } }\n      </style>\n    </head>\n    <body>\n      <div class="header">\n        <h1>Republic of the Philippines</h1>\n        <h2>Department of Education</h2>\n        <h3>'.concat(report.schoolInfo.region, "</h3>\n        <h4>").concat(report.schoolInfo.division, "</h4>\n        <h5>").concat(report.schoolInfo.schoolName, '</h5>\n        <h2>SCHOOL FORM 4 (SF4)</h2>\n        <h3>MONTHLY REPORT ON LEARNER\'S MOVEMENT</h3>\n      </div>\n\n      <div class="info-section">\n        <p><strong>Month:</strong> ').concat(report.month, " ").concat(report.year, "</p>\n        <p><strong>Grade:</strong> ").concat(report.grade, "</p>\n        ").concat(report.section ? "<p><strong>Section:</strong> ".concat(report.section, "</p>") : '', '\n      </div>\n\n      <h4>ENROLLMENT SUMMARY</h4>\n      <table class="movement-table">\n        <tr>\n          <td>Beginning of Month</td>\n          <td>').concat(report.enrollment.beginningOfMonth, "</td>\n        </tr>\n        <tr>\n          <td>New Admissions</td>\n          <td>").concat(report.enrollment.newAdmissions.length, "</td>\n        </tr>\n        <tr>\n          <td>Transfers Out</td>\n          <td>").concat(report.enrollment.transfers.transferredOut.length, "</td>\n        </tr>\n        <tr>\n          <td>Dropouts</td>\n          <td>").concat(report.enrollment.dropouts.length, "</td>\n        </tr>\n        <tr>\n          <td><strong>End of Month</strong></td>\n          <td><strong>").concat(report.enrollment.endOfMonth, '</strong></td>\n        </tr>\n      </table>\n\n      <h4>ATTENDANCE STATISTICS</h4>\n      <table class="movement-table">\n        <tr>\n          <td>Total School Days</td>\n          <td>').concat(report.attendance.totalSchoolDays, "</td>\n        </tr>\n        <tr>\n          <td>Average Daily Attendance</td>\n          <td>").concat(report.attendance.averageAttendance, "</td>\n        </tr>\n        <tr>\n          <td>Attendance Rate</td>\n          <td>").concat(report.attendance.attendanceRate.toFixed(1), '%</td>\n        </tr>\n      </table>\n\n      <div class="signature-section">\n        <div style="margin-top: 60px;">\n          <div style="display: inline-block; width: 300px; text-align: center;">\n            <div style="border-bottom: 1px solid #000; height: 40px;"></div>\n            <p>Prepared by</p>\n            <p>Class Adviser/Teacher</p>\n          </div>\n          <div style="display: inline-block; width: 300px; text-align: center; margin-left: 50px;">\n            <div style="border-bottom: 1px solid #000; height: 40px;"></div>\n            <p>Principal\'s Signature</p>\n            <p>').concat(report.schoolInfo.principalName, "</p>\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n    ");
    }
    static generateCustomHTML(report) {
        return "\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>".concat(report.name, '</title>\n      <style>\n        body { font-family: Arial, sans-serif; margin: 20px; }\n        .header { text-align: center; margin-bottom: 30px; }\n        .report-table { width: 100%; border-collapse: collapse; }\n        .report-table th, .report-table td { border: 1px solid #000; padding: 8px; }\n        @media print { body { margin: 0; } }\n      </style>\n    </head>\n    <body>\n      <div class="header">\n        <h1>').concat(report.name, "</h1>\n        <p>").concat(report.description, "</p>\n        <p>Generated on ").concat(new Date(report.generatedAt).toLocaleDateString(), '</p>\n      </div>\n\n      <table class="report-table">\n        <thead>\n          <tr>\n            ').concat(report.query.fields.map((field)=>"<th>".concat(field, "</th>")).join(''), "\n          </tr>\n        </thead>\n        <tbody>\n          ").concat(report.data.map((row)=>"\n            <tr>\n              ".concat(report.query.fields.map((field)=>"<td>".concat(row[field] || '', "</td>")).join(''), "\n            </tr>\n          ")).join(''), "\n        </tbody>\n      </table>\n    </body>\n    </html>\n    ");
    }
}
class ReportExcelGenerator {
    static async generateSF2Excel(report) {
        // In a real implementation, this would use a library like SheetJS or ExcelJS
        const csvContent = this.generateSF2CSV(report);
        await new Promise((resolve)=>setTimeout(resolve, 500));
        return new Blob([
            csvContent
        ], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
    }
    static async generateSF4Excel(report) {
        const csvContent = this.generateSF4CSV(report);
        await new Promise((resolve)=>setTimeout(resolve, 500));
        return new Blob([
            csvContent
        ], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
    }
    static async generateCustomExcel(report) {
        const csvContent = this.generateCustomCSV(report);
        await new Promise((resolve)=>setTimeout(resolve, 500));
        return new Blob([
            csvContent
        ], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
    }
    static generateSF2CSV(report) {
        const headers = [
            'No.',
            'Student Name',
            'Attendance Status',
            'Remarks'
        ];
        const rows = report.students.map((student, index)=>[
                index + 1,
                student.studentName,
                student.dailyStatus,
                student.remarks || ''
            ]);
        return [
            "SF2 Daily Attendance Report - ".concat(report.date),
            "Grade ".concat(report.grade, " - ").concat(report.section),
            "Teacher: ".concat(report.teacher.name),
            '',
            headers.join(','),
            ...rows.map((row)=>row.join(','))
        ].join('\n');
    }
    static generateSF4CSV(report) {
        return [
            "SF4 Monthly Learner's Movement - ".concat(report.month, " ").concat(report.year),
            "Grade ".concat(report.grade),
            '',
            'Enrollment Summary',
            "Beginning of Month,".concat(report.enrollment.beginningOfMonth),
            "New Admissions,".concat(report.enrollment.newAdmissions.length),
            "Transfers Out,".concat(report.enrollment.transfers.transferredOut.length),
            "Dropouts,".concat(report.enrollment.dropouts.length),
            "End of Month,".concat(report.enrollment.endOfMonth),
            '',
            'Attendance Statistics',
            "Total School Days,".concat(report.attendance.totalSchoolDays),
            "Average Daily Attendance,".concat(report.attendance.averageAttendance),
            "Attendance Rate,".concat(report.attendance.attendanceRate.toFixed(1), "%")
        ].join('\n');
    }
    static generateCustomCSV(report) {
        const headers = report.query.fields;
        const rows = report.data.map((row)=>headers.map((field)=>row[field] || '').join(','));
        return [
            report.name,
            report.description,
            "Generated on ".concat(new Date(report.generatedAt).toLocaleDateString()),
            '',
            headers.join(','),
            ...rows
        ].join('\n');
    }
}
class ReportExportManager {
    static async exportReport(report, format) {
        let blob;
        let filename;
        // Determine report type
        const reportType = 'students' in report ? 'SF2' : 'enrollment' in report ? 'SF4' : 'CUSTOM';
        switch(format){
            case 'PDF':
                if (reportType === 'SF2') {
                    blob = await ReportPDFGenerator.generateSF2PDF(report);
                    filename = "SF2_".concat(report.date, "_Grade").concat(report.grade, ".pdf");
                } else if (reportType === 'SF4') {
                    blob = await ReportPDFGenerator.generateSF4PDF(report);
                    filename = "SF4_".concat(report.month, "_").concat(report.year, "_Grade").concat(report.grade, ".pdf");
                } else {
                    blob = await ReportPDFGenerator.generateCustomPDF(report);
                    filename = "".concat(report.name.replace(/[^a-zA-Z0-9]/g, '_'), ".pdf");
                }
                break;
            case 'EXCEL':
                if (reportType === 'SF2') {
                    blob = await ReportExcelGenerator.generateSF2Excel(report);
                    filename = "SF2_".concat(report.date, "_Grade").concat(report.grade, ".xlsx");
                } else if (reportType === 'SF4') {
                    blob = await ReportExcelGenerator.generateSF4Excel(report);
                    filename = "SF4_".concat(report.month, "_").concat(report.year, "_Grade").concat(report.grade, ".xlsx");
                } else {
                    blob = await ReportExcelGenerator.generateCustomExcel(report);
                    filename = "".concat(report.name.replace(/[^a-zA-Z0-9]/g, '_'), ".xlsx");
                }
                break;
            case 'CSV':
                if (reportType === 'SF2') {
                    const csvContent = ReportExcelGenerator['generateSF2CSV'](report);
                    blob = new Blob([
                        csvContent
                    ], {
                        type: 'text/csv'
                    });
                    filename = "SF2_".concat(report.date, "_Grade").concat(report.grade, ".csv");
                } else if (reportType === 'SF4') {
                    const csvContent = ReportExcelGenerator['generateSF4CSV'](report);
                    blob = new Blob([
                        csvContent
                    ], {
                        type: 'text/csv'
                    });
                    filename = "SF4_".concat(report.month, "_").concat(report.year, "_Grade").concat(report.grade, ".csv");
                } else {
                    const csvContent = ReportExcelGenerator['generateCustomCSV'](report);
                    blob = new Blob([
                        csvContent
                    ], {
                        type: 'text/csv'
                    });
                    filename = "".concat(report.name.replace(/[^a-zA-Z0-9]/g, '_'), ".csv");
                }
                break;
            default:
                throw new Error("Unsupported export format: ".concat(format));
        }
        return {
            blob,
            filename
        };
    }
    static downloadFile(blob, filename) {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(dashboard)/reports/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>ReportsPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/tabs.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$type$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/report-type-selector.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$filters$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/report-filters.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$reports$2d$library$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/reports-library.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$sf2$2d$report$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/sf2-report-dialog.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$sf4$2d$report$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/sf4-report-dialog.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$custom$2d$report$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/custom-report-dialog.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$scheduler$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/report-scheduler.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$archive$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/report-archive.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$wizard$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/report-wizard-dialog.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$bulk$2d$report$2d$generator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/bulk-report-generator.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$analytics$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/reports/report-analytics.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/data/reports-mock-data.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-client] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/clock.js [app-client] (ecmascript) <export default as Clock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js [app-client] (ecmascript) <export default as TrendingUp>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/settings.js [app-client] (ecmascript) <export default as Settings>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function ReportsPage() {
    _s();
    const [selectedReportType, setSelectedReportType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("SF2");
    const [reportFilters, setReportFilters] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("generate");
    const [showSF2Dialog, setShowSF2Dialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showSF4Dialog, setShowSF4Dialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showCustomDialog, setShowCustomDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showWizardDialog, setShowWizardDialog] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Mock analytics data
    const totalReports = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockGeneratedReports"].length;
    const readyReports = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockGeneratedReports"].filter((r)=>r.status === "READY").length;
    const totalDownloads = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockGeneratedReports"].reduce((sum, r)=>sum + r.downloadCount, 0);
    const scheduledReports = 8 // Mock data
    ;
    const handleGenerateReport = ()=>{
        if (selectedReportType === "SF2") {
            setShowSF2Dialog(true);
        } else if (selectedReportType === "SF4") {
            setShowSF4Dialog(true);
        } else if (selectedReportType === "CUSTOM") {
            setShowCustomDialog(true);
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Report generation started", {
                description: "Your report will be ready in a few minutes"
            });
        }
    };
    const handleDownload = (reportId, format)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Downloading report in ".concat(format, " format"));
    };
    const handlePreview = (reportId)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info("Opening report preview");
    };
    const handleDelete = (reportId)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Report deleted successfully");
    };
    const handleArchive = (reportId)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Report archived successfully");
    };
    const handleShare = (reportId)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Report sharing link copied to clipboard");
    };
    const handleRegenerate = (reportId)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Report regeneration started");
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-3xl font-bold tracking-tight",
                                children: "Reports Dashboard"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 91,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground",
                                children: "Generate Philippine DepEd forms and custom attendance reports"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 92,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 90,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                size: "sm",
                                onClick: ()=>setShowWizardDialog(true),
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$settings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Settings$3e$__["Settings"], {
                                        className: "mr-2 h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 98,
                                        columnNumber: 13
                                    }, this),
                                    "Wizard"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 97,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                onClick: handleGenerateReport,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                        className: "mr-2 h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 102,
                                        columnNumber: 13
                                    }, this),
                                    "New Report"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 101,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 96,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 89,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid gap-4 md:grid-cols-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-sm font-medium",
                                        children: "Total Reports"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 112,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                                        className: "h-4 w-4 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 113,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 111,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-2xl font-bold",
                                        children: totalReports
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 116,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: [
                                            readyReports,
                                            " ready to download"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 117,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 115,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 110,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-sm font-medium",
                                        children: "Downloads"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 125,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                        className: "h-4 w-4 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 126,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 124,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-2xl font-bold",
                                        children: totalDownloads
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 129,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: "Total downloads"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 130,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 128,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 123,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-sm font-medium",
                                        children: "Scheduled Reports"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 138,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Clock$3e$__["Clock"], {
                                        className: "h-4 w-4 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 139,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 137,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-2xl font-bold",
                                        children: scheduledReports
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 142,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: "Auto-generated"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 143,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 141,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 136,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                className: "flex flex-row items-center justify-between space-y-0 pb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                        className: "text-sm font-medium",
                                        children: "DepEd Compliance"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 151,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trending$2d$up$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TrendingUp$3e$__["TrendingUp"], {
                                        className: "h-4 w-4 text-muted-foreground"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 152,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 150,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-2xl font-bold",
                                        children: "100%"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 155,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-muted-foreground",
                                        children: "SF2 & SF4 ready"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 156,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 154,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 149,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 109,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
                value: activeTab,
                onValueChange: setActiveTab,
                className: "space-y-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsList"], {
                        className: "grid w-full grid-cols-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "generate",
                                children: "Generate Report"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 166,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "library",
                                children: "Reports Library"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 167,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "bulk",
                                children: "Bulk Generate"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 168,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "scheduled",
                                children: "Scheduled"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 169,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "archive",
                                children: "Archive"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 170,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "analytics",
                                children: "Analytics"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 171,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 165,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "generate",
                        className: "space-y-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid gap-6 lg:grid-cols-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "lg:col-span-2",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$type$2d$selector$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReportTypeSelector"], {
                                            selectedType: selectedReportType,
                                            onTypeSelect: (type)=>{
                                                setSelectedReportType(type);
                                                if (type === "SF2") {
                                                    setShowSF2Dialog(true);
                                                } else if (type === "SF4") {
                                                    setShowSF4Dialog(true);
                                                } else if (type === "CUSTOM") {
                                                    setShowCustomDialog(true);
                                                }
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                            lineNumber: 177,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 176,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$filters$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReportFilters"], {
                                            filters: reportFilters,
                                            onFiltersChange: setReportFilters,
                                            reportType: selectedReportType
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                            lineNumber: 192,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 191,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 175,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                    className: "pt-6",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-between",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                        className: "text-lg font-medium",
                                                        children: "Ready to Generate"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                                        lineNumber: 205,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-muted-foreground",
                                                        children: [
                                                            selectedReportType,
                                                            " report with ",
                                                            Object.keys(reportFilters).length,
                                                            " filters applied"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                                        lineNumber: 206,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                                lineNumber: 204,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                size: "lg",
                                                onClick: handleGenerateReport,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                                                        className: "mr-2 h-5 w-5"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                                        lineNumber: 211,
                                                        columnNumber: 19
                                                    }, this),
                                                    "Generate Report"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                                lineNumber: 210,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                        lineNumber: 203,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                    lineNumber: 202,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                                lineNumber: 201,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 174,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "library",
                        className: "space-y-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$reports$2d$library$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReportsLibrary"], {
                            reports: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$data$2f$reports$2d$mock$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mockGeneratedReports"],
                            onDownload: handleDownload,
                            onPreview: handlePreview,
                            onDelete: handleDelete,
                            onArchive: handleArchive,
                            onShare: handleShare,
                            onRegenerate: handleRegenerate
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                            lineNumber: 220,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 219,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "bulk",
                        className: "space-y-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$bulk$2d$report$2d$generator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BulkReportGenerator"], {
                            onGenerate: (configs)=>{
                                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Bulk generation started for ".concat(configs.length, " reports"));
                            }
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                            lineNumber: 232,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 231,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "scheduled",
                        className: "space-y-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$scheduler$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReportScheduler"], {}, void 0, false, {
                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                            lineNumber: 240,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 239,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "archive",
                        className: "space-y-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$archive$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReportArchive"], {}, void 0, false, {
                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                            lineNumber: 244,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 243,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "analytics",
                        className: "space-y-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$analytics$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReportAnalytics"], {}, void 0, false, {
                            fileName: "[project]/app/(dashboard)/reports/page.tsx",
                            lineNumber: 248,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/reports/page.tsx",
                        lineNumber: 247,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 164,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$sf2$2d$report$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SF2ReportDialog"], {
                open: showSF2Dialog,
                onOpenChange: setShowSF2Dialog,
                config: {
                    id: "temp-".concat(Date.now()),
                    name: "SF2 Report - ".concat(selectedReportType),
                    type: selectedReportType,
                    description: "SF2 Daily Attendance Report",
                    dateRange: {
                        startDate: new Date().toISOString().split('T')[0],
                        endDate: new Date().toISOString().split('T')[0]
                    },
                    filters: reportFilters,
                    settings: {
                        includeSignatures: true,
                        pageOrientation: "portrait"
                    },
                    createdBy: "current-user",
                    createdAt: new Date().toISOString(),
                    lastModified: new Date().toISOString()
                }
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 253,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$sf4$2d$report$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SF4ReportDialog"], {
                open: showSF4Dialog,
                onOpenChange: setShowSF4Dialog,
                config: {
                    id: "temp-".concat(Date.now()),
                    name: "SF4 Report - ".concat(selectedReportType),
                    type: selectedReportType,
                    description: "SF4 Monthly Learner's Movement Report",
                    dateRange: {
                        startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
                        endDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString().split('T')[0]
                    },
                    filters: reportFilters,
                    settings: {
                        includeSignatures: true,
                        pageOrientation: "landscape"
                    },
                    createdBy: "current-user",
                    createdAt: new Date().toISOString(),
                    lastModified: new Date().toISOString()
                }
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 277,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$custom$2d$report$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CustomReportDialog"], {
                open: showCustomDialog,
                onOpenChange: setShowCustomDialog
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 301,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$reports$2f$report$2d$wizard$2d$dialog$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReportWizardDialog"], {
                open: showWizardDialog,
                onOpenChange: setShowWizardDialog,
                onComplete: (config)=>{
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success("Report wizard completed", {
                        description: "".concat(config.name, " is being generated")
                    });
                }
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/reports/page.tsx",
                lineNumber: 307,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/reports/page.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
}
_s(ReportsPage, "hmkyHa5psb+tfA4W2TI6cuVoEK8=");
_c = ReportsPage;
var _c;
__turbopack_context__.k.register(_c, "ReportsPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_0a61df60._.js.map