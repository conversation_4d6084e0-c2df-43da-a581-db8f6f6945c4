{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/report-type-selector.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { ReportType } from \"@/lib/types/reports\"\nimport { \n  FileText, \n  Calendar, \n  Users, \n  BarChart3, \n  ClipboardList,\n  TrendingUp,\n  BookOpen,\n  UserCheck\n} from \"lucide-react\"\n\ninterface ReportTypeOption {\n  type: ReportType\n  title: string\n  description: string\n  icon: React.ComponentType<{ className?: string }>\n  badge?: string\n  features: string[]\n  recommended?: boolean\n}\n\nconst reportTypeOptions: ReportTypeOption[] = [\n  {\n    type: \"SF2\",\n    title: \"SF2 Daily Attendance\",\n    description: \"Official DepEd SF2 Daily Attendance Report for class records\",\n    icon: ClipboardList,\n    badge: \"DepEd Official\",\n    features: [\n      \"Daily attendance tracking\",\n      \"Teacher signature fields\",\n      \"Class schedule integration\",\n      \"Absence reason coding\",\n      \"Print-ready format\"\n    ],\n    recommended: true\n  },\n  {\n    type: \"SF4\",\n    title: \"SF4 Monthly Movement\",\n    description: \"Official DepEd SF4 Monthly Learner's Movement Report\",\n    icon: TrendingUp,\n    badge: \"DepEd Official\",\n    features: [\n      \"Monthly enrollment summary\",\n      \"Transfer student tracking\",\n      \"Dropout identification\",\n      \"Statistical summaries\",\n      \"Principal review interface\"\n    ],\n    recommended: true\n  },\n  {\n    type: \"DAILY\",\n    title: \"Daily Summary Report\",\n    description: \"Comprehensive daily attendance summary for all grades\",\n    icon: Calendar,\n    features: [\n      \"All grades overview\",\n      \"Real-time statistics\",\n      \"Late arrival tracking\",\n      \"Quick export options\",\n      \"Mobile-friendly format\"\n    ]\n  },\n  {\n    type: \"WEEKLY\",\n    title: \"Weekly Attendance Report\",\n    description: \"Weekly attendance trends and patterns analysis\",\n    icon: BarChart3,\n    features: [\n      \"7-day attendance trends\",\n      \"Grade-level comparisons\",\n      \"Pattern identification\",\n      \"Statistical analysis\",\n      \"Visual charts included\"\n    ]\n  },\n  {\n    type: \"MONTHLY\",\n    title: \"Monthly Summary Report\",\n    description: \"Comprehensive monthly attendance and performance overview\",\n    icon: FileText,\n    features: [\n      \"Monthly statistics\",\n      \"Attendance rate trends\",\n      \"Student performance metrics\",\n      \"Comparative analysis\",\n      \"Executive summary\"\n    ]\n  },\n  {\n    type: \"CUSTOM\",\n    title: \"Custom Report Builder\",\n    description: \"Build custom reports with flexible filters and formats\",\n    icon: Users,\n    badge: \"Flexible\",\n    features: [\n      \"Custom date ranges\",\n      \"Advanced filtering\",\n      \"Multiple export formats\",\n      \"Template saving\",\n      \"Scheduled generation\"\n    ]\n  }\n]\n\ninterface ReportTypeSelectorProps {\n  selectedType?: ReportType\n  onTypeSelect: (type: ReportType) => void\n  className?: string\n}\n\nexport function ReportTypeSelector({ \n  selectedType, \n  onTypeSelect, \n  className \n}: ReportTypeSelectorProps) {\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle>Select Report Type</CardTitle>\n        <CardDescription>\n          Choose the type of report you want to generate\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n          {reportTypeOptions.map((option) => {\n            const Icon = option.icon\n            const isSelected = selectedType === option.type\n            \n            return (\n              <Card \n                key={option.type}\n                className={`cursor-pointer transition-all hover:shadow-md ${\n                  isSelected \n                    ? 'ring-2 ring-primary border-primary' \n                    : 'hover:border-primary/50'\n                } ${option.recommended ? 'relative' : ''}`}\n                onClick={() => onTypeSelect(option.type)}\n              >\n                {option.recommended && (\n                  <div className=\"absolute -top-2 -right-2\">\n                    <Badge variant=\"default\" className=\"text-xs\">\n                      Recommended\n                    </Badge>\n                  </div>\n                )}\n                \n                <CardHeader className=\"pb-3\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`p-2 rounded-lg ${\n                        isSelected \n                          ? 'bg-primary text-primary-foreground' \n                          : 'bg-muted'\n                      }`}>\n                        <Icon className=\"h-5 w-5\" />\n                      </div>\n                      <div>\n                        <CardTitle className=\"text-base\">{option.title}</CardTitle>\n                        {option.badge && (\n                          <Badge variant=\"secondary\" className=\"text-xs mt-1\">\n                            {option.badge}\n                          </Badge>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                  <CardDescription className=\"text-sm\">\n                    {option.description}\n                  </CardDescription>\n                </CardHeader>\n                \n                <CardContent className=\"pt-0\">\n                  <div className=\"space-y-2\">\n                    <h4 className=\"text-sm font-medium text-muted-foreground\">\n                      Key Features:\n                    </h4>\n                    <ul className=\"space-y-1\">\n                      {option.features.slice(0, 3).map((feature, index) => (\n                        <li key={index} className=\"text-xs text-muted-foreground flex items-center gap-2\">\n                          <div className=\"w-1 h-1 bg-muted-foreground rounded-full\" />\n                          {feature}\n                        </li>\n                      ))}\n                      {option.features.length > 3 && (\n                        <li className=\"text-xs text-muted-foreground\">\n                          +{option.features.length - 3} more features\n                        </li>\n                      )}\n                    </ul>\n                  </div>\n                  \n                  <Button \n                    variant={isSelected ? \"default\" : \"outline\"} \n                    size=\"sm\" \n                    className=\"w-full mt-4\"\n                    onClick={(e) => {\n                      e.stopPropagation()\n                      onTypeSelect(option.type)\n                    }}\n                  >\n                    {isSelected ? 'Selected' : 'Select'}\n                  </Button>\n                </CardContent>\n              </Card>\n            )\n          })}\n        </div>\n        \n        {/* Quick Actions for Popular Reports */}\n        <div className=\"mt-6 pt-6 border-t\">\n          <h3 className=\"text-sm font-medium mb-3\">Quick Actions</h3>\n          <div className=\"flex flex-wrap gap-2\">\n            <Button \n              variant=\"outline\" \n              size=\"sm\"\n              onClick={() => onTypeSelect(\"SF2\")}\n              className=\"text-xs\"\n            >\n              <ClipboardList className=\"h-3 w-3 mr-1\" />\n              Today's SF2\n            </Button>\n            <Button \n              variant=\"outline\" \n              size=\"sm\"\n              onClick={() => onTypeSelect(\"SF4\")}\n              className=\"text-xs\"\n            >\n              <TrendingUp className=\"h-3 w-3 mr-1\" />\n              This Month's SF4\n            </Button>\n            <Button \n              variant=\"outline\" \n              size=\"sm\"\n              onClick={() => onTypeSelect(\"WEEKLY\")}\n              className=\"text-xs\"\n            >\n              <BarChart3 className=\"h-3 w-3 mr-1\" />\n              Weekly Summary\n            </Button>\n            <Button \n              variant=\"outline\" \n              size=\"sm\"\n              onClick={() => onTypeSelect(\"CUSTOM\")}\n              className=\"text-xs\"\n            >\n              <Users className=\"h-3 w-3 mr-1\" />\n              Custom Report\n            </Button>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n\n// Helper function to get report type info\nexport function getReportTypeInfo(type: ReportType): ReportTypeOption | undefined {\n  return reportTypeOptions.find(option => option.type === type)\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;AA2BA,MAAM,oBAAwC;IAC5C;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,2NAAA,CAAA,gBAAa;QACnB,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,qNAAA,CAAA,aAAU;QAChB,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,6MAAA,CAAA,WAAQ;QACd,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,qNAAA,CAAA,YAAS;QACf,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,iNAAA,CAAA,WAAQ;QACd,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;CACD;AAQM,SAAS,mBAAmB,KAIT;QAJS,EACjC,YAAY,EACZ,YAAY,EACZ,SAAS,EACe,GAJS;IAKjC,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,4HAAA,CAAA,aAAU;;kCACT,6LAAC,4HAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,6LAAC,4HAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,4HAAA,CAAA,cAAW;;kCACV,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC;4BACtB,MAAM,OAAO,OAAO,IAAI;4BACxB,MAAM,aAAa,iBAAiB,OAAO,IAAI;4BAE/C,qBACE,6LAAC,4HAAA,CAAA,OAAI;gCAEH,WAAW,AAAC,iDAIR,OAHF,aACI,uCACA,2BACL,KAAwC,OAArC,OAAO,WAAW,GAAG,aAAa;gCACtC,SAAS,IAAM,aAAa,OAAO,IAAI;;oCAEtC,OAAO,WAAW,kBACjB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAU;;;;;;;;;;;kDAMjD,6LAAC,4HAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,AAAC,kBAIhB,OAHC,aACI,uCACA;sEAEJ,cAAA,6LAAC;gEAAK,WAAU;;;;;;;;;;;sEAElB,6LAAC;;8EACC,6LAAC,4HAAA,CAAA,YAAS;oEAAC,WAAU;8EAAa,OAAO,KAAK;;;;;;gEAC7C,OAAO,KAAK,kBACX,6LAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;8EAClC,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;0DAMvB,6LAAC,4HAAA,CAAA,kBAAe;gDAAC,WAAU;0DACxB,OAAO,WAAW;;;;;;;;;;;;kDAIvB,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAG1D,6LAAC;wDAAG,WAAU;;4DACX,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC;oEAAe,WAAU;;sFACxB,6LAAC;4EAAI,WAAU;;;;;;wEACd;;mEAFM;;;;;4DAKV,OAAO,QAAQ,CAAC,MAAM,GAAG,mBACxB,6LAAC;gEAAG,WAAU;;oEAAgC;oEAC1C,OAAO,QAAQ,CAAC,MAAM,GAAG;oEAAE;;;;;;;;;;;;;;;;;;;0DAMrC,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAS,aAAa,YAAY;gDAClC,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,aAAa,OAAO,IAAI;gDAC1B;0DAEC,aAAa,aAAa;;;;;;;;;;;;;+BAtE1B,OAAO,IAAI;;;;;wBA2EtB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAG5C,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGzC,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa;wCAC5B,WAAU;;0DAEV,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;KAhJgB;AAmJT,SAAS,kBAAkB,IAAgB;IAChD,OAAO,kBAAkB,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAC1D", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/report-filters.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { DateRangePicker, DateRange, dateRangePresets } from \"@/components/ui/date-range-picker\"\nimport { ReportFilters as ReportFiltersType, ReportType } from \"@/lib/types/reports\"\nimport { Filter, X, Calendar } from \"lucide-react\"\n\ninterface ReportFiltersProps {\n  filters: ReportFiltersType\n  onFiltersChange: (filters: ReportFiltersType) => void\n  reportType: ReportType\n  className?: string\n}\n\n// Mock data for filter options\nconst gradeOptions = [\"7\", \"8\", \"9\", \"10\", \"11\", \"12\"]\nconst sectionOptions = [\"A\", \"B\", \"C\", \"D\", \"E\"]\nconst courseOptions = [\"Junior High School\", \"Senior High School - STEM\", \"Senior High School - ABM\", \"Senior High School - HUMSS\", \"Senior High School - GAS\"]\nconst subjectOptions = [\"Mathematics\", \"English\", \"Science\", \"Filipino\", \"Social Studies\", \"PE\", \"TLE\", \"Arts\", \"Music\"]\nconst teacherOptions = [\"Prof. Santos\", \"Mrs. Reyes\", \"Mr. Torres\", \"Ms. Garcia\", \"Dr. Rodriguez\"]\nconst attendanceStatusOptions = [\"Present\", \"Late\", \"Absent\"]\n\nexport function ReportFilters({ \n  filters, \n  onFiltersChange, \n  reportType,\n  className \n}: ReportFiltersProps) {\n  const [dateRange, setDateRange] = useState<DateRange>({\n    from: filters.dateRange ? new Date(filters.dateRange.startDate) : undefined,\n    to: filters.dateRange ? new Date(filters.dateRange.endDate) : undefined\n  })\n\n  const handleDateRangeChange = (range: DateRange) => {\n    setDateRange(range)\n    if (range.from && range.to) {\n      onFiltersChange({\n        ...filters,\n        dateRange: {\n          startDate: range.from.toISOString().split('T')[0],\n          endDate: range.to.toISOString().split('T')[0]\n        }\n      })\n    }\n  }\n\n  const handleArrayFilterChange = (key: keyof ReportFiltersType, value: string, checked: boolean) => {\n    const currentArray = (filters[key] as string[]) || []\n    let newArray: string[]\n    \n    if (checked) {\n      newArray = [...currentArray, value]\n    } else {\n      newArray = currentArray.filter(item => item !== value)\n    }\n    \n    onFiltersChange({\n      ...filters,\n      [key]: newArray.length > 0 ? newArray : undefined\n    })\n  }\n\n  const handleBooleanFilterChange = (key: keyof ReportFiltersType, checked: boolean) => {\n    onFiltersChange({\n      ...filters,\n      [key]: checked\n    })\n  }\n\n  const clearFilters = () => {\n    onFiltersChange({})\n    setDateRange({ from: undefined, to: undefined })\n  }\n\n  const applyPreset = (preset: typeof dateRangePresets[0]) => {\n    const range = preset.value()\n    setDateRange(range)\n    onFiltersChange({\n      ...filters,\n      dateRange: {\n        startDate: range.from.toISOString().split('T')[0],\n        endDate: range.to.toISOString().split('T')[0]\n      }\n    })\n  }\n\n  const getActiveFiltersCount = () => {\n    let count = 0\n    if (filters.grades?.length) count++\n    if (filters.sections?.length) count++\n    if (filters.courses?.length) count++\n    if (filters.students?.length) count++\n    if (filters.subjects?.length) count++\n    if (filters.teachers?.length) count++\n    if (filters.attendanceStatus?.length) count++\n    if (filters.includeTransferred) count++\n    if (filters.includeInactive) count++\n    return count\n  }\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-2\">\n            <Filter className=\"h-5 w-5\" />\n            Report Filters\n            {getActiveFiltersCount() > 0 && (\n              <Badge variant=\"secondary\">{getActiveFiltersCount()} active</Badge>\n            )}\n          </div>\n          <Button variant=\"ghost\" size=\"sm\" onClick={clearFilters}>\n            <X className=\"h-4 w-4 mr-1\" />\n            Clear All\n          </Button>\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        {/* Date Range */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Date Range</Label>\n          <DateRangePicker\n            value={dateRange}\n            onChange={handleDateRangeChange}\n            className=\"w-full\"\n          />\n          \n          {/* Quick Date Presets */}\n          <div className=\"flex flex-wrap gap-2\">\n            {dateRangePresets.map((preset) => (\n              <Button\n                key={preset.label}\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => applyPreset(preset)}\n                className=\"text-xs\"\n              >\n                <Calendar className=\"h-3 w-3 mr-1\" />\n                {preset.label}\n              </Button>\n            ))}\n          </div>\n        </div>\n\n        {/* Grade Levels */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Grade Levels</Label>\n          <div className=\"grid grid-cols-3 gap-2\">\n            {gradeOptions.map((grade) => (\n              <div key={grade} className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id={`grade-${grade}`}\n                  checked={filters.grades?.includes(grade) || false}\n                  onCheckedChange={(checked) => \n                    handleArrayFilterChange('grades', grade, checked as boolean)\n                  }\n                />\n                <Label htmlFor={`grade-${grade}`} className=\"text-sm\">\n                  Grade {grade}\n                </Label>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Sections */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Sections</Label>\n          <div className=\"grid grid-cols-5 gap-2\">\n            {sectionOptions.map((section) => (\n              <div key={section} className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id={`section-${section}`}\n                  checked={filters.sections?.includes(section) || false}\n                  onCheckedChange={(checked) => \n                    handleArrayFilterChange('sections', section, checked as boolean)\n                  }\n                />\n                <Label htmlFor={`section-${section}`} className=\"text-sm\">\n                  {section}\n                </Label>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Courses */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Courses</Label>\n          <div className=\"space-y-2\">\n            {courseOptions.map((course) => (\n              <div key={course} className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id={`course-${course}`}\n                  checked={filters.courses?.includes(course) || false}\n                  onCheckedChange={(checked) => \n                    handleArrayFilterChange('courses', course, checked as boolean)\n                  }\n                />\n                <Label htmlFor={`course-${course}`} className=\"text-sm\">\n                  {course}\n                </Label>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Subjects (for subject-specific reports) */}\n        {(reportType === 'CUSTOM' || reportType === 'SF2') && (\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium\">Subjects</Label>\n            <div className=\"space-y-2\">\n              {subjectOptions.map((subject) => (\n                <div key={subject} className=\"flex items-center space-x-2\">\n                  <Checkbox\n                    id={`subject-${subject}`}\n                    checked={filters.subjects?.includes(subject) || false}\n                    onCheckedChange={(checked) => \n                      handleArrayFilterChange('subjects', subject, checked as boolean)\n                    }\n                  />\n                  <Label htmlFor={`subject-${subject}`} className=\"text-sm\">\n                    {subject}\n                  </Label>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Attendance Status */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Attendance Status</Label>\n          <div className=\"space-y-2\">\n            {attendanceStatusOptions.map((status) => (\n              <div key={status} className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id={`status-${status}`}\n                  checked={filters.attendanceStatus?.includes(status as any) || false}\n                  onCheckedChange={(checked) => \n                    handleArrayFilterChange('attendanceStatus', status, checked as boolean)\n                  }\n                />\n                <Label htmlFor={`status-${status}`} className=\"text-sm\">\n                  {status}\n                </Label>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Additional Options */}\n        <div className=\"space-y-3\">\n          <Label className=\"text-sm font-medium\">Additional Options</Label>\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-transferred\"\n                checked={filters.includeTransferred || false}\n                onCheckedChange={(checked) => \n                  handleBooleanFilterChange('includeTransferred', checked as boolean)\n                }\n              />\n              <Label htmlFor=\"include-transferred\" className=\"text-sm\">\n                Include transferred students\n              </Label>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-inactive\"\n                checked={filters.includeInactive || false}\n                onCheckedChange={(checked) => \n                  handleBooleanFilterChange('includeInactive', checked as boolean)\n                }\n              />\n              <Label htmlFor=\"include-inactive\" className=\"text-sm\">\n                Include inactive students\n              </Label>\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;;;AAZA;;;;;;;;;AAqBA,+BAA+B;AAC/B,MAAM,eAAe;IAAC;IAAK;IAAK;IAAK;IAAM;IAAM;CAAK;AACtD,MAAM,iBAAiB;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI;AAChD,MAAM,gBAAgB;IAAC;IAAsB;IAA6B;IAA4B;IAA8B;CAA2B;AAC/J,MAAM,iBAAiB;IAAC;IAAe;IAAW;IAAW;IAAY;IAAkB;IAAM;IAAO;IAAQ;CAAQ;AACxH,MAAM,iBAAiB;IAAC;IAAgB;IAAc;IAAc;IAAc;CAAgB;AAClG,MAAM,0BAA0B;IAAC;IAAW;IAAQ;CAAS;AAEtD,SAAS,cAAc,KAKT;QALS,EAC5B,OAAO,EACP,eAAe,EACf,UAAU,EACV,SAAS,EACU,GALS;;IAM5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM,QAAQ,SAAS,GAAG,IAAI,KAAK,QAAQ,SAAS,CAAC,SAAS,IAAI;QAClE,IAAI,QAAQ,SAAS,GAAG,IAAI,KAAK,QAAQ,SAAS,CAAC,OAAO,IAAI;IAChE;IAEA,MAAM,wBAAwB,CAAC;QAC7B,aAAa;QACb,IAAI,MAAM,IAAI,IAAI,MAAM,EAAE,EAAE;YAC1B,gBAAgB;gBACd,GAAG,OAAO;gBACV,WAAW;oBACT,WAAW,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACjD,SAAS,MAAM,EAAE,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC/C;YACF;QACF;IACF;IAEA,MAAM,0BAA0B,CAAC,KAA8B,OAAe;QAC5E,MAAM,eAAe,AAAC,OAAO,CAAC,IAAI,IAAiB,EAAE;QACrD,IAAI;QAEJ,IAAI,SAAS;YACX,WAAW;mBAAI;gBAAc;aAAM;QACrC,OAAO;YACL,WAAW,aAAa,MAAM,CAAC,CAAA,OAAQ,SAAS;QAClD;QAEA,gBAAgB;YACd,GAAG,OAAO;YACV,CAAC,IAAI,EAAE,SAAS,MAAM,GAAG,IAAI,WAAW;QAC1C;IACF;IAEA,MAAM,4BAA4B,CAAC,KAA8B;QAC/D,gBAAgB;YACd,GAAG,OAAO;YACV,CAAC,IAAI,EAAE;QACT;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB,CAAC;QACjB,aAAa;YAAE,MAAM;YAAW,IAAI;QAAU;IAChD;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ,OAAO,KAAK;QAC1B,aAAa;QACb,gBAAgB;YACd,GAAG,OAAO;YACV,WAAW;gBACT,WAAW,MAAM,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACjD,SAAS,MAAM,EAAE,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC/C;QACF;IACF;IAEA,MAAM,wBAAwB;YAExB,iBACA,mBACA,kBACA,mBACA,mBACA,mBACA;QAPJ,IAAI,QAAQ;QACZ,KAAI,kBAAA,QAAQ,MAAM,cAAd,sCAAA,gBAAgB,MAAM,EAAE;QAC5B,KAAI,oBAAA,QAAQ,QAAQ,cAAhB,wCAAA,kBAAkB,MAAM,EAAE;QAC9B,KAAI,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,MAAM,EAAE;QAC7B,KAAI,oBAAA,QAAQ,QAAQ,cAAhB,wCAAA,kBAAkB,MAAM,EAAE;QAC9B,KAAI,oBAAA,QAAQ,QAAQ,cAAhB,wCAAA,kBAAkB,MAAM,EAAE;QAC9B,KAAI,oBAAA,QAAQ,QAAQ,cAAhB,wCAAA,kBAAkB,MAAM,EAAE;QAC9B,KAAI,4BAAA,QAAQ,gBAAgB,cAAxB,gDAAA,0BAA0B,MAAM,EAAE;QACtC,IAAI,QAAQ,kBAAkB,EAAE;QAChC,IAAI,QAAQ,eAAe,EAAE;QAC7B,OAAO;IACT;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,4HAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;gCAE7B,0BAA0B,mBACzB,6LAAC,6HAAA,CAAA,QAAK;oCAAC,SAAQ;;wCAAa;wCAAwB;;;;;;;;;;;;;sCAGxD,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,SAAS;;8CACzC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAKpC,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,6LAAC,+IAAA,CAAA,kBAAe;gCACd,OAAO;gCACP,UAAU;gCACV,WAAU;;;;;;0CAIZ,6LAAC;gCAAI,WAAU;0CACZ,+IAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,uBACrB,6LAAC,8HAAA,CAAA,SAAM;wCAEL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,OAAO,KAAK;;uCAPR,OAAO,KAAK;;;;;;;;;;;;;;;;kCAczB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC;wCAIJ;yDAHb,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC,gIAAA,CAAA,WAAQ;gDACP,IAAI,AAAC,SAAc,OAAN;gDACb,SAAS,EAAA,kBAAA,QAAQ,MAAM,cAAd,sCAAA,gBAAgB,QAAQ,CAAC,WAAU;gDAC5C,iBAAiB,CAAC,UAChB,wBAAwB,UAAU,OAAO;;;;;;0DAG7C,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAS,AAAC,SAAc,OAAN;gDAAS,WAAU;;oDAAU;oDAC7C;;;;;;;;uCATD;;;;;;;;;;;;;;;;;kCAiBhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,6LAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC;wCAIN;yDAHb,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC,gIAAA,CAAA,WAAQ;gDACP,IAAI,AAAC,WAAkB,OAAR;gDACf,SAAS,EAAA,oBAAA,QAAQ,QAAQ,cAAhB,wCAAA,kBAAkB,QAAQ,CAAC,aAAY;gDAChD,iBAAiB,CAAC,UAChB,wBAAwB,YAAY,SAAS;;;;;;0DAGjD,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAS,AAAC,WAAkB,OAAR;gDAAW,WAAU;0DAC7C;;;;;;;uCATK;;;;;;;;;;;;;;;;;kCAiBhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC;wCAIL;yDAHb,6LAAC;wCAAiB,WAAU;;0DAC1B,6LAAC,gIAAA,CAAA,WAAQ;gDACP,IAAI,AAAC,UAAgB,OAAP;gDACd,SAAS,EAAA,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,QAAQ,CAAC,YAAW;gDAC9C,iBAAiB,CAAC,UAChB,wBAAwB,WAAW,QAAQ;;;;;;0DAG/C,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAS,AAAC,UAAgB,OAAP;gDAAU,WAAU;0DAC3C;;;;;;;uCATK;;;;;;;;;;;;;;;;;oBAiBf,CAAC,eAAe,YAAY,eAAe,KAAK,mBAC/C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,6LAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC;wCAIN;yDAHb,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC,gIAAA,CAAA,WAAQ;gDACP,IAAI,AAAC,WAAkB,OAAR;gDACf,SAAS,EAAA,oBAAA,QAAQ,QAAQ,cAAhB,wCAAA,kBAAkB,QAAQ,CAAC,aAAY;gDAChD,iBAAiB,CAAC,UAChB,wBAAwB,YAAY,SAAS;;;;;;0DAGjD,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAS,AAAC,WAAkB,OAAR;gDAAW,WAAU;0DAC7C;;;;;;;uCATK;;;;;;;;;;;;;;;;;kCAkBlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,6LAAC;gCAAI,WAAU;0CACZ,wBAAwB,GAAG,CAAC,CAAC;wCAIf;yDAHb,6LAAC;wCAAiB,WAAU;;0DAC1B,6LAAC,gIAAA,CAAA,WAAQ;gDACP,IAAI,AAAC,UAAgB,OAAP;gDACd,SAAS,EAAA,4BAAA,QAAQ,gBAAgB,cAAxB,gDAAA,0BAA0B,QAAQ,CAAC,YAAkB;gDAC9D,iBAAiB,CAAC,UAChB,wBAAwB,oBAAoB,QAAQ;;;;;;0DAGxD,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAS,AAAC,UAAgB,OAAP;gDAAU,WAAU;0DAC3C;;;;;;;uCATK;;;;;;;;;;;;;;;;;kCAiBhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,QAAK;gCAAC,WAAU;0CAAsB;;;;;;0CACvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,SAAS,QAAQ,kBAAkB,IAAI;gDACvC,iBAAiB,CAAC,UAChB,0BAA0B,sBAAsB;;;;;;0DAGpD,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAsB,WAAU;0DAAU;;;;;;;;;;;;kDAI3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,SAAS,QAAQ,eAAe,IAAI;gDACpC,iBAAiB,CAAC,UAChB,0BAA0B,mBAAmB;;;;;;0DAGjD,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAmB,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;GAtQgB;KAAA", "debugId": null}}, {"offset": {"line": 1106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/reports-library.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Input } from \"@/components/ui/input\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\"\nimport { GeneratedReport, ReportStatus, ReportType, ExportFormat } from \"@/lib/types/reports\"\nimport { \n  Download, \n  Eye, \n  MoreHorizontal, \n  Search, \n  Filter,\n  FileText,\n  Calendar,\n  Clock,\n  Users,\n  Trash2,\n  Share,\n  Archive,\n  RefreshCw\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\ninterface ReportsLibraryProps {\n  reports: GeneratedReport[]\n  onDownload: (reportId: string, format: ExportFormat) => void\n  onPreview: (reportId: string) => void\n  onDelete: (reportId: string) => void\n  onArchive: (reportId: string) => void\n  onShare: (reportId: string) => void\n  onRegenerate: (reportId: string) => void\n  className?: string\n}\n\nexport function ReportsLibrary({\n  reports,\n  onDownload,\n  onPreview,\n  onDelete,\n  onArchive,\n  onShare,\n  onRegenerate,\n  className\n}: ReportsLibraryProps) {\n  const [searchQuery, setSearchQuery] = useState(\"\")\n  const [statusFilter, setStatusFilter] = useState<ReportStatus | \"ALL\">(\"ALL\")\n  const [typeFilter, setTypeFilter] = useState<ReportType | \"ALL\">(\"ALL\")\n  const [sortBy, setSortBy] = useState<\"date\" | \"name\" | \"type\" | \"downloads\">(\"date\")\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"desc\")\n\n  // Filter and sort reports\n  const filteredReports = reports\n    .filter(report => {\n      const matchesSearch = report.config.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                           report.config.description.toLowerCase().includes(searchQuery.toLowerCase())\n      const matchesStatus = statusFilter === \"ALL\" || report.status === statusFilter\n      const matchesType = typeFilter === \"ALL\" || report.config.type === typeFilter\n      return matchesSearch && matchesStatus && matchesType\n    })\n    .sort((a, b) => {\n      let comparison = 0\n      switch (sortBy) {\n        case \"date\":\n          comparison = new Date(a.generatedAt).getTime() - new Date(b.generatedAt).getTime()\n          break\n        case \"name\":\n          comparison = a.config.name.localeCompare(b.config.name)\n          break\n        case \"type\":\n          comparison = a.config.type.localeCompare(b.config.type)\n          break\n        case \"downloads\":\n          comparison = a.downloadCount - b.downloadCount\n          break\n      }\n      return sortOrder === \"desc\" ? -comparison : comparison\n    })\n\n  const getStatusBadge = (status: ReportStatus) => {\n    const variants = {\n      READY: \"default\",\n      GENERATING: \"secondary\",\n      DRAFT: \"outline\",\n      FAILED: \"destructive\",\n      ARCHIVED: \"secondary\"\n    } as const\n\n    const colors = {\n      READY: \"text-green-600\",\n      GENERATING: \"text-blue-600\",\n      DRAFT: \"text-gray-600\",\n      FAILED: \"text-red-600\",\n      ARCHIVED: \"text-gray-500\"\n    } as const\n\n    return (\n      <Badge variant={variants[status]} className={colors[status]}>\n        {status === \"GENERATING\" && <RefreshCw className=\"h-3 w-3 mr-1 animate-spin\" />}\n        {status}\n      </Badge>\n    )\n  }\n\n  const getTypeBadge = (type: ReportType) => {\n    const colors = {\n      SF2: \"bg-blue-100 text-blue-800\",\n      SF4: \"bg-green-100 text-green-800\",\n      DAILY: \"bg-purple-100 text-purple-800\",\n      WEEKLY: \"bg-orange-100 text-orange-800\",\n      MONTHLY: \"bg-pink-100 text-pink-800\",\n      ANNUAL: \"bg-indigo-100 text-indigo-800\",\n      CUSTOM: \"bg-gray-100 text-gray-800\"\n    } as const\n\n    return (\n      <Badge variant=\"secondary\" className={colors[type]}>\n        {type}\n      </Badge>\n    )\n  }\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes'\n    const k = 1024\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <CardTitle className=\"flex items-center gap-2\">\n              <FileText className=\"h-5 w-5\" />\n              Generated Reports\n              <Badge variant=\"secondary\">{filteredReports.length}</Badge>\n            </CardTitle>\n            <CardDescription>\n              View, download, and manage your generated reports\n            </CardDescription>\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {/* Filters and Search */}\n        <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\n          <div className=\"relative flex-1\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Search reports...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n          \n          <div className=\"flex gap-2\">\n            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as ReportStatus | \"ALL\")}>\n              <SelectTrigger className=\"w-32\">\n                <SelectValue placeholder=\"Status\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"ALL\">All Status</SelectItem>\n                <SelectItem value=\"READY\">Ready</SelectItem>\n                <SelectItem value=\"GENERATING\">Generating</SelectItem>\n                <SelectItem value=\"DRAFT\">Draft</SelectItem>\n                <SelectItem value=\"FAILED\">Failed</SelectItem>\n                <SelectItem value=\"ARCHIVED\">Archived</SelectItem>\n              </SelectContent>\n            </Select>\n\n            <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as ReportType | \"ALL\")}>\n              <SelectTrigger className=\"w-32\">\n                <SelectValue placeholder=\"Type\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"ALL\">All Types</SelectItem>\n                <SelectItem value=\"SF2\">SF2</SelectItem>\n                <SelectItem value=\"SF4\">SF4</SelectItem>\n                <SelectItem value=\"DAILY\">Daily</SelectItem>\n                <SelectItem value=\"WEEKLY\">Weekly</SelectItem>\n                <SelectItem value=\"MONTHLY\">Monthly</SelectItem>\n                <SelectItem value=\"CUSTOM\">Custom</SelectItem>\n              </SelectContent>\n            </Select>\n\n            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {\n              const [field, order] = value.split('-')\n              setSortBy(field as typeof sortBy)\n              setSortOrder(order as typeof sortOrder)\n            }}>\n              <SelectTrigger className=\"w-40\">\n                <SelectValue placeholder=\"Sort by\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"date-desc\">Newest First</SelectItem>\n                <SelectItem value=\"date-asc\">Oldest First</SelectItem>\n                <SelectItem value=\"name-asc\">Name A-Z</SelectItem>\n                <SelectItem value=\"name-desc\">Name Z-A</SelectItem>\n                <SelectItem value=\"downloads-desc\">Most Downloaded</SelectItem>\n                <SelectItem value=\"downloads-asc\">Least Downloaded</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </div>\n\n        {/* Reports Table */}\n        <div className=\"rounded-md border\">\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Report</TableHead>\n                <TableHead>Type</TableHead>\n                <TableHead>Status</TableHead>\n                <TableHead>Generated</TableHead>\n                <TableHead>Size</TableHead>\n                <TableHead>Downloads</TableHead>\n                <TableHead className=\"text-right\">Actions</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {filteredReports.length === 0 ? (\n                <TableRow>\n                  <TableCell colSpan={7} className=\"text-center py-8 text-muted-foreground\">\n                    No reports found matching your criteria\n                  </TableCell>\n                </TableRow>\n              ) : (\n                filteredReports.map((report) => (\n                  <TableRow key={report.id}>\n                    <TableCell>\n                      <div className=\"space-y-1\">\n                        <div className=\"font-medium\">{report.config.name}</div>\n                        <div className=\"text-sm text-muted-foreground\">\n                          {report.config.description}\n                        </div>\n                        <div className=\"text-xs text-muted-foreground\">\n                          {format(new Date(report.config.dateRange.startDate), \"MMM dd\")} - {format(new Date(report.config.dateRange.endDate), \"MMM dd, yyyy\")}\n                        </div>\n                      </div>\n                    </TableCell>\n                    <TableCell>\n                      {getTypeBadge(report.config.type)}\n                    </TableCell>\n                    <TableCell>\n                      {getStatusBadge(report.status)}\n                    </TableCell>\n                    <TableCell>\n                      <div className=\"flex items-center gap-1 text-sm text-muted-foreground\">\n                        <Clock className=\"h-3 w-3\" />\n                        {format(new Date(report.generatedAt), \"MMM dd, HH:mm\")}\n                      </div>\n                    </TableCell>\n                    <TableCell>\n                      <div className=\"text-sm\">\n                        {report.fileSize ? formatFileSize(report.fileSize) : '-'}\n                      </div>\n                    </TableCell>\n                    <TableCell>\n                      <div className=\"flex items-center gap-1 text-sm\">\n                        <Download className=\"h-3 w-3\" />\n                        {report.downloadCount}\n                      </div>\n                    </TableCell>\n                    <TableCell className=\"text-right\">\n                      <div className=\"flex items-center justify-end gap-2\">\n                        {report.status === \"READY\" && (\n                          <>\n                            <Button\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={() => onPreview(report.id)}\n                            >\n                              <Eye className=\"h-4 w-4\" />\n                            </Button>\n                            <DropdownMenu>\n                              <DropdownMenuTrigger asChild>\n                                <Button variant=\"ghost\" size=\"sm\">\n                                  <Download className=\"h-4 w-4\" />\n                                </Button>\n                              </DropdownMenuTrigger>\n                              <DropdownMenuContent align=\"end\">\n                                <DropdownMenuItem onClick={() => onDownload(report.id, \"PDF\")}>\n                                  Download PDF\n                                </DropdownMenuItem>\n                                <DropdownMenuItem onClick={() => onDownload(report.id, \"EXCEL\")}>\n                                  Download Excel\n                                </DropdownMenuItem>\n                                <DropdownMenuItem onClick={() => onDownload(report.id, \"CSV\")}>\n                                  Download CSV\n                                </DropdownMenuItem>\n                              </DropdownMenuContent>\n                            </DropdownMenu>\n                          </>\n                        )}\n                        \n                        <DropdownMenu>\n                          <DropdownMenuTrigger asChild>\n                            <Button variant=\"ghost\" size=\"sm\">\n                              <MoreHorizontal className=\"h-4 w-4\" />\n                            </Button>\n                          </DropdownMenuTrigger>\n                          <DropdownMenuContent align=\"end\">\n                            <DropdownMenuItem onClick={() => onShare(report.id)}>\n                              <Share className=\"h-4 w-4 mr-2\" />\n                              Share\n                            </DropdownMenuItem>\n                            <DropdownMenuItem onClick={() => onRegenerate(report.id)}>\n                              <RefreshCw className=\"h-4 w-4 mr-2\" />\n                              Regenerate\n                            </DropdownMenuItem>\n                            <DropdownMenuItem onClick={() => onArchive(report.id)}>\n                              <Archive className=\"h-4 w-4 mr-2\" />\n                              Archive\n                            </DropdownMenuItem>\n                            <DropdownMenuItem \n                              onClick={() => onDelete(report.id)}\n                              className=\"text-destructive\"\n                            >\n                              <Trash2 className=\"h-4 w-4 mr-2\" />\n                              Delete\n                            </DropdownMenuItem>\n                          </DropdownMenuContent>\n                        </DropdownMenu>\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))\n              )}\n            </TableBody>\n          </Table>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;;;AA1BA;;;;;;;;;;;AAuCO,SAAS,eAAe,KAST;QATS,EAC7B,OAAO,EACP,UAAU,EACV,SAAS,EACT,QAAQ,EACR,SAAS,EACT,OAAO,EACP,YAAY,EACZ,SAAS,EACW,GATS;;IAU7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACjE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0C;IAC7E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,0BAA0B;IAC1B,MAAM,kBAAkB,QACrB,MAAM,CAAC,CAAA;QACN,MAAM,gBAAgB,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAClE,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAC7F,MAAM,gBAAgB,iBAAiB,SAAS,OAAO,MAAM,KAAK;QAClE,MAAM,cAAc,eAAe,SAAS,OAAO,MAAM,CAAC,IAAI,KAAK;QACnE,OAAO,iBAAiB,iBAAiB;IAC3C,GACC,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,aAAa,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;gBAChF;YACF,KAAK;gBACH,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,IAAI;gBACtD;YACF,KAAK;gBACH,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,IAAI;gBACtD;YACF,KAAK;gBACH,aAAa,EAAE,aAAa,GAAG,EAAE,aAAa;gBAC9C;QACJ;QACA,OAAO,cAAc,SAAS,CAAC,aAAa;IAC9C;IAEF,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW;YACf,OAAO;YACP,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;QAEA,MAAM,SAAS;YACb,OAAO;YACP,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,UAAU;QACZ;QAEA,qBACE,6LAAC,6HAAA,CAAA,QAAK;YAAC,SAAS,QAAQ,CAAC,OAAO;YAAE,WAAW,MAAM,CAAC,OAAO;;gBACxD,WAAW,8BAAgB,6LAAC,mNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;gBAChD;;;;;;;IAGP;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,SAAS;YACb,KAAK;YACL,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QAEA,qBACE,6LAAC,6HAAA,CAAA,QAAK;YAAC,SAAQ;YAAY,WAAW,MAAM,CAAC,KAAK;sBAC/C;;;;;;IAGP;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,6LAAC,4HAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,6LAAC,4HAAA,CAAA,aAAU;0BACT,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;0CACC,6LAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;kDAEhC,6LAAC,6HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAa,gBAAgB,MAAM;;;;;;;;;;;;0CAEpD,6LAAC,4HAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;;;;;;;;;;;0BAMvB,6LAAC,4HAAA,CAAA,cAAW;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,6HAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAc,eAAe,CAAC,QAAU,gBAAgB;;0DACrE,6LAAC,8HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,8HAAA,CAAA,gBAAa;;kEACZ,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAa;;;;;;kEAC/B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAIjC,6LAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO;wCAAY,eAAe,CAAC,QAAU,cAAc;;0DACjE,6LAAC,8HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,8HAAA,CAAA,gBAAa;;kEACZ,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;;;;;;;;;;;;;kDAI/B,6LAAC,8HAAA,CAAA,SAAM;wCAAC,OAAO,AAAC,GAAY,OAAV,QAAO,KAAa,OAAV;wCAAa,eAAe,CAAC;4CACvD,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,KAAK,CAAC;4CACnC,UAAU;4CACV,aAAa;wCACf;;0DACE,6LAAC,8HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,8HAAA,CAAA,gBAAa;;kEACZ,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;kEAC7B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;kEAC7B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAiB;;;;;;kEACnC,6LAAC,8HAAA,CAAA,aAAU;wDAAC,OAAM;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;;8CACJ,6LAAC,6HAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,6HAAA,CAAA,WAAQ;;0DACP,6LAAC,6HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,6HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,6HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,6HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,6HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,6HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAU;0DAAa;;;;;;;;;;;;;;;;;8CAGtC,6LAAC,6HAAA,CAAA,YAAS;8CACP,gBAAgB,MAAM,KAAK,kBAC1B,6LAAC,6HAAA,CAAA,WAAQ;kDACP,cAAA,6LAAC,6HAAA,CAAA,YAAS;4CAAC,SAAS;4CAAG,WAAU;sDAAyC;;;;;;;;;;+CAK5E,gBAAgB,GAAG,CAAC,CAAC,uBACnB,6LAAC,6HAAA,CAAA,WAAQ;;8DACP,6LAAC,6HAAA,CAAA,YAAS;8DACR,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAe,OAAO,MAAM,CAAC,IAAI;;;;;;0EAChD,6LAAC;gEAAI,WAAU;0EACZ,OAAO,MAAM,CAAC,WAAW;;;;;;0EAE5B,6LAAC;gEAAI,WAAU;;oEACZ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,MAAM,CAAC,SAAS,CAAC,SAAS,GAAG;oEAAU;oEAAI,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG;;;;;;;;;;;;;;;;;;8DAI3H,6LAAC,6HAAA,CAAA,YAAS;8DACP,aAAa,OAAO,MAAM,CAAC,IAAI;;;;;;8DAElC,6LAAC,6HAAA,CAAA,YAAS;8DACP,eAAe,OAAO,MAAM;;;;;;8DAE/B,6LAAC,6HAAA,CAAA,YAAS;8DACR,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,WAAW,GAAG;;;;;;;;;;;;8DAG1C,6LAAC,6HAAA,CAAA,YAAS;8DACR,cAAA,6LAAC;wDAAI,WAAU;kEACZ,OAAO,QAAQ,GAAG,eAAe,OAAO,QAAQ,IAAI;;;;;;;;;;;8DAGzD,6LAAC,6HAAA,CAAA,YAAS;8DACR,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DACnB,OAAO,aAAa;;;;;;;;;;;;8DAGzB,6LAAC,6HAAA,CAAA,YAAS;oDAAC,WAAU;8DACnB,cAAA,6LAAC;wDAAI,WAAU;;4DACZ,OAAO,MAAM,KAAK,yBACjB;;kFACE,6LAAC,8HAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,UAAU,OAAO,EAAE;kFAElC,cAAA,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;kFAEjB,6LAAC,wIAAA,CAAA,eAAY;;0FACX,6LAAC,wIAAA,CAAA,sBAAmB;gFAAC,OAAO;0FAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oFAAC,SAAQ;oFAAQ,MAAK;8FAC3B,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAGxB,6LAAC,wIAAA,CAAA,sBAAmB;gFAAC,OAAM;;kGACzB,6LAAC,wIAAA,CAAA,mBAAgB;wFAAC,SAAS,IAAM,WAAW,OAAO,EAAE,EAAE;kGAAQ;;;;;;kGAG/D,6LAAC,wIAAA,CAAA,mBAAgB;wFAAC,SAAS,IAAM,WAAW,OAAO,EAAE,EAAE;kGAAU;;;;;;kGAGjE,6LAAC,wIAAA,CAAA,mBAAgB;wFAAC,SAAS,IAAM,WAAW,OAAO,EAAE,EAAE;kGAAQ;;;;;;;;;;;;;;;;;;;;0EAQvE,6LAAC,wIAAA,CAAA,eAAY;;kFACX,6LAAC,wIAAA,CAAA,sBAAmB;wEAAC,OAAO;kFAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;4EAAC,SAAQ;4EAAQ,MAAK;sFAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;gFAAC,WAAU;;;;;;;;;;;;;;;;kFAG9B,6LAAC,wIAAA,CAAA,sBAAmB;wEAAC,OAAM;;0FACzB,6LAAC,wIAAA,CAAA,mBAAgB;gFAAC,SAAS,IAAM,QAAQ,OAAO,EAAE;;kGAChD,6LAAC,uMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGpC,6LAAC,wIAAA,CAAA,mBAAgB;gFAAC,SAAS,IAAM,aAAa,OAAO,EAAE;;kGACrD,6LAAC,mNAAA,CAAA,YAAS;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGxC,6LAAC,wIAAA,CAAA,mBAAgB;gFAAC,SAAS,IAAM,UAAU,OAAO,EAAE;;kGAClD,6LAAC,2MAAA,CAAA,UAAO;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGtC,6LAAC,wIAAA,CAAA,mBAAgB;gFACf,SAAS,IAAM,SAAS,OAAO,EAAE;gFACjC,WAAU;;kGAEV,6LAAC,6MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CA1FhC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0G1C;GA/SgB;KAAA", "debugId": null}}, {"offset": {"line": 2051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/sf2-report-generator.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { SF2Report, ReportConfig, TeacherInfo } from \"@/lib/types/reports\"\nimport { schoolInfo, mockTeachers } from \"@/lib/data/reports-mock-data\"\nimport { generateSF2Report } from \"@/lib/utils/report-utils\"\nimport { mockStudents, mockAttendanceRecords } from \"@/lib/data/mock-data\"\nimport { \n  FileText, \n  Calendar, \n  User, \n  School, \n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Download,\n  Eye,\n  Printer\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\ninterface SF2ReportGeneratorProps {\n  config: ReportConfig\n  onGenerate: (report: SF2Report) => void\n  onPreview: (report: SF2Report) => void\n  className?: string\n}\n\nexport function SF2ReportGenerator({ \n  config, \n  onGenerate, \n  onPreview, \n  className \n}: SF2ReportGeneratorProps) {\n  const [selectedTeacher, setSelectedTeacher] = useState<TeacherInfo>(mockTeachers[0])\n  const [selectedSubject, setSelectedSubject] = useState(\"\")\n  const [classSchedule, setClassSchedule] = useState({\n    startTime: \"08:00\",\n    endTime: \"09:00\",\n    room: \"Room 101\"\n  })\n  const [reportSettings, setReportSettings] = useState({\n    includeSignatures: true,\n    includeRemarks: true,\n    includeAbsenteeReasons: true,\n    showStatistics: true\n  })\n  const [isGenerating, setIsGenerating] = useState(false)\n  const [generatedReport, setGeneratedReport] = useState<SF2Report | null>(null)\n\n  // Mock subjects for the selected grade/section\n  const subjects = [\n    \"Mathematics\",\n    \"English\",\n    \"Science\", \n    \"Filipino\",\n    \"Social Studies\",\n    \"Physical Education\",\n    \"Technology and Livelihood Education\",\n    \"Arts\",\n    \"Music\"\n  ]\n\n  const handleGenerateReport = async () => {\n    setIsGenerating(true)\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      // Generate the SF2 report\n      const report = generateSF2Report(\n        config,\n        mockStudents,\n        mockAttendanceRecords as any[]\n      )\n      \n      // Add additional SF2-specific data\n      const enhancedReport: SF2Report = {\n        ...report,\n        subject: selectedSubject,\n        teacher: {\n          ...selectedTeacher,\n          dateChecked: format(new Date(), \"yyyy-MM-dd\")\n        }\n      }\n      \n      setGeneratedReport(enhancedReport)\n      onGenerate(enhancedReport)\n    } catch (error) {\n      console.error(\"Error generating SF2 report:\", error)\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  const handlePreviewReport = () => {\n    if (generatedReport) {\n      onPreview(generatedReport)\n    }\n  }\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* SF2 Report Header */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <FileText className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div>\n                <CardTitle>SF2 Daily Attendance Report</CardTitle>\n                <CardDescription>\n                  Official DepEd School Form 2 - Daily Attendance Report of Learners\n                </CardDescription>\n              </div>\n            </div>\n            <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800\">\n              DepEd Official\n            </Badge>\n          </div>\n        </CardHeader>\n      </Card>\n\n      {/* School Information */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <School className=\"h-5 w-5\" />\n            School Information\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <Label className=\"text-sm font-medium\">School Name</Label>\n              <p className=\"text-sm text-muted-foreground\">{schoolInfo.schoolName}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium\">School ID</Label>\n              <p className=\"text-sm text-muted-foreground\">{schoolInfo.schoolId}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium\">Division</Label>\n              <p className=\"text-sm text-muted-foreground\">{schoolInfo.division}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium\">Region</Label>\n              <p className=\"text-sm text-muted-foreground\">{schoolInfo.region}</p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Class Information */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <User className=\"h-5 w-5\" />\n            Class Information\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"teacher-select\">Teacher</Label>\n              <Select \n                value={selectedTeacher.id} \n                onValueChange={(value) => {\n                  const teacher = mockTeachers.find(t => t.id === value)\n                  if (teacher) setSelectedTeacher(teacher)\n                }}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select teacher\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {mockTeachers.map((teacher) => (\n                    <SelectItem key={teacher.id} value={teacher.id}>\n                      {teacher.name} - {teacher.position}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"subject-select\">Subject</Label>\n              <Select value={selectedSubject} onValueChange={setSelectedSubject}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select subject\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {subjects.map((subject) => (\n                    <SelectItem key={subject} value={subject}>\n                      {subject}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>Grade & Section</Label>\n              <p className=\"text-sm text-muted-foreground\">\n                {config.filters.grades?.join(\", \") || \"All Grades\"} - {config.filters.sections?.join(\", \") || \"All Sections\"}\n              </p>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>Date</Label>\n              <p className=\"text-sm text-muted-foreground\">\n                {format(new Date(config.dateRange.startDate), \"MMMM dd, yyyy\")}\n              </p>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Class Schedule */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-sm font-medium\">Class Schedule</Label>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"start-time\" className=\"text-xs\">Start Time</Label>\n                <Input\n                  id=\"start-time\"\n                  type=\"time\"\n                  value={classSchedule.startTime}\n                  onChange={(e) => setClassSchedule({...classSchedule, startTime: e.target.value})}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"end-time\" className=\"text-xs\">End Time</Label>\n                <Input\n                  id=\"end-time\"\n                  type=\"time\"\n                  value={classSchedule.endTime}\n                  onChange={(e) => setClassSchedule({...classSchedule, endTime: e.target.value})}\n                />\n              </div>\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"room\" className=\"text-xs\">Room</Label>\n                <Input\n                  id=\"room\"\n                  value={classSchedule.room}\n                  onChange={(e) => setClassSchedule({...classSchedule, room: e.target.value})}\n                  placeholder=\"Room number\"\n                />\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Report Settings */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <CheckCircle className=\"h-5 w-5\" />\n            Report Settings\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-signatures\"\n                checked={reportSettings.includeSignatures}\n                onCheckedChange={(checked) => \n                  setReportSettings({...reportSettings, includeSignatures: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"include-signatures\" className=\"text-sm\">\n                Include teacher signature fields\n              </Label>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-remarks\"\n                checked={reportSettings.includeRemarks}\n                onCheckedChange={(checked) => \n                  setReportSettings({...reportSettings, includeRemarks: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"include-remarks\" className=\"text-sm\">\n                Include remarks column\n              </Label>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-reasons\"\n                checked={reportSettings.includeAbsenteeReasons}\n                onCheckedChange={(checked) => \n                  setReportSettings({...reportSettings, includeAbsenteeReasons: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"include-reasons\" className=\"text-sm\">\n                Include absence reason coding\n              </Label>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"show-statistics\"\n                checked={reportSettings.showStatistics}\n                onCheckedChange={(checked) => \n                  setReportSettings({...reportSettings, showStatistics: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"show-statistics\" className=\"text-sm\">\n                Show attendance statistics summary\n              </Label>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Generation Actions */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-1\">\n              <h3 className=\"text-lg font-medium\">Generate SF2 Report</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                Create official DepEd SF2 Daily Attendance Report\n              </p>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              {generatedReport && (\n                <>\n                  <Button variant=\"outline\" onClick={handlePreviewReport}>\n                    <Eye className=\"mr-2 h-4 w-4\" />\n                    Preview\n                  </Button>\n                  <Button variant=\"outline\">\n                    <Download className=\"mr-2 h-4 w-4\" />\n                    Download\n                  </Button>\n                  <Button variant=\"outline\">\n                    <Printer className=\"mr-2 h-4 w-4\" />\n                    Print\n                  </Button>\n                </>\n              )}\n              <Button \n                onClick={handleGenerateReport} \n                disabled={isGenerating || !selectedSubject}\n                size=\"lg\"\n              >\n                {isGenerating ? (\n                  <>\n                    <Clock className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Generating...\n                  </>\n                ) : (\n                  <>\n                    <FileText className=\"mr-2 h-4 w-4\" />\n                    Generate SF2\n                  </>\n                )}\n              </Button>\n            </div>\n          </div>\n\n          {!selectedSubject && (\n            <div className=\"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n              <div className=\"flex items-center gap-2 text-yellow-800\">\n                <AlertCircle className=\"h-4 w-4\" />\n                <span className=\"text-sm\">Please select a subject to generate the SF2 report</span>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;;;AA5BA;;;;;;;;;;;;;;;AAqCO,SAAS,mBAAmB,KAKT;QALS,EACjC,MAAM,EACN,UAAU,EACV,SAAS,EACT,SAAS,EACe,GALS;QAmLlB,wBAAsD;;IA7KrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,yIAAA,CAAA,eAAY,CAAC,EAAE;IACnF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,mBAAmB;QACnB,gBAAgB;QAChB,wBAAwB;QACxB,gBAAgB;IAClB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAEzE,+CAA+C;IAC/C,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,uBAAuB;QAC3B,gBAAgB;QAEhB,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,0BAA0B;YAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD,EAC7B,QACA,8HAAA,CAAA,eAAY,EACZ,8HAAA,CAAA,wBAAqB;YAGvB,mCAAmC;YACnC,MAAM,iBAA4B;gBAChC,GAAG,MAAM;gBACT,SAAS;gBACT,SAAS;oBACP,GAAG,eAAe;oBAClB,aAAa,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;gBAClC;YACF;YAEA,mBAAmB;YACnB,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,iBAAiB;YACnB,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAE3B,6LAAC,4HAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,4HAAA,CAAA,aAAU;8BACT,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,6LAAC;;0DACC,6LAAC,4HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,4HAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;;0CAKrB,6LAAC,6HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;0BAQvE,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIlC,6LAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,6HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,6LAAC;4CAAE,WAAU;sDAAiC,yIAAA,CAAA,aAAU,CAAC,UAAU;;;;;;;;;;;;8CAErE,6LAAC;;sDACC,6LAAC,6HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,6LAAC;4CAAE,WAAU;sDAAiC,yIAAA,CAAA,aAAU,CAAC,QAAQ;;;;;;;;;;;;8CAEnE,6LAAC;;sDACC,6LAAC,6HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,6LAAC;4CAAE,WAAU;sDAAiC,yIAAA,CAAA,aAAU,CAAC,QAAQ;;;;;;;;;;;;8CAEnE,6LAAC;;sDACC,6LAAC,6HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,6LAAC;4CAAE,WAAU;sDAAiC,yIAAA,CAAA,aAAU,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvE,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIhC,6LAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,6LAAC,8HAAA,CAAA,SAAM;gDACL,OAAO,gBAAgB,EAAE;gDACzB,eAAe,CAAC;oDACd,MAAM,UAAU,yIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oDAChD,IAAI,SAAS,mBAAmB;gDAClC;;kEAEA,6LAAC,8HAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,8HAAA,CAAA,gBAAa;kEACX,yIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,wBACjB,6LAAC,8HAAA,CAAA,aAAU;gEAAkB,OAAO,QAAQ,EAAE;;oEAC3C,QAAQ,IAAI;oEAAC;oEAAI,QAAQ,QAAQ;;+DADnB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;kDAQnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,6LAAC,8HAAA,CAAA,SAAM;gDAAC,OAAO;gDAAiB,eAAe;;kEAC7C,6LAAC,8HAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,8HAAA,CAAA,gBAAa;kEACX,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,8HAAA,CAAA,aAAU;gEAAe,OAAO;0EAC9B;+DADc;;;;;;;;;;;;;;;;;;;;;;kDAQzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDAAE,WAAU;;oDACV,EAAA,yBAAA,OAAO,OAAO,CAAC,MAAM,cAArB,6CAAA,uBAAuB,IAAI,CAAC,UAAS;oDAAa;oDAAI,EAAA,2BAAA,OAAO,OAAO,CAAC,QAAQ,cAAvB,+CAAA,yBAAyB,IAAI,CAAC,UAAS;;;;;;;;;;;;;kDAIlG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,SAAS,GAAG;;;;;;;;;;;;;;;;;;0CAKpD,6LAAC,iIAAA,CAAA,YAAS;;;;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;wCAAC,WAAU;kDAAsB;;;;;;kDACvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAa,WAAU;kEAAU;;;;;;kEAChD,6LAAC,6HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,cAAc,SAAS;wDAC9B,UAAU,CAAC,IAAM,iBAAiB;gEAAC,GAAG,aAAa;gEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4DAAA;;;;;;;;;;;;0DAGlF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;kEAAU;;;;;;kEAC9C,6LAAC,6HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,cAAc,OAAO;wDAC5B,UAAU,CAAC,IAAM,iBAAiB;gEAAC,GAAG,aAAa;gEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4DAAA;;;;;;;;;;;;0DAGhF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAO,WAAU;kEAAU;;;;;;kEAC1C,6LAAC,6HAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,cAAc,IAAI;wDACzB,UAAU,CAAC,IAAM,iBAAiB;gEAAC,GAAG,aAAa;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACzE,aAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxB,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIvC,6LAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,iBAAiB;4CACzC,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,mBAAmB;gDAAkB;;;;;;sDAG/E,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAqB,WAAU;sDAAU;;;;;;;;;;;;8CAK1D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,cAAc;4CACtC,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,gBAAgB;gDAAkB;;;;;;sDAG5E,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAkB,WAAU;sDAAU;;;;;;;;;;;;8CAKvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,sBAAsB;4CAC9C,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,wBAAwB;gDAAkB;;;;;;sDAGpF,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAkB,WAAU;sDAAU;;;;;;;;;;;;8CAKvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,cAAc;4CACtC,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,gBAAgB;gDAAkB;;;;;;sDAG5E,6LAAC,6HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAkB,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7D,6LAAC,4HAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAI/C,6LAAC;oCAAI,WAAU;;wCACZ,iCACC;;8DACE,6LAAC,8HAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,SAAS;;sEACjC,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGlC,6LAAC,8HAAA,CAAA,SAAM;oDAAC,SAAQ;;sEACd,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGvC,6LAAC,8HAAA,CAAA,SAAM;oDAAC,SAAQ;;sEACd,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;sDAK1C,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,gBAAgB,CAAC;4CAC3B,MAAK;sDAEJ,6BACC;;kEACE,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAA8B;;6EAIjD;;kEACE,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;wBAQ9C,CAAC,iCACA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C;GAjWgB;KAAA", "debugId": null}}, {"offset": {"line": 3079, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/sf2-report-preview.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { SF2Report, ExportFormat } from \"@/lib/types/reports\"\nimport { ReportExportManager } from \"@/lib/utils/export-utils\"\nimport { \n  FileText, \n  Download, \n  Printer, \n  School,\n  Calendar,\n  User,\n  Clock,\n  CheckCircle,\n  XCircle,\n  AlertTriangle\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\ninterface SF2ReportPreviewProps {\n  report: SF2Report\n  onDownload?: (format: ExportFormat) => void\n  onPrint?: () => void\n  className?: string\n}\n\nexport function SF2ReportPreview({\n  report,\n  onDownload,\n  onPrint,\n  className\n}: SF2ReportPreviewProps) {\n  const handleDownload = async (format: ExportFormat) => {\n    try {\n      const { blob, filename } = await ReportExportManager.exportReport(report, format)\n      ReportExportManager.downloadFile(blob, filename)\n      onDownload?.(format)\n    } catch (error) {\n      console.error('Export failed:', error)\n    }\n  }\n  const getAttendanceIcon = (status: string) => {\n    switch (status) {\n      case 'P':\n        return <CheckCircle className=\"h-4 w-4 text-green-600\" />\n      case 'L':\n        return <AlertTriangle className=\"h-4 w-4 text-yellow-600\" />\n      case 'A':\n        return <XCircle className=\"h-4 w-4 text-red-600\" />\n      case 'E':\n        return <CheckCircle className=\"h-4 w-4 text-blue-600\" />\n      default:\n        return <XCircle className=\"h-4 w-4 text-gray-400\" />\n    }\n  }\n\n  const getAttendanceLabel = (status: string) => {\n    switch (status) {\n      case 'P': return 'Present'\n      case 'L': return 'Late'\n      case 'A': return 'Absent'\n      case 'E': return 'Excused'\n      default: return 'Unknown'\n    }\n  }\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Header Actions */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"p-2 bg-blue-100 rounded-lg\">\n            <FileText className=\"h-6 w-6 text-blue-600\" />\n          </div>\n          <div>\n            <h2 className=\"text-xl font-semibold\">SF2 Report Preview</h2>\n            <p className=\"text-sm text-muted-foreground\">\n              Daily Attendance Report - {format(new Date(report.date), \"MMMM dd, yyyy\")}\n            </p>\n          </div>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button variant=\"outline\" onClick={() => handleDownload('EXCEL')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            Excel\n          </Button>\n          <Button variant=\"outline\" onClick={() => handleDownload('PDF')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            PDF\n          </Button>\n          <Button variant=\"outline\" onClick={() => handleDownload('CSV')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            CSV\n          </Button>\n          <Button onClick={onPrint}>\n            <Printer className=\"mr-2 h-4 w-4\" />\n            Print\n          </Button>\n        </div>\n      </div>\n\n      {/* SF2 Form Preview */}\n      <Card className=\"print:shadow-none print:border-none\">\n        <CardContent className=\"p-8 space-y-6\">\n          {/* Official Header */}\n          <div className=\"text-center space-y-2\">\n            <h1 className=\"text-lg font-bold\">Republic of the Philippines</h1>\n            <h2 className=\"text-base font-semibold\">Department of Education</h2>\n            <h3 className=\"text-base font-semibold\">{report.schoolInfo.region}</h3>\n            <h4 className=\"text-base font-semibold\">{report.schoolInfo.division}</h4>\n            <h5 className=\"text-base font-semibold\">{report.schoolInfo.schoolName}</h5>\n            <div className=\"pt-4\">\n              <h2 className=\"text-xl font-bold\">SCHOOL FORM 2 (SF2)</h2>\n              <h3 className=\"text-lg font-semibold\">DAILY ATTENDANCE REPORT OF LEARNERS</h3>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* School and Class Information */}\n          <div className=\"grid grid-cols-2 gap-8\">\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">School:</span>\n                <span className=\"underline\">{report.schoolInfo.schoolName}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">School ID:</span>\n                <span className=\"underline\">{report.schoolInfo.schoolId}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Division:</span>\n                <span className=\"underline\">{report.schoolInfo.division}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Region:</span>\n                <span className=\"underline\">{report.schoolInfo.region}</span>\n              </div>\n            </div>\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Grade & Section:</span>\n                <span className=\"underline\">Grade {report.grade} - {report.section}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Subject:</span>\n                <span className=\"underline\">{report.subject || 'All Subjects'}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Teacher:</span>\n                <span className=\"underline\">{report.teacher.name}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Date:</span>\n                <span className=\"underline\">{format(new Date(report.date), \"MMMM dd, yyyy\")}</span>\n              </div>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Attendance Table */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-center\">DAILY ATTENDANCE</h3>\n            \n            <div className=\"border rounded-lg overflow-hidden\">\n              <Table>\n                <TableHeader>\n                  <TableRow className=\"bg-gray-50\">\n                    <TableHead className=\"w-12 text-center border-r\">No.</TableHead>\n                    <TableHead className=\"border-r\">LEARNER'S NAME</TableHead>\n                    <TableHead className=\"w-24 text-center border-r\">ATTENDANCE</TableHead>\n                    <TableHead className=\"w-32 text-center border-r\">STATUS</TableHead>\n                    <TableHead className=\"text-center\">REMARKS</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {report.students.map((student, index) => (\n                    <TableRow key={student.studentId} className=\"border-b\">\n                      <TableCell className=\"text-center border-r font-medium\">\n                        {index + 1}\n                      </TableCell>\n                      <TableCell className=\"border-r\">\n                        {student.studentName}\n                      </TableCell>\n                      <TableCell className=\"text-center border-r\">\n                        <div className=\"flex items-center justify-center\">\n                          {getAttendanceIcon(student.dailyStatus)}\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"text-center border-r\">\n                        <Badge \n                          variant={\n                            student.dailyStatus === 'P' ? 'default' :\n                            student.dailyStatus === 'L' ? 'secondary' :\n                            student.dailyStatus === 'E' ? 'outline' : 'destructive'\n                          }\n                          className=\"text-xs\"\n                        >\n                          {getAttendanceLabel(student.dailyStatus)}\n                        </Badge>\n                      </TableCell>\n                      <TableCell className=\"text-center text-sm\">\n                        {student.remarks || '-'}\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Summary Statistics */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-center\">ATTENDANCE SUMMARY</h3>\n            \n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-blue-600\">{report.summary.totalStudents}</div>\n                <div className=\"text-sm text-muted-foreground\">Total Students</div>\n              </div>\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-green-600\">{report.summary.presentCount}</div>\n                <div className=\"text-sm text-muted-foreground\">Present</div>\n              </div>\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-yellow-600\">{report.summary.lateCount}</div>\n                <div className=\"text-sm text-muted-foreground\">Late</div>\n              </div>\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-red-600\">{report.summary.absentCount}</div>\n                <div className=\"text-sm text-muted-foreground\">Absent</div>\n              </div>\n            </div>\n\n            <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n              <div className=\"text-xl font-bold text-blue-800\">\n                {report.summary.attendanceRate.toFixed(1)}%\n              </div>\n              <div className=\"text-sm text-blue-600\">Overall Attendance Rate</div>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Signature Section */}\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-2 gap-8\">\n              <div className=\"space-y-4\">\n                <div className=\"text-center\">\n                  <div className=\"border-b border-black w-48 mx-auto mb-2 h-12\"></div>\n                  <div className=\"text-sm font-medium\">Teacher's Signature</div>\n                  <div className=\"text-xs text-muted-foreground\">{report.teacher.name}</div>\n                </div>\n              </div>\n              <div className=\"space-y-4\">\n                <div className=\"text-center\">\n                  <div className=\"border-b border-black w-48 mx-auto mb-2 h-12\"></div>\n                  <div className=\"text-sm font-medium\">Date</div>\n                  <div className=\"text-xs text-muted-foreground\">\n                    {report.teacher.dateChecked ? format(new Date(report.teacher.dateChecked), \"MMMM dd, yyyy\") : ''}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"border-b border-black w-64 mx-auto mb-2 h-12\"></div>\n              <div className=\"text-sm font-medium\">Principal's Signature</div>\n              <div className=\"text-xs text-muted-foreground\">{report.schoolInfo.principalName}</div>\n            </div>\n          </div>\n\n          {/* Footer */}\n          <div className=\"text-center text-xs text-muted-foreground pt-4 border-t\">\n            <p>SF2 - Daily Attendance Report of Learners</p>\n            <p>Generated on {format(new Date(report.generatedAt), \"MMMM dd, yyyy 'at' HH:mm\")}</p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AArBA;;;;;;;;;;AA8BO,SAAS,iBAAiB,KAKT;QALS,EAC/B,MAAM,EACN,UAAU,EACV,OAAO,EACP,SAAS,EACa,GALS;IAM/B,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,MAAM,kIAAA,CAAA,sBAAmB,CAAC,YAAY,CAAC,QAAQ;YAC1E,kIAAA,CAAA,sBAAmB,CAAC,YAAY,CAAC,MAAM;YACvC,uBAAA,iCAAA,WAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IACA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QAC9B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAE3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC;wCAAE,WAAU;;4CAAgC;4CAChB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,IAAI,GAAG;;;;;;;;;;;;;;;;;;;kCAI/D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,8HAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO1C,6LAAC,4HAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoB;;;;;;8CAClC,6LAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,6LAAC;oCAAG,WAAU;8CAA2B,OAAO,UAAU,CAAC,MAAM;;;;;;8CACjE,6LAAC;oCAAG,WAAU;8CAA2B,OAAO,UAAU,CAAC,QAAQ;;;;;;8CACnE,6LAAC;oCAAG,WAAU;8CAA2B,OAAO,UAAU,CAAC,UAAU;;;;;;8CACrE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoB;;;;;;sDAClC,6LAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAI1C,6LAAC,iIAAA,CAAA,YAAS;;;;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,UAAU;;;;;;;;;;;;sDAE3D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,QAAQ;;;;;;;;;;;;sDAEzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,QAAQ;;;;;;;;;;;;sDAEzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,MAAM;;;;;;;;;;;;;;;;;;8CAGzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;;wDAAY;wDAAO,OAAO,KAAK;wDAAC;wDAAI,OAAO,OAAO;;;;;;;;;;;;;sDAEpE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DAAa,OAAO,OAAO,IAAI;;;;;;;;;;;;sDAEjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DAAa,OAAO,OAAO,CAAC,IAAI;;;;;;;;;;;;sDAElD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,6LAAC;oDAAK,WAAU;8DAAa,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,IAAI,GAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAKjE,6LAAC,iIAAA,CAAA,YAAS;;;;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;;0DACJ,6LAAC,6HAAA,CAAA,cAAW;0DACV,cAAA,6LAAC,6HAAA,CAAA,WAAQ;oDAAC,WAAU;;sEAClB,6LAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAA4B;;;;;;sEACjD,6LAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAW;;;;;;sEAChC,6LAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAA4B;;;;;;sEACjD,6LAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAA4B;;;;;;sEACjD,6LAAC,6HAAA,CAAA,YAAS;4DAAC,WAAU;sEAAc;;;;;;;;;;;;;;;;;0DAGvC,6LAAC,6HAAA,CAAA,YAAS;0DACP,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7B,6LAAC,6HAAA,CAAA,WAAQ;wDAAyB,WAAU;;0EAC1C,6LAAC,6HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,QAAQ;;;;;;0EAEX,6LAAC,6HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,QAAQ,WAAW;;;;;;0EAEtB,6LAAC,6HAAA,CAAA,YAAS;gEAAC,WAAU;0EACnB,cAAA,6LAAC;oEAAI,WAAU;8EACZ,kBAAkB,QAAQ,WAAW;;;;;;;;;;;0EAG1C,6LAAC,6HAAA,CAAA,YAAS;gEAAC,WAAU;0EACnB,cAAA,6LAAC,6HAAA,CAAA,QAAK;oEACJ,SACE,QAAQ,WAAW,KAAK,MAAM,YAC9B,QAAQ,WAAW,KAAK,MAAM,cAC9B,QAAQ,WAAW,KAAK,MAAM,YAAY;oEAE5C,WAAU;8EAET,mBAAmB,QAAQ,WAAW;;;;;;;;;;;0EAG3C,6LAAC,6HAAA,CAAA,YAAS;gEAAC,WAAU;0EAClB,QAAQ,OAAO,IAAI;;;;;;;uDAzBT,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAkC1C,6LAAC,iIAAA,CAAA,YAAS;;;;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAoC,OAAO,OAAO,CAAC,aAAa;;;;;;8DAC/E,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAqC,OAAO,OAAO,CAAC,YAAY;;;;;;8DAC/E,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAsC,OAAO,OAAO,CAAC,SAAS;;;;;;8DAC7E,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmC,OAAO,OAAO,CAAC,WAAW;;;;;;8DAC5E,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAInD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDACZ,OAAO,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC;gDAAG;;;;;;;sDAE5C,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAI3C,6LAAC,iIAAA,CAAA,YAAS;;;;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;kEAAsB;;;;;;kEACrC,6LAAC;wDAAI,WAAU;kEAAiC,OAAO,OAAO,CAAC,IAAI;;;;;;;;;;;;;;;;;sDAGvE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;kEAAsB;;;;;;kEACrC,6LAAC;wDAAI,WAAU;kEACZ,OAAO,OAAO,CAAC,WAAW,GAAG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,OAAO,CAAC,WAAW,GAAG,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;8CAMtG,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;sDAAsB;;;;;;sDACrC,6LAAC;4CAAI,WAAU;sDAAiC,OAAO,UAAU,CAAC,aAAa;;;;;;;;;;;;;;;;;;sCAKnF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAE;;;;;;8CACH,6LAAC;;wCAAE;wCAAc,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlE;KAlQgB", "debugId": null}}, {"offset": {"line": 4143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/sf2-report-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON>alogHeader, DialogTitle } from \"@/components/ui/dialog\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from \"@/components/ui/tabs\"\nimport { SF2ReportGenerator } from \"./sf2-report-generator\"\nimport { SF2ReportPreview } from \"./sf2-report-preview\"\nimport { ReportConfig, SF2Report, ExportFormat } from \"@/lib/types/reports\"\nimport { ArrowLeft, FileText, Eye } from \"lucide-react\"\nimport { toast } from \"sonner\"\n\ninterface SF2ReportDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  config: ReportConfig\n}\n\nexport function SF2ReportDialog({ open, onOpenChange, config }: SF2ReportDialogProps) {\n  const [activeTab, setActiveTab] = useState(\"generate\")\n  const [generatedReport, setGeneratedReport] = useState<SF2Report | null>(null)\n\n  const handleGenerateReport = (report: SF2Report) => {\n    setGeneratedReport(report)\n    setActiveTab(\"preview\")\n    toast.success(\"SF2 report generated successfully\", {\n      description: \"You can now preview, download, or print the report\"\n    })\n  }\n\n  const handlePreviewReport = (report: SF2Report) => {\n    setGeneratedReport(report)\n    setActiveTab(\"preview\")\n  }\n\n  const handleDownload = (format: ExportFormat) => {\n    toast.success(`Downloading SF2 report in ${format} format`, {\n      description: \"The download will start shortly\"\n    })\n  }\n\n  const handlePrint = () => {\n    window.print()\n    toast.success(\"Print dialog opened\", {\n      description: \"Please select your printer and print settings\"\n    })\n  }\n\n  const handleBackToGenerate = () => {\n    setActiveTab(\"generate\")\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-6xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            SF2 Daily Attendance Report Generator\n          </DialogTitle>\n          <DialogDescription>\n            Generate official DepEd School Form 2 - Daily Attendance Report of Learners\n          </DialogDescription>\n        </DialogHeader>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-2\">\n            <TabsTrigger value=\"generate\" className=\"flex items-center gap-2\">\n              <FileText className=\"h-4 w-4\" />\n              Generate Report\n            </TabsTrigger>\n            <TabsTrigger \n              value=\"preview\" \n              disabled={!generatedReport}\n              className=\"flex items-center gap-2\"\n            >\n              <Eye className=\"h-4 w-4\" />\n              Preview Report\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"generate\" className=\"mt-6\">\n            <SF2ReportGenerator\n              config={config}\n              onGenerate={handleGenerateReport}\n              onPreview={handlePreviewReport}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"preview\" className=\"mt-6\">\n            {generatedReport ? (\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <Button \n                    variant=\"outline\" \n                    onClick={handleBackToGenerate}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4\" />\n                    Back to Generator\n                  </Button>\n                </div>\n                \n                <SF2ReportPreview\n                  report={generatedReport}\n                  onDownload={handleDownload}\n                  onPrint={handlePrint}\n                />\n              </div>\n            ) : (\n              <div className=\"text-center py-8 text-muted-foreground\">\n                <FileText className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                <p>No report generated yet</p>\n                <p className=\"text-sm\">Generate a report first to see the preview</p>\n              </div>\n            )}\n          </TabsContent>\n        </Tabs>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;;;AAVA;;;;;;;;;AAkBO,SAAS,gBAAgB,KAAoD;QAApD,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAwB,GAApD;;IAC9B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAEzE,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;QACnB,aAAa;QACb,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qCAAqC;YACjD,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,iBAAiB,CAAC;QACtB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,6BAAmC,OAAP,QAAO,YAAU;YAC1D,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,KAAK;QACZ,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;YACnC,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB;QAC3B,aAAa;IACf;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGlC,6LAAC,8HAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC,4HAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,6LAAC,4HAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;;sDACtC,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGlC,6LAAC,4HAAA,CAAA,cAAW;oCACV,OAAM;oCACN,UAAU,CAAC;oCACX,WAAU;;sDAEV,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAK/B,6LAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,6LAAC,uJAAA,CAAA,qBAAkB;gCACjB,QAAQ;gCACR,YAAY;gCACZ,WAAW;;;;;;;;;;;sCAIf,6LAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACpC,gCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;;8DAEV,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAKrC,6LAAC,qJAAA,CAAA,mBAAgB;wCACf,QAAQ;wCACR,YAAY;wCACZ,SAAS;;;;;;;;;;;qDAIb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAE;;;;;;kDACH,6LAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC;GAvGgB;KAAA", "debugId": null}}, {"offset": {"line": 4417, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/sf4-report-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON>alogHeader, DialogTitle } from \"@/components/ui/dialog\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from \"@/components/ui/tabs\"\nimport { SF4ReportGenerator } from \"./sf4-report-generator\"\nimport { SF4ReportPreview } from \"./sf4-report-preview\"\nimport { ReportConfig, SF4Report, ExportFormat } from \"@/lib/types/reports\"\nimport { ArrowLeft, TrendingUp, Eye } from \"lucide-react\"\nimport { toast } from \"sonner\"\n\ninterface SF4ReportDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  config: ReportConfig\n}\n\nexport function SF4ReportDialog({ open, onOpenChange, config }: SF4ReportDialogProps) {\n  const [activeTab, setActiveTab] = useState(\"generate\")\n  const [generatedReport, setGeneratedReport] = useState<SF4Report | null>(null)\n\n  const handleGenerateReport = (report: SF4Report) => {\n    setGeneratedReport(report)\n    setActiveTab(\"preview\")\n    toast.success(\"SF4 report generated successfully\", {\n      description: \"You can now preview, download, or print the report\"\n    })\n  }\n\n  const handlePreviewReport = (report: SF4Report) => {\n    setGeneratedReport(report)\n    setActiveTab(\"preview\")\n  }\n\n  const handleDownload = (format: ExportFormat) => {\n    toast.success(`Downloading SF4 report in ${format} format`, {\n      description: \"The download will start shortly\"\n    })\n  }\n\n  const handlePrint = () => {\n    window.print()\n    toast.success(\"Print dialog opened\", {\n      description: \"Please select your printer and print settings\"\n    })\n  }\n\n  const handleBackToGenerate = () => {\n    setActiveTab(\"generate\")\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-6xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <TrendingUp className=\"h-5 w-5\" />\n            SF4 Monthly Learner's Movement Generator\n          </DialogTitle>\n          <DialogDescription>\n            Generate official DepEd School Form 4 - Monthly Report on Learner's Movement\n          </DialogDescription>\n        </DialogHeader>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-2\">\n            <TabsTrigger value=\"generate\" className=\"flex items-center gap-2\">\n              <TrendingUp className=\"h-4 w-4\" />\n              Generate Report\n            </TabsTrigger>\n            <TabsTrigger \n              value=\"preview\" \n              disabled={!generatedReport}\n              className=\"flex items-center gap-2\"\n            >\n              <Eye className=\"h-4 w-4\" />\n              Preview Report\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"generate\" className=\"mt-6\">\n            <SF4ReportGenerator\n              config={config}\n              onGenerate={handleGenerateReport}\n              onPreview={handlePreviewReport}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"preview\" className=\"mt-6\">\n            {generatedReport ? (\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <Button \n                    variant=\"outline\" \n                    onClick={handleBackToGenerate}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4\" />\n                    Back to Generator\n                  </Button>\n                </div>\n                \n                <SF4ReportPreview\n                  report={generatedReport}\n                  onDownload={handleDownload}\n                  onPrint={handlePrint}\n                />\n              </div>\n            ) : (\n              <div className=\"text-center py-8 text-muted-foreground\">\n                <TrendingUp className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                <p>No report generated yet</p>\n                <p className=\"text-sm\">Generate a report first to see the preview</p>\n              </div>\n            )}\n          </TabsContent>\n        </Tabs>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;;;AAVA;;;;;;;;;AAkBO,SAAS,gBAAgB,KAAoD;QAApD,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAwB,GAApD;;IAC9B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAEzE,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;QACnB,aAAa;QACb,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,qCAAqC;YACjD,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,iBAAiB,CAAC;QACtB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,6BAAmC,OAAP,QAAO,YAAU;YAC1D,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,KAAK;QACZ,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;YACnC,aAAa;QACf;IACF;IAEA,MAAM,uBAAuB;QAC3B,aAAa;IACf;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGpC,6LAAC,8HAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC,4HAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,6LAAC,4HAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;;sDACtC,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAGpC,6LAAC,4HAAA,CAAA,cAAW;oCACV,OAAM;oCACN,UAAU,CAAC;oCACX,WAAU;;sDAEV,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAK/B,6LAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,6LAAC,uJAAA,CAAA,qBAAkB;gCACjB,QAAQ;gCACR,YAAY;gCACZ,WAAW;;;;;;;;;;;sCAIf,6LAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACpC,gCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;;8DAEV,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAKrC,6LAAC,qJAAA,CAAA,mBAAgB;wCACf,QAAQ;wCACR,YAAY;wCACZ,SAAS;;;;;;;;;;;qDAIb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;kDAAE;;;;;;kDACH,6LAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC;GAvGgB;KAAA", "debugId": null}}, {"offset": {"line": 4691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/custom-report-preview.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { CustomReport, ExportFormat } from \"@/lib/types/reports\"\nimport { ReportExportManager } from \"@/lib/utils/export-utils\"\nimport { \n  Users, \n  Download, \n  Printer, \n  BarChart3,\n  Table as TableIcon,\n  PieChart,\n  TrendingUp,\n  FileText\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\ninterface CustomReportPreviewProps {\n  report: CustomReport\n  onDownload?: (format: ExportFormat) => void\n  onPrint?: () => void\n  className?: string\n}\n\n// Mock data for preview\nconst mockData = [\n  {\n    \"students.id\": \"STU001\",\n    \"students.name\": \"<PERSON>\",\n    \"students.grade\": \"7\",\n    \"students.section\": \"A\",\n    \"attendance.status\": \"Present\",\n    \"attendance.date\": \"2025-01-02\"\n  },\n  {\n    \"students.id\": \"STU002\", \n    \"students.name\": \"<PERSON>\",\n    \"students.grade\": \"7\",\n    \"students.section\": \"B\",\n    \"attendance.status\": \"Late\",\n    \"attendance.date\": \"2025-01-02\"\n  },\n  {\n    \"students.id\": \"STU003\",\n    \"students.name\": \"Ana Marie Reyes\", \n    \"students.grade\": \"8\",\n    \"students.section\": \"A\",\n    \"attendance.status\": \"Present\",\n    \"attendance.date\": \"2025-01-02\"\n  },\n  {\n    \"students.id\": \"STU004\",\n    \"students.name\": \"Jose Miguel Rodriguez\",\n    \"students.grade\": \"8\", \n    \"students.section\": \"B\",\n    \"attendance.status\": \"Absent\",\n    \"attendance.date\": \"2025-01-02\"\n  },\n  {\n    \"students.id\": \"STU005\",\n    \"students.name\": \"Princess Mae Garcia\",\n    \"students.grade\": \"9\",\n    \"students.section\": \"A\", \n    \"attendance.status\": \"Present\",\n    \"attendance.date\": \"2025-01-02\"\n  }\n]\n\nexport function CustomReportPreview({\n  report,\n  onDownload,\n  onPrint,\n  className\n}: CustomReportPreviewProps) {\n  const handleDownload = async (format: ExportFormat) => {\n    try {\n      const { blob, filename } = await ReportExportManager.exportReport(report, format)\n      ReportExportManager.downloadFile(blob, filename)\n      onDownload?.(format)\n    } catch (error) {\n      console.error('Export failed:', error)\n    }\n  }\n  // Use mock data for preview, in real implementation this would come from report.data\n  const data = report.data.length > 0 ? report.data : mockData.slice(0, Math.min(5, report.query.limit || 5))\n\n  const getVisualizationIcon = () => {\n    if (report.visualization?.type === 'chart') {\n      switch (report.visualization.chartType) {\n        case 'bar': return <BarChart3 className=\"h-6 w-6 text-blue-600\" />\n        case 'pie': return <PieChart className=\"h-6 w-6 text-green-600\" />\n        case 'line': return <TrendingUp className=\"h-6 w-6 text-purple-600\" />\n        default: return <BarChart3 className=\"h-6 w-6 text-blue-600\" />\n      }\n    }\n    return <TableIcon className=\"h-6 w-6 text-gray-600\" />\n  }\n\n  const formatFieldName = (fieldId: string) => {\n    const parts = fieldId.split('.')\n    if (parts.length === 2) {\n      const [source, field] = parts\n      return field.charAt(0).toUpperCase() + field.slice(1).replace(/([A-Z])/g, ' $1')\n    }\n    return fieldId\n  }\n\n  const formatCellValue = (value: any, fieldId: string) => {\n    if (value === null || value === undefined) return '-'\n    \n    // Format dates\n    if (fieldId.includes('date') || fieldId.includes('Date')) {\n      try {\n        return format(new Date(value), \"MMM dd, yyyy\")\n      } catch {\n        return value\n      }\n    }\n    \n    // Format status with badges\n    if (fieldId.includes('status') || fieldId.includes('Status')) {\n      const variant = value === 'Present' ? 'default' : \n                    value === 'Late' ? 'secondary' : \n                    value === 'Absent' ? 'destructive' : 'outline'\n      return <Badge variant={variant as any}>{value}</Badge>\n    }\n    \n    return value\n  }\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Header Actions */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"p-2 bg-gray-100 rounded-lg\">\n            {getVisualizationIcon()}\n          </div>\n          <div>\n            <h2 className=\"text-xl font-semibold\">{report.name || \"Custom Report\"}</h2>\n            <p className=\"text-sm text-muted-foreground\">\n              {report.description || \"Custom report preview\"}\n            </p>\n          </div>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button variant=\"outline\" onClick={() => handleDownload('CSV')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            CSV\n          </Button>\n          <Button variant=\"outline\" onClick={() => handleDownload('EXCEL')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            Excel\n          </Button>\n          <Button variant=\"outline\" onClick={() => handleDownload('PDF')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            PDF\n          </Button>\n          <Button onClick={onPrint}>\n            <Printer className=\"mr-2 h-4 w-4\" />\n            Print\n          </Button>\n        </div>\n      </div>\n\n      {/* Report Information */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <FileText className=\"h-5 w-5\" />\n            Report Information\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            <div>\n              <div className=\"text-sm font-medium text-muted-foreground\">Data Sources</div>\n              <div className=\"text-lg font-semibold\">{report.query.tables.length}</div>\n              <div className=\"text-xs text-muted-foreground\">\n                {report.query.tables.join(', ')}\n              </div>\n            </div>\n            <div>\n              <div className=\"text-sm font-medium text-muted-foreground\">Fields</div>\n              <div className=\"text-lg font-semibold\">{report.query.fields.length}</div>\n              <div className=\"text-xs text-muted-foreground\">Selected columns</div>\n            </div>\n            <div>\n              <div className=\"text-sm font-medium text-muted-foreground\">Filters</div>\n              <div className=\"text-lg font-semibold\">{report.query.conditions.length}</div>\n              <div className=\"text-xs text-muted-foreground\">Applied conditions</div>\n            </div>\n            <div>\n              <div className=\"text-sm font-medium text-muted-foreground\">Records</div>\n              <div className=\"text-lg font-semibold\">{data.length}</div>\n              <div className=\"text-xs text-muted-foreground\">\n                {data.length < (report.query.limit || 100) ? 'All records' : `Limited to ${report.query.limit}`}\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Query Details */}\n      {(report.query.conditions.length > 0 || report.query.groupBy || report.query.orderBy) && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Query Details</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            {report.query.conditions.length > 0 && (\n              <div>\n                <h4 className=\"text-sm font-medium mb-2\">Applied Filters:</h4>\n                <div className=\"space-y-1\">\n                  {report.query.conditions.map((condition, index) => (\n                    <div key={index} className=\"text-sm text-muted-foreground\">\n                      • {formatFieldName(condition.field)} {condition.operator.replace('_', ' ')} \"{condition.value}\"\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n            \n            {report.query.groupBy && report.query.groupBy.length > 0 && (\n              <div>\n                <h4 className=\"text-sm font-medium mb-2\">Grouped By:</h4>\n                <div className=\"text-sm text-muted-foreground\">\n                  {report.query.groupBy.map(formatFieldName).join(', ')}\n                </div>\n              </div>\n            )}\n            \n            {report.query.orderBy && report.query.orderBy.length > 0 && (\n              <div>\n                <h4 className=\"text-sm font-medium mb-2\">Ordered By:</h4>\n                <div className=\"text-sm text-muted-foreground\">\n                  {report.query.orderBy.map(formatFieldName).join(', ')}\n                </div>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Data Preview */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Data Preview</CardTitle>\n          <div className=\"text-sm text-muted-foreground\">\n            Showing {data.length} of {data.length} records\n            {data.length >= (report.query.limit || 100) && \" (limited)\"}\n          </div>\n        </CardHeader>\n        <CardContent>\n          {data.length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              <TableIcon className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n              <p>No data available</p>\n              <p className=\"text-sm\">Check your filters and data sources</p>\n            </div>\n          ) : (\n            <div className=\"rounded-md border overflow-x-auto\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    {report.query.fields.map((fieldId) => (\n                      <TableHead key={fieldId}>\n                        {formatFieldName(fieldId)}\n                      </TableHead>\n                    ))}\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {data.map((row, index) => (\n                    <TableRow key={index}>\n                      {report.query.fields.map((fieldId) => (\n                        <TableCell key={fieldId}>\n                          {formatCellValue(row[fieldId], fieldId)}\n                        </TableCell>\n                      ))}\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Visualization Preview */}\n      {report.visualization?.type === 'chart' && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Visualization Preview</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-center py-12 text-muted-foreground\">\n              {getVisualizationIcon()}\n              <p className=\"mt-4\">Chart visualization preview</p>\n              <p className=\"text-sm\">\n                {report.visualization.chartType?.toUpperCase()} chart with {report.visualization.xAxis} vs {report.visualization.yAxis}\n              </p>\n              <div className=\"mt-4 text-xs text-muted-foreground\">\n                Chart rendering would be implemented with a charting library like Chart.js or Recharts\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Footer */}\n      <div className=\"text-center text-xs text-muted-foreground pt-4 border-t\">\n        <p>Custom Report - Generated on {format(new Date(report.generatedAt), \"MMMM dd, yyyy 'at' HH:mm\")}</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAlBA;;;;;;;;;AA2BA,wBAAwB;AACxB,MAAM,WAAW;IACf;QACE,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,mBAAmB;IACrB;IACA;QACE,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,mBAAmB;IACrB;IACA;QACE,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,mBAAmB;IACrB;IACA;QACE,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,mBAAmB;IACrB;IACA;QACE,eAAe;QACf,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,mBAAmB;IACrB;CACD;AAEM,SAAS,oBAAoB,KAKT;QALS,EAClC,MAAM,EACN,UAAU,EACV,OAAO,EACP,SAAS,EACgB,GALS;QA8N7B,uBAUU;IAlOf,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,MAAM,kIAAA,CAAA,sBAAmB,CAAC,YAAY,CAAC,QAAQ;YAC1E,kIAAA,CAAA,sBAAmB,CAAC,YAAY,CAAC,MAAM;YACvC,uBAAA,iCAAA,WAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IACA,qFAAqF;IACrF,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,IAAI,GAAG,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,OAAO,KAAK,CAAC,KAAK,IAAI;IAExG,MAAM,uBAAuB;YACvB;QAAJ,IAAI,EAAA,wBAAA,OAAO,aAAa,cAApB,4CAAA,sBAAsB,IAAI,MAAK,SAAS;YAC1C,OAAQ,OAAO,aAAa,CAAC,SAAS;gBACpC,KAAK;oBAAO,qBAAO,6LAAC,qNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;gBACxC,KAAK;oBAAO,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;gBACvC,KAAK;oBAAQ,qBAAO,6LAAC,qNAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;gBAC1C;oBAAS,qBAAO,6LAAC,qNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;YACvC;QACF;QACA,qBAAO,6LAAC,uMAAA,CAAA,QAAS;YAAC,WAAU;;;;;;IAC9B;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ,QAAQ,KAAK,CAAC;QAC5B,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,MAAM,CAAC,QAAQ,MAAM,GAAG;YACxB,OAAO,MAAM,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,KAAK,CAAC,GAAG,OAAO,CAAC,YAAY;QAC5E;QACA,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC,OAAY;QACnC,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAElD,eAAe;QACf,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,SAAS;YACxD,IAAI;gBACF,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ;YACjC,EAAE,UAAM;gBACN,OAAO;YACT;QACF;QAEA,4BAA4B;QAC5B,IAAI,QAAQ,QAAQ,CAAC,aAAa,QAAQ,QAAQ,CAAC,WAAW;YAC5D,MAAM,UAAU,UAAU,YAAY,YACxB,UAAU,SAAS,cACnB,UAAU,WAAW,gBAAgB;YACnD,qBAAO,6LAAC,6HAAA,CAAA,QAAK;gBAAC,SAAS;0BAAiB;;;;;;QAC1C;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAE3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAEH,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAyB,OAAO,IAAI,IAAI;;;;;;kDACtD,6LAAC;wCAAE,WAAU;kDACV,OAAO,WAAW,IAAI;;;;;;;;;;;;;;;;;;kCAI7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,8HAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO1C,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,6LAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,6LAAC;4CAAI,WAAU;sDAAyB,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM;;;;;;sDAClE,6LAAC;4CAAI,WAAU;sDACZ,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;;;;;;;;;;;;8CAG9B,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,6LAAC;4CAAI,WAAU;sDAAyB,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM;;;;;;sDAClE,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,6LAAC;4CAAI,WAAU;sDAAyB,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM;;;;;;sDACtE,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,6LAAC;4CAAI,WAAU;sDAAyB,KAAK,MAAM;;;;;;sDACnD,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM,GAAG,CAAC,OAAO,KAAK,CAAC,KAAK,IAAI,GAAG,IAAI,gBAAgB,AAAC,cAAgC,OAAnB,OAAO,KAAK,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQtG,CAAC,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,KAAK,OAAO,KAAK,CAAC,OAAO,IAAI,OAAO,KAAK,CAAC,OAAO,mBAClF,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;;4BACpB,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,mBAChC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDACZ,OAAO,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACvC,6LAAC;gDAAgB,WAAU;;oDAAgC;oDACtD,gBAAgB,UAAU,KAAK;oDAAE;oDAAE,UAAU,QAAQ,CAAC,OAAO,CAAC,KAAK;oDAAK;oDAAG,UAAU,KAAK;oDAAC;;+CADtF;;;;;;;;;;;;;;;;4BAQjB,OAAO,KAAK,CAAC,OAAO,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,mBACrD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDACZ,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC;;;;;;;;;;;;4BAKrD,OAAO,KAAK,CAAC,OAAO,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,mBACrD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDACZ,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAS5D,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;;0CACT,6LAAC,4HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC;gCAAI,WAAU;;oCAAgC;oCACpC,KAAK,MAAM;oCAAC;oCAAK,KAAK,MAAM;oCAAC;oCACrC,KAAK,MAAM,IAAI,CAAC,OAAO,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK;;;;;;;;;;;;;kCAGnD,6LAAC,4HAAA,CAAA,cAAW;kCACT,KAAK,MAAM,KAAK,kBACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;iDAGzB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;;kDACJ,6LAAC,6HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,6HAAA,CAAA,WAAQ;sDACN,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,wBACxB,6LAAC,6HAAA,CAAA,YAAS;8DACP,gBAAgB;mDADH;;;;;;;;;;;;;;;kDAMtB,6LAAC,6HAAA,CAAA,YAAS;kDACP,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,6LAAC,6HAAA,CAAA,WAAQ;0DACN,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,wBACxB,6LAAC,6HAAA,CAAA,YAAS;kEACP,gBAAgB,GAAG,CAAC,QAAQ,EAAE;uDADjB;;;;;+CAFL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAgB5B,EAAA,wBAAA,OAAO,aAAa,cAApB,4CAAA,sBAAsB,IAAI,MAAK,yBAC9B,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,4HAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;gCACZ;8CACD,6LAAC;oCAAE,WAAU;8CAAO;;;;;;8CACpB,6LAAC;oCAAE,WAAU;;yCACV,kCAAA,OAAO,aAAa,CAAC,SAAS,cAA9B,sDAAA,gCAAgC,WAAW;wCAAG;wCAAa,OAAO,aAAa,CAAC,KAAK;wCAAC;wCAAK,OAAO,aAAa,CAAC,KAAK;;;;;;;8CAExH,6LAAC;oCAAI,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;0BAS5D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;wBAAE;wBAA8B,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,WAAW,GAAG;;;;;;;;;;;;;;;;;;AAI9E;KAxPgB", "debugId": null}}, {"offset": {"line": 5506, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/custom-report-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from \"@/components/ui/dialog\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from \"@/components/ui/tabs\"\nimport { CustomReportBuilder } from \"./custom-report-builder\"\nimport { CustomReportPreview } from \"./custom-report-preview\"\nimport { CustomReport, ExportFormat } from \"@/lib/types/reports\"\nimport { ArrowLeft, Users, Eye, Save } from \"lucide-react\"\nimport { toast } from \"sonner\"\n\ninterface CustomReportDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n}\n\nexport function CustomReportDialog({ open, onOpenChange }: CustomReportDialogProps) {\n  const [activeTab, setActiveTab] = useState(\"builder\")\n  const [currentReport, setCurrentReport] = useState<CustomReport | null>(null)\n  const [savedReports, setSavedReports] = useState<CustomReport[]>([])\n\n  const handleGenerateReport = (report: CustomReport) => {\n    setCurrentReport(report)\n    setActiveTab(\"preview\")\n    toast.success(\"Custom report generated successfully\", {\n      description: \"You can now preview, download, or print the report\"\n    })\n  }\n\n  const handlePreviewReport = (report: CustomReport) => {\n    setCurrentReport(report)\n    setActiveTab(\"preview\")\n  }\n\n  const handleSaveReport = (report: CustomReport) => {\n    setSavedReports([...savedReports, report])\n    toast.success(\"Report template saved\", {\n      description: \"You can reuse this template for future reports\"\n    })\n  }\n\n  const handleDownload = (format: ExportFormat) => {\n    toast.success(`Downloading custom report in ${format} format`, {\n      description: \"The download will start shortly\"\n    })\n  }\n\n  const handlePrint = () => {\n    window.print()\n    toast.success(\"Print dialog opened\", {\n      description: \"Please select your printer and print settings\"\n    })\n  }\n\n  const handleBackToBuilder = () => {\n    setActiveTab(\"builder\")\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-7xl max-h-[95vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Users className=\"h-5 w-5\" />\n            Custom Report Builder\n          </DialogTitle>\n          <DialogDescription>\n            Create flexible reports with custom data sources, filters, and visualizations\n          </DialogDescription>\n        </DialogHeader>\n\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-3\">\n            <TabsTrigger value=\"builder\" className=\"flex items-center gap-2\">\n              <Users className=\"h-4 w-4\" />\n              Report Builder\n            </TabsTrigger>\n            <TabsTrigger \n              value=\"preview\" \n              disabled={!currentReport}\n              className=\"flex items-center gap-2\"\n            >\n              <Eye className=\"h-4 w-4\" />\n              Preview Report\n            </TabsTrigger>\n            <TabsTrigger value=\"templates\" className=\"flex items-center gap-2\">\n              <Save className=\"h-4 w-4\" />\n              Saved Templates\n            </TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"builder\" className=\"mt-6\">\n            <CustomReportBuilder\n              onGenerate={handleGenerateReport}\n              onPreview={handlePreviewReport}\n              onSave={handleSaveReport}\n            />\n          </TabsContent>\n\n          <TabsContent value=\"preview\" className=\"mt-6\">\n            {currentReport ? (\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <Button \n                    variant=\"outline\" \n                    onClick={handleBackToBuilder}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4\" />\n                    Back to Builder\n                  </Button>\n                </div>\n                \n                <CustomReportPreview\n                  report={currentReport}\n                  onDownload={handleDownload}\n                  onPrint={handlePrint}\n                />\n              </div>\n            ) : (\n              <div className=\"text-center py-8 text-muted-foreground\">\n                <Users className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                <p>No report generated yet</p>\n                <p className=\"text-sm\">Use the report builder to create a custom report</p>\n              </div>\n            )}\n          </TabsContent>\n\n          <TabsContent value=\"templates\" className=\"mt-6\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h3 className=\"text-lg font-medium\">Saved Report Templates</h3>\n                  <p className=\"text-sm text-muted-foreground\">\n                    Reuse previously saved report configurations\n                  </p>\n                </div>\n                <Button \n                  variant=\"outline\"\n                  onClick={() => setActiveTab(\"builder\")}\n                >\n                  <Users className=\"mr-2 h-4 w-4\" />\n                  Create New\n                </Button>\n              </div>\n\n              {savedReports.length === 0 ? (\n                <div className=\"text-center py-12 text-muted-foreground\">\n                  <Save className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n                  <p>No saved templates</p>\n                  <p className=\"text-sm\">Save report configurations to reuse them later</p>\n                </div>\n              ) : (\n                <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n                  {savedReports.map((report, index) => (\n                    <div key={index} className=\"p-4 border rounded-lg space-y-3\">\n                      <div>\n                        <h4 className=\"font-medium\">{report.name}</h4>\n                        <p className=\"text-sm text-muted-foreground\">\n                          {report.description}\n                        </p>\n                      </div>\n                      \n                      <div className=\"space-y-2\">\n                        <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n                          <span>{report.query.tables.length} data sources</span>\n                          <span>•</span>\n                          <span>{report.query.fields.length} fields</span>\n                          <span>•</span>\n                          <span>{report.query.conditions.length} filters</span>\n                        </div>\n                        \n                        <div className=\"flex items-center gap-2\">\n                          <Button \n                            size=\"sm\" \n                            variant=\"outline\"\n                            onClick={() => {\n                              setCurrentReport(report)\n                              setActiveTab(\"preview\")\n                            }}\n                          >\n                            <Eye className=\"h-3 w-3 mr-1\" />\n                            Preview\n                          </Button>\n                          <Button \n                            size=\"sm\"\n                            onClick={() => handleGenerateReport(report)}\n                          >\n                            Generate\n                          </Button>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </TabsContent>\n        </Tabs>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;;;AAVA;;;;;;;;;AAiBO,SAAS,mBAAmB,KAA+C;QAA/C,EAAE,IAAI,EAAE,YAAY,EAA2B,GAA/C;;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAEnE,MAAM,uBAAuB,CAAC;QAC5B,iBAAiB;QACjB,aAAa;QACb,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,wCAAwC;YACpD,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB;QACjB,aAAa;IACf;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;eAAI;YAAc;SAAO;QACzC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,yBAAyB;YACrC,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,gCAAsC,OAAP,QAAO,YAAU;YAC7D,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,KAAK;QACZ,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,uBAAuB;YACnC,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB;QAC1B,aAAa;IACf;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG/B,6LAAC,8HAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC,4HAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;;sCAC7D,6LAAC,4HAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;;sDACrC,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,6LAAC,4HAAA,CAAA,cAAW;oCACV,OAAM;oCACN,UAAU,CAAC;oCACX,WAAU;;sDAEV,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG7B,6LAAC,4HAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,WAAU;;sDACvC,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;sCAKhC,6LAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACrC,cAAA,6LAAC,wJAAA,CAAA,sBAAmB;gCAClB,YAAY;gCACZ,WAAW;gCACX,QAAQ;;;;;;;;;;;sCAIZ,6LAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACpC,8BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;;8DAEV,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;kDAKrC,6LAAC,wJAAA,CAAA,sBAAmB;wCAClB,QAAQ;wCACR,YAAY;wCACZ,SAAS;;;;;;;;;;;qDAIb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAE;;;;;;kDACH,6LAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;;sCAK7B,6LAAC,4HAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;sCACvC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsB;;;;;;kEACpC,6LAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;0DAI/C,6LAAC,8HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,aAAa;;kEAE5B,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;oCAKrC,aAAa,MAAM,KAAK,kBACvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAE;;;;;;0DACH,6LAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;6DAGzB,6LAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,QAAQ,sBACzB,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAe,OAAO,IAAI;;;;;;0EACxC,6LAAC;gEAAE,WAAU;0EACV,OAAO,WAAW;;;;;;;;;;;;kEAIvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;;4EAAM,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM;4EAAC;;;;;;;kFAClC,6LAAC;kFAAK;;;;;;kFACN,6LAAC;;4EAAM,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM;4EAAC;;;;;;;kFAClC,6LAAC;kFAAK;;;;;;kFACN,6LAAC;;4EAAM,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM;4EAAC;;;;;;;;;;;;;0EAGxC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,8HAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,SAAS;4EACP,iBAAiB;4EACjB,aAAa;wEACf;;0FAEA,6LAAC,mMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;kFAGlC,6LAAC,8HAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAS,IAAM,qBAAqB;kFACrC;;;;;;;;;;;;;;;;;;;+CAhCG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+C9B;GA1LgB;KAAA", "debugId": null}}, {"offset": {"line": 6049, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/report-archive.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from \"@/components/ui/dropdown-menu\"\nimport { GeneratedReport, ReportArchive } from \"@/lib/types/reports\"\nimport { mockGeneratedReports } from \"@/lib/data/reports-mock-data\"\nimport { \n  Archive, \n  Search, \n  Download, \n  Trash2, \n  RotateCcw, \n  MoreHorizontal,\n  Calendar,\n  FileText,\n  Clock,\n  HardDrive\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\nimport { toast } from \"sonner\"\n\ninterface ReportArchiveProps {\n  className?: string\n}\n\n// Mock archived reports\nconst mockArchivedReports: (GeneratedReport & { archiveInfo: ReportArchive })[] = [\n  {\n    ...mockGeneratedReports[0],\n    id: \"ARC001\",\n    status: \"ARCHIVED\",\n    archiveInfo: {\n      id: \"ARCH001\",\n      reportId: \"RPT001\",\n      archivedAt: \"2024-12-01T10:00:00Z\",\n      archivedBy: \"admin\",\n      reason: \"Automatic archival after 30 days\",\n      retentionPeriod: 365,\n      autoDelete: true\n    }\n  },\n  {\n    ...mockGeneratedReports[1],\n    id: \"ARC002\", \n    status: \"ARCHIVED\",\n    archiveInfo: {\n      id: \"ARCH002\",\n      reportId: \"RPT002\",\n      archivedAt: \"2024-11-15T15:30:00Z\",\n      archivedBy: \"principal\",\n      reason: \"Manual archival for compliance\",\n      retentionPeriod: 2555, // 7 years\n      autoDelete: false\n    }\n  }\n]\n\nexport function ReportArchive({ className }: ReportArchiveProps) {\n  const [archivedReports, setArchivedReports] = useState(mockArchivedReports)\n  const [searchQuery, setSearchQuery] = useState(\"\")\n  const [sortBy, setSortBy] = useState<\"date\" | \"name\" | \"type\">(\"date\")\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"desc\")\n\n  // Filter and sort archived reports\n  const filteredReports = archivedReports\n    .filter(report => \n      report.config.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      report.config.description.toLowerCase().includes(searchQuery.toLowerCase())\n    )\n    .sort((a, b) => {\n      let comparison = 0\n      switch (sortBy) {\n        case \"date\":\n          comparison = new Date(a.archiveInfo.archivedAt).getTime() - new Date(b.archiveInfo.archivedAt).getTime()\n          break\n        case \"name\":\n          comparison = a.config.name.localeCompare(b.config.name)\n          break\n        case \"type\":\n          comparison = a.config.type.localeCompare(b.config.type)\n          break\n      }\n      return sortOrder === \"desc\" ? -comparison : comparison\n    })\n\n  const handleRestore = (reportId: string) => {\n    setArchivedReports(archivedReports.filter(report => report.id !== reportId))\n    toast.success(\"Report restored successfully\", {\n      description: \"The report has been moved back to the active reports list\"\n    })\n  }\n\n  const handlePermanentDelete = (reportId: string) => {\n    setArchivedReports(archivedReports.filter(report => report.id !== reportId))\n    toast.success(\"Report permanently deleted\", {\n      description: \"This action cannot be undone\"\n    })\n  }\n\n  const handleDownload = (reportId: string) => {\n    toast.success(\"Downloading archived report\")\n  }\n\n  const calculateDaysUntilDeletion = (archiveInfo: ReportArchive): number => {\n    if (!archiveInfo.autoDelete) return -1\n    \n    const archivedDate = new Date(archiveInfo.archivedAt)\n    const deletionDate = new Date(archivedDate.getTime() + (archiveInfo.retentionPeriod * 24 * 60 * 60 * 1000))\n    const now = new Date()\n    const daysLeft = Math.ceil((deletionDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000))\n    \n    return Math.max(0, daysLeft)\n  }\n\n  const getRetentionBadge = (archiveInfo: ReportArchive) => {\n    if (!archiveInfo.autoDelete) {\n      return <Badge variant=\"secondary\">Permanent</Badge>\n    }\n    \n    const daysLeft = calculateDaysUntilDeletion(archiveInfo)\n    \n    if (daysLeft === 0) {\n      return <Badge variant=\"destructive\">Expires Today</Badge>\n    } else if (daysLeft <= 7) {\n      return <Badge variant=\"destructive\">Expires in {daysLeft} days</Badge>\n    } else if (daysLeft <= 30) {\n      return <Badge variant=\"secondary\">Expires in {daysLeft} days</Badge>\n    } else {\n      return <Badge variant=\"outline\">{daysLeft} days left</Badge>\n    }\n  }\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes'\n    const k = 1024\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  const getTotalArchiveSize = () => {\n    const totalBytes = archivedReports.reduce((sum, report) => sum + (report.fileSize || 0), 0)\n    return formatFileSize(totalBytes)\n  }\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold flex items-center gap-2\">\n            <Archive className=\"h-6 w-6\" />\n            Report Archive\n          </h2>\n          <p className=\"text-muted-foreground\">\n            Manage archived reports and retention policies\n          </p>\n        </div>\n        <div className=\"flex items-center gap-4 text-sm text-muted-foreground\">\n          <div className=\"flex items-center gap-1\">\n            <FileText className=\"h-4 w-4\" />\n            {archivedReports.length} reports\n          </div>\n          <div className=\"flex items-center gap-1\">\n            <HardDrive className=\"h-4 w-4\" />\n            {getTotalArchiveSize()}\n          </div>\n        </div>\n      </div>\n\n      {/* Archive Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Total Archived</p>\n                <p className=\"text-2xl font-bold\">{archivedReports.length}</p>\n              </div>\n              <Archive className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Expiring Soon</p>\n                <p className=\"text-2xl font-bold text-orange-600\">\n                  {archivedReports.filter(r => {\n                    const days = calculateDaysUntilDeletion(r.archiveInfo)\n                    return days >= 0 && days <= 30\n                  }).length}\n                </p>\n              </div>\n              <Clock className=\"h-8 w-8 text-orange-600\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Permanent</p>\n                <p className=\"text-2xl font-bold text-blue-600\">\n                  {archivedReports.filter(r => !r.archiveInfo.autoDelete).length}\n                </p>\n              </div>\n              <FileText className=\"h-8 w-8 text-blue-600\" />\n            </div>\n          </CardContent>\n        </Card>\n        \n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Storage Used</p>\n                <p className=\"text-2xl font-bold\">{getTotalArchiveSize()}</p>\n              </div>\n              <HardDrive className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Search and Filters */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"relative flex-1\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search archived reports...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n            \n            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {\n              const [field, order] = value.split('-')\n              setSortBy(field as typeof sortBy)\n              setSortOrder(order as typeof sortOrder)\n            }}>\n              <SelectTrigger className=\"w-40\">\n                <SelectValue placeholder=\"Sort by\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"date-desc\">Newest First</SelectItem>\n                <SelectItem value=\"date-asc\">Oldest First</SelectItem>\n                <SelectItem value=\"name-asc\">Name A-Z</SelectItem>\n                <SelectItem value=\"name-desc\">Name Z-A</SelectItem>\n                <SelectItem value=\"type-asc\">Type A-Z</SelectItem>\n                <SelectItem value=\"type-desc\">Type Z-A</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Archived Reports Table */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Archived Reports</CardTitle>\n          <CardDescription>\n            View and manage your archived reports\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          {filteredReports.length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              <Archive className=\"h-12 w-12 mx-auto mb-4 opacity-50\" />\n              <p>No archived reports found</p>\n              <p className=\"text-sm\">Reports will appear here when archived</p>\n            </div>\n          ) : (\n            <div className=\"rounded-md border\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>Report</TableHead>\n                    <TableHead>Archived</TableHead>\n                    <TableHead>Retention</TableHead>\n                    <TableHead>Size</TableHead>\n                    <TableHead>Reason</TableHead>\n                    <TableHead className=\"text-right\">Actions</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {filteredReports.map((report) => (\n                    <TableRow key={report.id}>\n                      <TableCell>\n                        <div className=\"space-y-1\">\n                          <div className=\"font-medium\">{report.config.name}</div>\n                          <div className=\"text-sm text-muted-foreground\">\n                            {report.config.description}\n                          </div>\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            {report.config.type}\n                          </Badge>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"text-sm\">\n                          {format(new Date(report.archiveInfo.archivedAt), \"MMM dd, yyyy\")}\n                          <div className=\"text-xs text-muted-foreground\">\n                            by {report.archiveInfo.archivedBy}\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        {getRetentionBadge(report.archiveInfo)}\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"text-sm\">\n                          {report.fileSize ? formatFileSize(report.fileSize) : '-'}\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"text-sm text-muted-foreground max-w-48 truncate\">\n                          {report.archiveInfo.reason}\n                        </div>\n                      </TableCell>\n                      <TableCell className=\"text-right\">\n                        <div className=\"flex items-center justify-end gap-2\">\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => handleDownload(report.id)}\n                          >\n                            <Download className=\"h-4 w-4\" />\n                          </Button>\n                          \n                          <DropdownMenu>\n                            <DropdownMenuTrigger asChild>\n                              <Button variant=\"ghost\" size=\"sm\">\n                                <MoreHorizontal className=\"h-4 w-4\" />\n                              </Button>\n                            </DropdownMenuTrigger>\n                            <DropdownMenuContent align=\"end\">\n                              <DropdownMenuItem onClick={() => handleRestore(report.id)}>\n                                <RotateCcw className=\"h-4 w-4 mr-2\" />\n                                Restore\n                              </DropdownMenuItem>\n                              <DropdownMenuItem \n                                onClick={() => handlePermanentDelete(report.id)}\n                                className=\"text-destructive\"\n                              >\n                                <Trash2 className=\"h-4 w-4 mr-2\" />\n                                Delete Permanently\n                              </DropdownMenuItem>\n                            </DropdownMenuContent>\n                          </DropdownMenu>\n                        </div>\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;;;AA1BA;;;;;;;;;;;;;AAgCA,wBAAwB;AACxB,MAAM,sBAA4E;IAChF;QACE,GAAG,yIAAA,CAAA,uBAAoB,CAAC,EAAE;QAC1B,IAAI;QACJ,QAAQ;QACR,aAAa;YACX,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,QAAQ;YACR,iBAAiB;YACjB,YAAY;QACd;IACF;IACA;QACE,GAAG,yIAAA,CAAA,uBAAoB,CAAC,EAAE;QAC1B,IAAI;QACJ,QAAQ;QACR,aAAa;YACX,IAAI;YACJ,UAAU;YACV,YAAY;YACZ,YAAY;YACZ,QAAQ;YACR,iBAAiB;YACjB,YAAY;QACd;IACF;CACD;AAEM,SAAS,cAAc,KAAiC;QAAjC,EAAE,SAAS,EAAsB,GAAjC;;IAC5B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,mCAAmC;IACnC,MAAM,kBAAkB,gBACrB,MAAM,CAAC,CAAA,SACN,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACjE,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,KAEzE,IAAI,CAAC,CAAC,GAAG;QACR,IAAI,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,aAAa,IAAI,KAAK,EAAE,WAAW,CAAC,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,CAAC,UAAU,EAAE,OAAO;gBACtG;YACF,KAAK;gBACH,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,IAAI;gBACtD;YACF,KAAK;gBACH,aAAa,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,IAAI;gBACtD;QACJ;QACA,OAAO,cAAc,SAAS,CAAC,aAAa;IAC9C;IAEF,MAAM,gBAAgB,CAAC;QACrB,mBAAmB,gBAAgB,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QAClE,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,gCAAgC;YAC5C,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB,gBAAgB,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QAClE,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,8BAA8B;YAC1C,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,6BAA6B,CAAC;QAClC,IAAI,CAAC,YAAY,UAAU,EAAE,OAAO,CAAC;QAErC,MAAM,eAAe,IAAI,KAAK,YAAY,UAAU;QACpD,MAAM,eAAe,IAAI,KAAK,aAAa,OAAO,KAAM,YAAY,eAAe,GAAG,KAAK,KAAK,KAAK;QACrG,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,IAAI,CAAC,CAAC,aAAa,OAAO,KAAK,IAAI,OAAO,EAAE,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI;QAE1F,OAAO,KAAK,GAAG,CAAC,GAAG;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,YAAY,UAAU,EAAE;YAC3B,qBAAO,6LAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAY;;;;;;QACpC;QAEA,MAAM,WAAW,2BAA2B;QAE5C,IAAI,aAAa,GAAG;YAClB,qBAAO,6LAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAc;;;;;;QACtC,OAAO,IAAI,YAAY,GAAG;YACxB,qBAAO,6LAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;;oBAAc;oBAAY;oBAAS;;;;;;;QAC3D,OAAO,IAAI,YAAY,IAAI;YACzB,qBAAO,6LAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;;oBAAY;oBAAY;oBAAS;;;;;;;QACzD,OAAO;YACL,qBAAO,6LAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;;oBAAW;oBAAS;;;;;;;QAC5C;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,sBAAsB;QAC1B,MAAM,aAAa,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,CAAC,OAAO,QAAQ,IAAI,CAAC,GAAG;QACzF,OAAO,eAAe;IACxB;IAEA,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAE3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGjC,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCACnB,gBAAgB,MAAM;oCAAC;;;;;;;0CAE1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCACpB;;;;;;;;;;;;;;;;;;;0BAMP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAsB,gBAAgB,MAAM;;;;;;;;;;;;kDAE3D,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKzB,6LAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DACV,gBAAgB,MAAM,CAAC,CAAA;oDACtB,MAAM,OAAO,2BAA2B,EAAE,WAAW;oDACrD,OAAO,QAAQ,KAAK,QAAQ;gDAC9B,GAAG,MAAM;;;;;;;;;;;;kDAGb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKvB,6LAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DACV,gBAAgB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,WAAW,CAAC,UAAU,EAAE,MAAM;;;;;;;;;;;;kDAGlE,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;kDAErC,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7B,6LAAC,4HAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,6HAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAId,6LAAC,8HAAA,CAAA,SAAM;gCAAC,OAAO,AAAC,GAAY,OAAV,QAAO,KAAa,OAAV;gCAAa,eAAe,CAAC;oCACvD,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,KAAK,CAAC;oCACnC,UAAU;oCACV,aAAa;gCACf;;kDACE,6LAAC,8HAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6LAAC,8HAAA,CAAA,gBAAa;;0DACZ,6LAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;0DAC9B,6LAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,6LAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,6LAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;0DAC9B,6LAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAW;;;;;;0DAC7B,6LAAC,8HAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxC,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;;0CACT,6LAAC,4HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,4HAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,4HAAA,CAAA,cAAW;kCACT,gBAAgB,MAAM,KAAK,kBAC1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;iDAGzB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;;kDACJ,6LAAC,6HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,6HAAA,CAAA,WAAQ;;8DACP,6LAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,6HAAA,CAAA,YAAS;oDAAC,WAAU;8DAAa;;;;;;;;;;;;;;;;;kDAGtC,6LAAC,6HAAA,CAAA,YAAS;kDACP,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC,6HAAA,CAAA,WAAQ;;kEACP,6LAAC,6HAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAe,OAAO,MAAM,CAAC,IAAI;;;;;;8EAChD,6LAAC;oEAAI,WAAU;8EACZ,OAAO,MAAM,CAAC,WAAW;;;;;;8EAE5B,6LAAC,6HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;8EAChC,OAAO,MAAM,CAAC,IAAI;;;;;;;;;;;;;;;;;kEAIzB,6LAAC,6HAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;gEACZ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,WAAW,CAAC,UAAU,GAAG;8EACjD,6LAAC;oEAAI,WAAU;;wEAAgC;wEACzC,OAAO,WAAW,CAAC,UAAU;;;;;;;;;;;;;;;;;;kEAIvC,6LAAC,6HAAA,CAAA,YAAS;kEACP,kBAAkB,OAAO,WAAW;;;;;;kEAEvC,6LAAC,6HAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;sEACZ,OAAO,QAAQ,GAAG,eAAe,OAAO,QAAQ,IAAI;;;;;;;;;;;kEAGzD,6LAAC,6HAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;sEACZ,OAAO,WAAW,CAAC,MAAM;;;;;;;;;;;kEAG9B,6LAAC,6HAAA,CAAA,YAAS;wDAAC,WAAU;kEACnB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,8HAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,eAAe,OAAO,EAAE;8EAEvC,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAGtB,6LAAC,wIAAA,CAAA,eAAY;;sFACX,6LAAC,wIAAA,CAAA,sBAAmB;4EAAC,OAAO;sFAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;gFAAC,SAAQ;gFAAQ,MAAK;0FAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;oFAAC,WAAU;;;;;;;;;;;;;;;;sFAG9B,6LAAC,wIAAA,CAAA,sBAAmB;4EAAC,OAAM;;8FACzB,6LAAC,wIAAA,CAAA,mBAAgB;oFAAC,SAAS,IAAM,cAAc,OAAO,EAAE;;sGACtD,6LAAC,mNAAA,CAAA,YAAS;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;8FAGxC,6LAAC,wIAAA,CAAA,mBAAgB;oFACf,SAAS,IAAM,sBAAsB,OAAO,EAAE;oFAC9C,WAAU;;sGAEV,6LAAC,6MAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CA1DhC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2E5C;GAvTgB;KAAA", "debugId": null}}, {"offset": {"line": 7067, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/report-wizard.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState } from \"react\"\nimport { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { ReportTypeSelector } from \"./report-type-selector\"\nimport { ReportFilters } from \"./report-filters\"\nimport { ReportType, ReportFilters as ReportFiltersType, ReportConfig } from \"@/lib/types/reports\"\nimport { \n  ChevronLeft, \n  ChevronRight, \n  FileText, \n  Filter, \n  Settings, \n  Eye, \n  Download,\n  CheckCircle,\n  Wand2\n} from \"lucide-react\"\nimport { toast } from \"sonner\"\n\ninterface ReportWizardProps {\n  onComplete: (config: ReportConfig) => void\n  onCancel: () => void\n  className?: string\n}\n\ntype WizardStep = \"type\" | \"filters\" | \"settings\" | \"preview\"\n\nconst wizardSteps = [\n  { id: \"type\", title: \"Report Type\", description: \"Choose the type of report to generate\", icon: FileText },\n  { id: \"filters\", title: \"Filters\", description: \"Set date ranges and data filters\", icon: Filter },\n  { id: \"settings\", title: \"Settings\", description: \"Configure report options\", icon: Settings },\n  { id: \"preview\", title: \"Preview\", description: \"Review and generate report\", icon: Eye }\n]\n\nexport function ReportWizard({ onComplete, onCancel, className }: ReportWizardProps) {\n  const [currentStep, setCurrentStep] = useState<WizardStep>(\"type\")\n  const [selectedReportType, setSelectedReportType] = useState<ReportType>(\"SF2\")\n  const [reportFilters, setReportFilters] = useState<ReportFiltersType>({})\n  const [reportSettings, setReportSettings] = useState({\n    includeSignatures: true,\n    includePhotos: false,\n    includeRemarks: true,\n    showStatistics: true,\n    pageOrientation: \"portrait\" as \"portrait\" | \"landscape\",\n    fontSize: \"medium\" as \"small\" | \"medium\" | \"large\",\n    includeHeader: true,\n    includeFooter: true,\n    watermark: \"\"\n  })\n  const [reportName, setReportName] = useState(\"\")\n  const [reportDescription, setReportDescription] = useState(\"\")\n\n  const getCurrentStepIndex = () => {\n    return wizardSteps.findIndex(step => step.id === currentStep)\n  }\n\n  const getProgress = () => {\n    return ((getCurrentStepIndex() + 1) / wizardSteps.length) * 100\n  }\n\n  const canProceed = () => {\n    switch (currentStep) {\n      case \"type\":\n        return selectedReportType !== undefined\n      case \"filters\":\n        return true // Filters are optional\n      case \"settings\":\n        return reportName.trim() !== \"\"\n      case \"preview\":\n        return true\n      default:\n        return false\n    }\n  }\n\n  const handleNext = () => {\n    const currentIndex = getCurrentStepIndex()\n    if (currentIndex < wizardSteps.length - 1) {\n      setCurrentStep(wizardSteps[currentIndex + 1].id as WizardStep)\n    }\n  }\n\n  const handlePrevious = () => {\n    const currentIndex = getCurrentStepIndex()\n    if (currentIndex > 0) {\n      setCurrentStep(wizardSteps[currentIndex - 1].id as WizardStep)\n    }\n  }\n\n  const handleComplete = () => {\n    const config: ReportConfig = {\n      id: `wizard_${Date.now()}`,\n      name: reportName,\n      type: selectedReportType,\n      description: reportDescription,\n      dateRange: {\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: new Date().toISOString().split('T')[0]\n      },\n      filters: reportFilters,\n      settings: reportSettings,\n      createdBy: \"current-user\",\n      createdAt: new Date().toISOString(),\n      lastModified: new Date().toISOString()\n    }\n\n    onComplete(config)\n    toast.success(\"Report configuration completed\", {\n      description: \"Your report is being generated\"\n    })\n  }\n\n  const generateDefaultName = () => {\n    const today = new Date().toLocaleDateString()\n    const typeNames = {\n      SF2: \"SF2 Daily Attendance\",\n      SF4: \"SF4 Monthly Movement\", \n      DAILY: \"Daily Summary\",\n      WEEKLY: \"Weekly Summary\",\n      MONTHLY: \"Monthly Summary\",\n      ANNUAL: \"Annual Summary\",\n      CUSTOM: \"Custom Report\"\n    }\n    return `${typeNames[selectedReportType]} - ${today}`\n  }\n\n  const generateDefaultDescription = () => {\n    const descriptions = {\n      SF2: \"Official DepEd SF2 Daily Attendance Report of Learners\",\n      SF4: \"Official DepEd SF4 Monthly Report on Learner's Movement\",\n      DAILY: \"Daily attendance summary for all students\",\n      WEEKLY: \"Weekly attendance trends and statistics\",\n      MONTHLY: \"Monthly attendance overview and analysis\",\n      ANNUAL: \"Annual attendance and performance report\",\n      CUSTOM: \"Custom report with selected data and filters\"\n    }\n    return descriptions[selectedReportType]\n  }\n\n  // Auto-generate name and description when report type changes\n  React.useEffect(() => {\n    if (!reportName) {\n      setReportName(generateDefaultName())\n    }\n    if (!reportDescription) {\n      setReportDescription(generateDefaultDescription())\n    }\n  }, [selectedReportType])\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Wizard Header */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"p-2 bg-primary/10 rounded-lg\">\n                <Wand2 className=\"h-6 w-6 text-primary\" />\n              </div>\n              <div>\n                <CardTitle>Report Generation Wizard</CardTitle>\n                <CardDescription>\n                  Step-by-step guide to create your report\n                </CardDescription>\n              </div>\n            </div>\n            <Button variant=\"ghost\" onClick={onCancel}>\n              Cancel\n            </Button>\n          </div>\n        </CardHeader>\n        <CardContent>\n          {/* Progress Bar */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between text-sm\">\n              <span>Step {getCurrentStepIndex() + 1} of {wizardSteps.length}</span>\n              <span>{Math.round(getProgress())}% Complete</span>\n            </div>\n            <Progress value={getProgress()} className=\"w-full\" />\n          </div>\n\n          {/* Step Indicators */}\n          <div className=\"flex items-center justify-between mt-6\">\n            {wizardSteps.map((step, index) => {\n              const isActive = step.id === currentStep\n              const isCompleted = index < getCurrentStepIndex()\n              const Icon = step.icon\n\n              return (\n                <div key={step.id} className=\"flex flex-col items-center space-y-2\">\n                  <div className={`\n                    flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors\n                    ${isActive ? 'border-primary bg-primary text-primary-foreground' : \n                      isCompleted ? 'border-green-500 bg-green-500 text-white' : \n                      'border-muted-foreground/30 bg-background'}\n                  `}>\n                    {isCompleted ? (\n                      <CheckCircle className=\"h-5 w-5\" />\n                    ) : (\n                      <Icon className=\"h-5 w-5\" />\n                    )}\n                  </div>\n                  <div className=\"text-center\">\n                    <div className={`text-sm font-medium ${isActive ? 'text-primary' : isCompleted ? 'text-green-600' : 'text-muted-foreground'}`}>\n                      {step.title}\n                    </div>\n                    <div className=\"text-xs text-muted-foreground hidden sm:block\">\n                      {step.description}\n                    </div>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Step Content */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          {currentStep === \"type\" && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold mb-2\">Choose Report Type</h3>\n                <p className=\"text-muted-foreground mb-6\">\n                  Select the type of report you want to generate. Each type has specific features and formatting.\n                </p>\n              </div>\n              <ReportTypeSelector\n                selectedType={selectedReportType}\n                onTypeSelect={setSelectedReportType}\n              />\n            </div>\n          )}\n\n          {currentStep === \"filters\" && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold mb-2\">Configure Filters</h3>\n                <p className=\"text-muted-foreground mb-6\">\n                  Set date ranges and apply filters to customize your report data.\n                </p>\n              </div>\n              <ReportFilters\n                filters={reportFilters}\n                onFiltersChange={setReportFilters}\n                reportType={selectedReportType}\n              />\n            </div>\n          )}\n\n          {currentStep === \"settings\" && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold mb-2\">Report Settings</h3>\n                <p className=\"text-muted-foreground mb-6\">\n                  Configure report options and provide a name and description.\n                </p>\n              </div>\n              \n              {/* Report Name and Description */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Report Name</label>\n                  <input\n                    type=\"text\"\n                    value={reportName}\n                    onChange={(e) => setReportName(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-input rounded-md\"\n                    placeholder=\"Enter report name\"\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Page Orientation</label>\n                  <select\n                    value={reportSettings.pageOrientation}\n                    onChange={(e) => setReportSettings({\n                      ...reportSettings,\n                      pageOrientation: e.target.value as \"portrait\" | \"landscape\"\n                    })}\n                    className=\"w-full px-3 py-2 border border-input rounded-md\"\n                  >\n                    <option value=\"portrait\">Portrait</option>\n                    <option value=\"landscape\">Landscape</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">Description</label>\n                <textarea\n                  value={reportDescription}\n                  onChange={(e) => setReportDescription(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-input rounded-md\"\n                  rows={3}\n                  placeholder=\"Enter report description\"\n                />\n              </div>\n\n              {/* Report Options */}\n              <div className=\"space-y-4\">\n                <h4 className=\"font-medium\">Report Options</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-3\">\n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={reportSettings.includeSignatures}\n                        onChange={(e) => setReportSettings({\n                          ...reportSettings,\n                          includeSignatures: e.target.checked\n                        })}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Include signature fields</span>\n                    </label>\n                    \n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={reportSettings.includeRemarks}\n                        onChange={(e) => setReportSettings({\n                          ...reportSettings,\n                          includeRemarks: e.target.checked\n                        })}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Include remarks column</span>\n                    </label>\n                    \n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={reportSettings.showStatistics}\n                        onChange={(e) => setReportSettings({\n                          ...reportSettings,\n                          showStatistics: e.target.checked\n                        })}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Show statistics summary</span>\n                    </label>\n                  </div>\n                  \n                  <div className=\"space-y-3\">\n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={reportSettings.includeHeader}\n                        onChange={(e) => setReportSettings({\n                          ...reportSettings,\n                          includeHeader: e.target.checked\n                        })}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Include header</span>\n                    </label>\n                    \n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={reportSettings.includeFooter}\n                        onChange={(e) => setReportSettings({\n                          ...reportSettings,\n                          includeFooter: e.target.checked\n                        })}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Include footer</span>\n                    </label>\n                    \n                    <div className=\"space-y-2\">\n                      <label className=\"text-sm font-medium\">Font Size</label>\n                      <select\n                        value={reportSettings.fontSize}\n                        onChange={(e) => setReportSettings({\n                          ...reportSettings,\n                          fontSize: e.target.value as \"small\" | \"medium\" | \"large\"\n                        })}\n                        className=\"w-full px-3 py-2 border border-input rounded-md\"\n                      >\n                        <option value=\"small\">Small</option>\n                        <option value=\"medium\">Medium</option>\n                        <option value=\"large\">Large</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {currentStep === \"preview\" && (\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold mb-2\">Review Configuration</h3>\n                <p className=\"text-muted-foreground mb-6\">\n                  Review your report configuration before generating.\n                </p>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"space-y-4\">\n                  <div>\n                    <h4 className=\"font-medium text-sm text-muted-foreground\">Report Type</h4>\n                    <Badge variant=\"outline\" className=\"mt-1\">{selectedReportType}</Badge>\n                  </div>\n                  \n                  <div>\n                    <h4 className=\"font-medium text-sm text-muted-foreground\">Report Name</h4>\n                    <p className=\"text-sm\">{reportName}</p>\n                  </div>\n                  \n                  <div>\n                    <h4 className=\"font-medium text-sm text-muted-foreground\">Description</h4>\n                    <p className=\"text-sm\">{reportDescription}</p>\n                  </div>\n                </div>\n                \n                <div className=\"space-y-4\">\n                  <div>\n                    <h4 className=\"font-medium text-sm text-muted-foreground\">Filters Applied</h4>\n                    <div className=\"flex flex-wrap gap-1 mt-1\">\n                      {Object.keys(reportFilters).length === 0 ? (\n                        <Badge variant=\"secondary\">No filters</Badge>\n                      ) : (\n                        Object.entries(reportFilters).map(([key, value]) => (\n                          <Badge key={key} variant=\"secondary\" className=\"text-xs\">\n                            {key}: {Array.isArray(value) ? value.length : 1}\n                          </Badge>\n                        ))\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <h4 className=\"font-medium text-sm text-muted-foreground\">Settings</h4>\n                    <div className=\"text-sm space-y-1\">\n                      <p>Orientation: {reportSettings.pageOrientation}</p>\n                      <p>Font Size: {reportSettings.fontSize}</p>\n                      <p>Signatures: {reportSettings.includeSignatures ? 'Yes' : 'No'}</p>\n                      <p>Statistics: {reportSettings.showStatistics ? 'Yes' : 'No'}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Navigation */}\n      <div className=\"flex items-center justify-between\">\n        <Button\n          variant=\"outline\"\n          onClick={handlePrevious}\n          disabled={getCurrentStepIndex() === 0}\n        >\n          <ChevronLeft className=\"mr-2 h-4 w-4\" />\n          Previous\n        </Button>\n\n        <div className=\"flex items-center gap-2\">\n          {getCurrentStepIndex() === wizardSteps.length - 1 ? (\n            <Button onClick={handleComplete} disabled={!canProceed()}>\n              <Download className=\"mr-2 h-4 w-4\" />\n              Generate Report\n            </Button>\n          ) : (\n            <Button onClick={handleNext} disabled={!canProceed()}>\n              Next\n              <ChevronRight className=\"ml-2 h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AArBA;;;;;;;;;;AA+BA,MAAM,cAAc;IAClB;QAAE,IAAI;QAAQ,OAAO;QAAe,aAAa;QAAyC,MAAM,iNAAA,CAAA,WAAQ;IAAC;IACzG;QAAE,IAAI;QAAW,OAAO;QAAW,aAAa;QAAoC,MAAM,yMAAA,CAAA,SAAM;IAAC;IACjG;QAAE,IAAI;QAAY,OAAO;QAAY,aAAa;QAA4B,MAAM,6MAAA,CAAA,WAAQ;IAAC;IAC7F;QAAE,IAAI;QAAW,OAAO;QAAW,aAAa;QAA8B,MAAM,mMAAA,CAAA,MAAG;IAAC;CACzF;AAEM,SAAS,aAAa,KAAsD;QAAtD,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAqB,GAAtD;;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,mBAAmB;QACnB,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,UAAU;QACV,eAAe;QACf,eAAe;QACf,WAAW;IACb;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,sBAAsB;QAC1B,OAAO,YAAY,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACnD;IAEA,MAAM,cAAc;QAClB,OAAO,AAAC,CAAC,wBAAwB,CAAC,IAAI,YAAY,MAAM,GAAI;IAC9D;IAEA,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,OAAO,uBAAuB;YAChC,KAAK;gBACH,OAAO,KAAK,uBAAuB;;YACrC,KAAK;gBACH,OAAO,WAAW,IAAI,OAAO;YAC/B,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;QACjB,MAAM,eAAe;QACrB,IAAI,eAAe,YAAY,MAAM,GAAG,GAAG;YACzC,eAAe,WAAW,CAAC,eAAe,EAAE,CAAC,EAAE;QACjD;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,eAAe;QACrB,IAAI,eAAe,GAAG;YACpB,eAAe,WAAW,CAAC,eAAe,EAAE,CAAC,EAAE;QACjD;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,SAAuB;YAC3B,IAAI,AAAC,UAAoB,OAAX,KAAK,GAAG;YACtB,MAAM;YACN,MAAM;YACN,aAAa;YACb,WAAW;gBACT,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACjD,SAAS,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACjD;YACA,SAAS;YACT,UAAU;YACV,WAAW;YACX,WAAW,IAAI,OAAO,WAAW;YACjC,cAAc,IAAI,OAAO,WAAW;QACtC;QAEA,WAAW;QACX,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kCAAkC;YAC9C,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,QAAQ,IAAI,OAAO,kBAAkB;QAC3C,MAAM,YAAY;YAChB,KAAK;YACL,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA,OAAO,AAAC,GAAqC,OAAnC,SAAS,CAAC,mBAAmB,EAAC,OAAW,OAAN;IAC/C;IAEA,MAAM,6BAA6B;QACjC,MAAM,eAAe;YACnB,KAAK;YACL,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,QAAQ;QACV;QACA,OAAO,YAAY,CAAC,mBAAmB;IACzC;IAEA,8DAA8D;IAC9D,6JAAA,CAAA,UAAK,CAAC,SAAS;kCAAC;YACd,IAAI,CAAC,YAAY;gBACf,cAAc;YAChB;YACA,IAAI,CAAC,mBAAmB;gBACtB,qBAAqB;YACvB;QACF;iCAAG;QAAC;KAAmB;IAEvB,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAE3B,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,kNAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;;8DACC,6LAAC,4HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,4HAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;;;;;;;8CAKrB,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,SAAS;8CAAU;;;;;;;;;;;;;;;;;kCAK/C,6LAAC,4HAAA,CAAA,cAAW;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAK;oDAAM,wBAAwB;oDAAE;oDAAK,YAAY,MAAM;;;;;;;0DAC7D,6LAAC;;oDAAM,KAAK,KAAK,CAAC;oDAAe;;;;;;;;;;;;;kDAEnC,6LAAC,gIAAA,CAAA,WAAQ;wCAAC,OAAO;wCAAe,WAAU;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,MAAM;oCACtB,MAAM,WAAW,KAAK,EAAE,KAAK;oCAC7B,MAAM,cAAc,QAAQ;oCAC5B,MAAM,OAAO,KAAK,IAAI;oCAEtB,qBACE,6LAAC;wCAAkB,WAAU;;0DAC3B,6LAAC;gDAAI,WAAW,AAAC,iIAI8B,OAF3C,WAAW,sDACX,cAAc,6CACd,4CAA2C;0DAE5C,4BACC,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,6LAAC;oDAAK,WAAU;;;;;;;;;;;0DAGpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,uBAA2G,OAArF,WAAW,iBAAiB,cAAc,mBAAmB;kEACjG,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAI,WAAU;kEACZ,KAAK,WAAW;;;;;;;;;;;;;uCAlBb,KAAK,EAAE;;;;;gCAuBrB;;;;;;;;;;;;;;;;;;0BAMN,6LAAC,4HAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;;wBACpB,gBAAgB,wBACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAI5C,6LAAC,uJAAA,CAAA,qBAAkB;oCACjB,cAAc;oCACd,cAAc;;;;;;;;;;;;wBAKnB,gBAAgB,2BACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAI5C,6LAAC,8IAAA,CAAA,gBAAa;oCACZ,SAAS;oCACT,iBAAiB;oCACjB,YAAY;;;;;;;;;;;;wBAKjB,gBAAgB,4BACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAM5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAsB;;;;;;8DACvC,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAGhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAsB;;;;;;8DACvC,6LAAC;oDACC,OAAO,eAAe,eAAe;oDACrC,UAAU,CAAC,IAAM,kBAAkB;4DACjC,GAAG,cAAc;4DACjB,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDACjC;oDACA,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAW;;;;;;sEACzB,6LAAC;4DAAO,OAAM;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAKhC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;4CACpD,WAAU;4CACV,MAAM;4CACN,aAAY;;;;;;;;;;;;8CAKhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEACC,MAAK;oEACL,SAAS,eAAe,iBAAiB;oEACzC,UAAU,CAAC,IAAM,kBAAkB;4EACjC,GAAG,cAAc;4EACjB,mBAAmB,EAAE,MAAM,CAAC,OAAO;wEACrC;oEACA,WAAU;;;;;;8EAEZ,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAG5B,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEACC,MAAK;oEACL,SAAS,eAAe,cAAc;oEACtC,UAAU,CAAC,IAAM,kBAAkB;4EACjC,GAAG,cAAc;4EACjB,gBAAgB,EAAE,MAAM,CAAC,OAAO;wEAClC;oEACA,WAAU;;;;;;8EAEZ,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAG5B,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEACC,MAAK;oEACL,SAAS,eAAe,cAAc;oEACtC,UAAU,CAAC,IAAM,kBAAkB;4EACjC,GAAG,cAAc;4EACjB,gBAAgB,EAAE,MAAM,CAAC,OAAO;wEAClC;oEACA,WAAU;;;;;;8EAEZ,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;;;;;;;8DAI9B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEACC,MAAK;oEACL,SAAS,eAAe,aAAa;oEACrC,UAAU,CAAC,IAAM,kBAAkB;4EACjC,GAAG,cAAc;4EACjB,eAAe,EAAE,MAAM,CAAC,OAAO;wEACjC;oEACA,WAAU;;;;;;8EAEZ,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAG5B,6LAAC;4DAAM,WAAU;;8EACf,6LAAC;oEACC,MAAK;oEACL,SAAS,eAAe,aAAa;oEACrC,UAAU,CAAC,IAAM,kBAAkB;4EACjC,GAAG,cAAc;4EACjB,eAAe,EAAE,MAAM,CAAC,OAAO;wEACjC;oEACA,WAAU;;;;;;8EAEZ,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAG5B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAM,WAAU;8EAAsB;;;;;;8EACvC,6LAAC;oEACC,OAAO,eAAe,QAAQ;oEAC9B,UAAU,CAAC,IAAM,kBAAkB;4EACjC,GAAG,cAAc;4EACjB,UAAU,EAAE,MAAM,CAAC,KAAK;wEAC1B;oEACA,WAAU;;sFAEV,6LAAC;4EAAO,OAAM;sFAAQ;;;;;;sFACtB,6LAAC;4EAAO,OAAM;sFAAS;;;;;;sFACvB,6LAAC;4EAAO,OAAM;sFAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASnC,gBAAgB,2BACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA4C;;;;;;sEAC1D,6LAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAQ;;;;;;;;;;;;8DAG7C,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA4C;;;;;;sEAC1D,6LAAC;4DAAE,WAAU;sEAAW;;;;;;;;;;;;8DAG1B,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA4C;;;;;;sEAC1D,6LAAC;4DAAE,WAAU;sEAAW;;;;;;;;;;;;;;;;;;sDAI5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA4C;;;;;;sEAC1D,6LAAC;4DAAI,WAAU;sEACZ,OAAO,IAAI,CAAC,eAAe,MAAM,KAAK,kBACrC,6LAAC,6HAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAY;;;;;uEAE3B,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC;oEAAC,CAAC,KAAK,MAAM;qFAC7C,6LAAC,6HAAA,CAAA,QAAK;oEAAW,SAAQ;oEAAY,WAAU;;wEAC5C;wEAAI;wEAAG,MAAM,OAAO,CAAC,SAAS,MAAM,MAAM,GAAG;;mEADpC;;;;;;;;;;;;;;;;;8DAQpB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA4C;;;;;;sEAC1D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;wEAAE;wEAAc,eAAe,eAAe;;;;;;;8EAC/C,6LAAC;;wEAAE;wEAAY,eAAe,QAAQ;;;;;;;8EACtC,6LAAC;;wEAAE;wEAAa,eAAe,iBAAiB,GAAG,QAAQ;;;;;;;8EAC3D,6LAAC;;wEAAE;wEAAa,eAAe,cAAc,GAAG,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWxE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU,0BAA0B;;0CAEpC,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAI1C,6LAAC;wBAAI,WAAU;kCACZ,0BAA0B,YAAY,MAAM,GAAG,kBAC9C,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAS;4BAAgB,UAAU,CAAC;;8CAC1C,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;iDAIvC,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAS;4BAAY,UAAU,CAAC;;gCAAc;8CAEpD,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;GA5bgB;KAAA", "debugId": null}}, {"offset": {"line": 8265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/report-wizard-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from \"@/components/ui/dialog\"\nimport { ReportWizard } from \"./report-wizard\"\nimport { ReportConfig } from \"@/lib/types/reports\"\nimport { Wand2 } from \"lucide-react\"\nimport { toast } from \"sonner\"\n\ninterface ReportWizardDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  onComplete?: (config: ReportConfig) => void\n}\n\nexport function ReportWizardDialog({ \n  open, \n  onOpenChange, \n  onComplete \n}: ReportWizardDialogProps) {\n  const handleComplete = (config: ReportConfig) => {\n    onComplete?.(config)\n    onOpenChange(false)\n    toast.success(\"Report wizard completed\", {\n      description: \"Your report configuration has been saved and generation started\"\n    })\n  }\n\n  const handleCancel = () => {\n    onOpenChange(false)\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-6xl max-h-[95vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <Wand2 className=\"h-5 w-5\" />\n            Report Generation Wizard\n          </DialogTitle>\n          <DialogDescription>\n            Follow the step-by-step guide to create your perfect report\n          </DialogDescription>\n        </DialogHeader>\n\n        <ReportWizard\n          onComplete={handleComplete}\n          onCancel={handleCancel}\n        />\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAcO,SAAS,mBAAmB,KAIT;QAJS,EACjC,IAAI,EACJ,YAAY,EACZ,UAAU,EACc,GAJS;IAKjC,MAAM,iBAAiB,CAAC;QACtB,uBAAA,iCAAA,WAAa;QACb,aAAa;QACb,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,2BAA2B;YACvC,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,aAAa;IACf;IAEA,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,kNAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG/B,6LAAC,8HAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,6LAAC,6IAAA,CAAA,eAAY;oBACX,YAAY;oBACZ,UAAU;;;;;;;;;;;;;;;;;AAKpB;KArCgB", "debugId": null}}, {"offset": {"line": 8361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/bulk-report-generator.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Progress } from \"@/components/ui/progress\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { ReportType, ReportConfig } from \"@/lib/types/reports\"\nimport { DateRangePicker, DateRange } from \"@/components/ui/date-range-picker\"\nimport { \n  Layers, \n  Play, \n  Pause, \n  CheckCircle, \n  XCircle, \n  Clock,\n  Download,\n  FileText,\n  Calendar\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\nimport { toast } from \"sonner\"\n\ninterface BulkReportGeneratorProps {\n  onGenerate: (configs: ReportConfig[]) => void\n  className?: string\n}\n\ninterface BulkReportJob {\n  id: string\n  name: string\n  type: ReportType\n  dateRange: DateRange\n  filters: {\n    grades?: string[]\n    sections?: string[]\n  }\n  status: 'pending' | 'generating' | 'completed' | 'failed'\n  progress: number\n  error?: string\n  generatedAt?: string\n}\n\nconst gradeOptions = [\"7\", \"8\", \"9\", \"10\", \"11\", \"12\"]\nconst sectionOptions = [\"A\", \"B\", \"C\", \"D\", \"E\"]\n\nexport function BulkReportGenerator({ onGenerate, className }: BulkReportGeneratorProps) {\n  const [reportType, setReportType] = useState<ReportType>(\"SF2\")\n  const [dateRange, setDateRange] = useState<DateRange>({\n    from: new Date(),\n    to: new Date()\n  })\n  const [selectedGrades, setSelectedGrades] = useState<string[]>([])\n  const [selectedSections, setSelectedSections] = useState<string[]>([])\n  const [generateBy, setGenerateBy] = useState<\"grade\" | \"section\" | \"both\">(\"section\")\n  const [jobs, setJobs] = useState<BulkReportJob[]>([])\n  const [isGenerating, setIsGenerating] = useState(false)\n\n  const handleGradeChange = (grade: string, checked: boolean) => {\n    if (checked) {\n      setSelectedGrades([...selectedGrades, grade])\n    } else {\n      setSelectedGrades(selectedGrades.filter(g => g !== grade))\n    }\n  }\n\n  const handleSectionChange = (section: string, checked: boolean) => {\n    if (checked) {\n      setSelectedSections([...selectedSections, section])\n    } else {\n      setSelectedSections(selectedSections.filter(s => s !== section))\n    }\n  }\n\n  const generateJobList = (): BulkReportJob[] => {\n    const jobs: BulkReportJob[] = []\n\n    if (generateBy === \"grade\") {\n      selectedGrades.forEach(grade => {\n        jobs.push({\n          id: `bulk_${Date.now()}_${grade}`,\n          name: `${reportType} Report - Grade ${grade}`,\n          type: reportType,\n          dateRange,\n          filters: { grades: [grade] },\n          status: 'pending',\n          progress: 0\n        })\n      })\n    } else if (generateBy === \"section\") {\n      selectedGrades.forEach(grade => {\n        selectedSections.forEach(section => {\n          jobs.push({\n            id: `bulk_${Date.now()}_${grade}_${section}`,\n            name: `${reportType} Report - Grade ${grade}-${section}`,\n            type: reportType,\n            dateRange,\n            filters: { grades: [grade], sections: [section] },\n            status: 'pending',\n            progress: 0\n          })\n        })\n      })\n    } else { // both\n      // Generate one report per grade\n      selectedGrades.forEach(grade => {\n        jobs.push({\n          id: `bulk_${Date.now()}_grade_${grade}`,\n          name: `${reportType} Report - Grade ${grade} (All Sections)`,\n          type: reportType,\n          dateRange,\n          filters: { grades: [grade] },\n          status: 'pending',\n          progress: 0\n        })\n      })\n      \n      // Generate one report per section within each grade\n      selectedGrades.forEach(grade => {\n        selectedSections.forEach(section => {\n          jobs.push({\n            id: `bulk_${Date.now()}_section_${grade}_${section}`,\n            name: `${reportType} Report - Grade ${grade}-${section}`,\n            type: reportType,\n            dateRange,\n            filters: { grades: [grade], sections: [section] },\n            status: 'pending',\n            progress: 0\n          })\n        })\n      })\n    }\n\n    return jobs\n  }\n\n  const handleStartBulkGeneration = async () => {\n    if (!dateRange.from || !dateRange.to) {\n      toast.error(\"Please select a date range\")\n      return\n    }\n\n    if (selectedGrades.length === 0) {\n      toast.error(\"Please select at least one grade\")\n      return\n    }\n\n    if (generateBy !== \"grade\" && selectedSections.length === 0) {\n      toast.error(\"Please select at least one section\")\n      return\n    }\n\n    const newJobs = generateJobList()\n    setJobs(newJobs)\n    setIsGenerating(true)\n\n    // Simulate bulk generation process\n    for (let i = 0; i < newJobs.length; i++) {\n      const job = newJobs[i]\n      \n      // Update job status to generating\n      setJobs(prevJobs => \n        prevJobs.map(j => \n          j.id === job.id \n            ? { ...j, status: 'generating' as const }\n            : j\n        )\n      )\n\n      // Simulate generation progress\n      for (let progress = 0; progress <= 100; progress += 20) {\n        await new Promise(resolve => setTimeout(resolve, 200))\n        setJobs(prevJobs => \n          prevJobs.map(j => \n            j.id === job.id \n              ? { ...j, progress }\n              : j\n          )\n        )\n      }\n\n      // Mark as completed (or failed randomly for demo)\n      const isSuccess = Math.random() > 0.1 // 90% success rate\n      setJobs(prevJobs => \n        prevJobs.map(j => \n          j.id === job.id \n            ? { \n                ...j, \n                status: isSuccess ? 'completed' as const : 'failed' as const,\n                progress: 100,\n                generatedAt: isSuccess ? new Date().toISOString() : undefined,\n                error: isSuccess ? undefined : \"Generation failed due to data issues\"\n              }\n            : j\n        )\n      )\n    }\n\n    setIsGenerating(false)\n    \n    const successCount = newJobs.filter(j => Math.random() > 0.1).length\n    toast.success(`Bulk generation completed`, {\n      description: `${successCount} of ${newJobs.length} reports generated successfully`\n    })\n  }\n\n  const handleClearJobs = () => {\n    setJobs([])\n  }\n\n  const getStatusIcon = (status: BulkReportJob['status']) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"h-4 w-4 text-gray-500\" />\n      case 'generating':\n        return <Play className=\"h-4 w-4 text-blue-500 animate-spin\" />\n      case 'completed':\n        return <CheckCircle className=\"h-4 w-4 text-green-500\" />\n      case 'failed':\n        return <XCircle className=\"h-4 w-4 text-red-500\" />\n    }\n  }\n\n  const getStatusBadge = (status: BulkReportJob['status']) => {\n    const variants = {\n      pending: \"secondary\",\n      generating: \"default\", \n      completed: \"default\",\n      failed: \"destructive\"\n    } as const\n\n    const colors = {\n      pending: \"text-gray-600\",\n      generating: \"text-blue-600\",\n      completed: \"text-green-600\", \n      failed: \"text-red-600\"\n    } as const\n\n    return (\n      <Badge variant={variants[status]} className={colors[status]}>\n        {status.charAt(0).toUpperCase() + status.slice(1)}\n      </Badge>\n    )\n  }\n\n  const completedJobs = jobs.filter(j => j.status === 'completed').length\n  const totalJobs = jobs.length\n  const overallProgress = totalJobs > 0 ? (completedJobs / totalJobs) * 100 : 0\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Header */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center gap-3\">\n            <div className=\"p-2 bg-purple-100 rounded-lg\">\n              <Layers className=\"h-6 w-6 text-purple-600\" />\n            </div>\n            <div>\n              <CardTitle>Bulk Report Generator</CardTitle>\n              <CardDescription>\n                Generate multiple reports at once for different grades and sections\n              </CardDescription>\n            </div>\n          </div>\n        </CardHeader>\n      </Card>\n\n      {/* Configuration */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Bulk Generation Settings</CardTitle>\n          <CardDescription>\n            Configure the reports you want to generate in bulk\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          {/* Report Type and Date Range */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label>Report Type</Label>\n              <Select value={reportType} onValueChange={(value) => setReportType(value as ReportType)}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"SF2\">SF2 Daily Attendance</SelectItem>\n                  <SelectItem value=\"SF4\">SF4 Monthly Movement</SelectItem>\n                  <SelectItem value=\"DAILY\">Daily Summary</SelectItem>\n                  <SelectItem value=\"WEEKLY\">Weekly Summary</SelectItem>\n                  <SelectItem value=\"MONTHLY\">Monthly Summary</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label>Generation Strategy</Label>\n              <Select value={generateBy} onValueChange={(value) => setGenerateBy(value as typeof generateBy)}>\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"grade\">One report per grade</SelectItem>\n                  <SelectItem value=\"section\">One report per section</SelectItem>\n                  <SelectItem value=\"both\">Both grade and section reports</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          {/* Date Range */}\n          <div className=\"space-y-2\">\n            <Label>Date Range</Label>\n            <DateRangePicker\n              value={dateRange}\n              onChange={setDateRange}\n            />\n          </div>\n\n          {/* Grade Selection */}\n          <div className=\"space-y-3\">\n            <Label>Select Grades</Label>\n            <div className=\"grid grid-cols-3 md:grid-cols-6 gap-2\">\n              {gradeOptions.map((grade) => (\n                <div key={grade} className=\"flex items-center space-x-2\">\n                  <Checkbox\n                    id={`bulk-grade-${grade}`}\n                    checked={selectedGrades.includes(grade)}\n                    onCheckedChange={(checked) => handleGradeChange(grade, checked as boolean)}\n                  />\n                  <Label htmlFor={`bulk-grade-${grade}`} className=\"text-sm\">\n                    Grade {grade}\n                  </Label>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Section Selection */}\n          {generateBy !== \"grade\" && (\n            <div className=\"space-y-3\">\n              <Label>Select Sections</Label>\n              <div className=\"grid grid-cols-5 gap-2\">\n                {sectionOptions.map((section) => (\n                  <div key={section} className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id={`bulk-section-${section}`}\n                      checked={selectedSections.includes(section)}\n                      onCheckedChange={(checked) => handleSectionChange(section, checked as boolean)}\n                    />\n                    <Label htmlFor={`bulk-section-${section}`} className=\"text-sm\">\n                      Section {section}\n                    </Label>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Preview */}\n          <div className=\"p-4 bg-muted rounded-lg\">\n            <h4 className=\"font-medium mb-2\">Generation Preview</h4>\n            <p className=\"text-sm text-muted-foreground\">\n              This will generate <strong>{generateJobList().length}</strong> reports\n              {dateRange.from && dateRange.to && (\n                <> for the period from <strong>{format(dateRange.from, \"MMM dd, yyyy\")}</strong> to <strong>{format(dateRange.to, \"MMM dd, yyyy\")}</strong></>\n              )}\n            </p>\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex items-center gap-2\">\n            <Button \n              onClick={handleStartBulkGeneration}\n              disabled={isGenerating || selectedGrades.length === 0}\n            >\n              <Layers className=\"mr-2 h-4 w-4\" />\n              Start Bulk Generation\n            </Button>\n            {jobs.length > 0 && (\n              <Button variant=\"outline\" onClick={handleClearJobs} disabled={isGenerating}>\n                Clear Jobs\n              </Button>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Generation Progress */}\n      {jobs.length > 0 && (\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <CardTitle>Generation Progress</CardTitle>\n                <CardDescription>\n                  {completedJobs} of {totalJobs} reports completed\n                </CardDescription>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-2xl font-bold\">{Math.round(overallProgress)}%</div>\n                <div className=\"text-sm text-muted-foreground\">Overall Progress</div>\n              </div>\n            </div>\n            <Progress value={overallProgress} className=\"w-full\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"rounded-md border\">\n              <Table>\n                <TableHeader>\n                  <TableRow>\n                    <TableHead>Report Name</TableHead>\n                    <TableHead>Status</TableHead>\n                    <TableHead>Progress</TableHead>\n                    <TableHead>Generated</TableHead>\n                    <TableHead className=\"text-right\">Actions</TableHead>\n                  </TableRow>\n                </TableHeader>\n                <TableBody>\n                  {jobs.map((job) => (\n                    <TableRow key={job.id}>\n                      <TableCell>\n                        <div className=\"flex items-center gap-2\">\n                          {getStatusIcon(job.status)}\n                          <div>\n                            <div className=\"font-medium\">{job.name}</div>\n                            {job.error && (\n                              <div className=\"text-xs text-red-600\">{job.error}</div>\n                            )}\n                          </div>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        {getStatusBadge(job.status)}\n                      </TableCell>\n                      <TableCell>\n                        <div className=\"flex items-center gap-2\">\n                          <Progress value={job.progress} className=\"w-20\" />\n                          <span className=\"text-sm\">{job.progress}%</span>\n                        </div>\n                      </TableCell>\n                      <TableCell>\n                        {job.generatedAt ? (\n                          <div className=\"text-sm\">\n                            {format(new Date(job.generatedAt), \"HH:mm:ss\")}\n                          </div>\n                        ) : (\n                          <span className=\"text-sm text-muted-foreground\">-</span>\n                        )}\n                      </TableCell>\n                      <TableCell className=\"text-right\">\n                        {job.status === 'completed' && (\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <Download className=\"h-4 w-4\" />\n                          </Button>\n                        )}\n                      </TableCell>\n                    </TableRow>\n                  ))}\n                </TableBody>\n              </Table>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AA1BA;;;;;;;;;;;;;;AAgDA,MAAM,eAAe;IAAC;IAAK;IAAK;IAAK;IAAM;IAAM;CAAK;AACtD,MAAM,iBAAiB;IAAC;IAAK;IAAK;IAAK;IAAK;CAAI;AAEzC,SAAS,oBAAoB,KAAmD;QAAnD,EAAE,UAAU,EAAE,SAAS,EAA4B,GAAnD;;IAClC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM,IAAI;QACV,IAAI,IAAI;IACV;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAC3E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,oBAAoB,CAAC,OAAe;QACxC,IAAI,SAAS;YACX,kBAAkB;mBAAI;gBAAgB;aAAM;QAC9C,OAAO;YACL,kBAAkB,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM;QACrD;IACF;IAEA,MAAM,sBAAsB,CAAC,SAAiB;QAC5C,IAAI,SAAS;YACX,oBAAoB;mBAAI;gBAAkB;aAAQ;QACpD,OAAO;YACL,oBAAoB,iBAAiB,MAAM,CAAC,CAAA,IAAK,MAAM;QACzD;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,OAAwB,EAAE;QAEhC,IAAI,eAAe,SAAS;YAC1B,eAAe,OAAO,CAAC,CAAA;gBACrB,KAAK,IAAI,CAAC;oBACR,IAAI,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAAS,OAAN;oBAC1B,MAAM,AAAC,GAA+B,OAA7B,YAAW,oBAAwB,OAAN;oBACtC,MAAM;oBACN;oBACA,SAAS;wBAAE,QAAQ;4BAAC;yBAAM;oBAAC;oBAC3B,QAAQ;oBACR,UAAU;gBACZ;YACF;QACF,OAAO,IAAI,eAAe,WAAW;YACnC,eAAe,OAAO,CAAC,CAAA;gBACrB,iBAAiB,OAAO,CAAC,CAAA;oBACvB,KAAK,IAAI,CAAC;wBACR,IAAI,AAAC,QAAqB,OAAd,KAAK,GAAG,IAAG,KAAY,OAAT,OAAM,KAAW,OAAR;wBACnC,MAAM,AAAC,GAA+B,OAA7B,YAAW,oBAA2B,OAAT,OAAM,KAAW,OAAR;wBAC/C,MAAM;wBACN;wBACA,SAAS;4BAAE,QAAQ;gCAAC;6BAAM;4BAAE,UAAU;gCAAC;6BAAQ;wBAAC;wBAChD,QAAQ;wBACR,UAAU;oBACZ;gBACF;YACF;QACF,OAAO;YACL,gCAAgC;YAChC,eAAe,OAAO,CAAC,CAAA;gBACrB,KAAK,IAAI,CAAC;oBACR,IAAI,AAAC,QAA2B,OAApB,KAAK,GAAG,IAAG,WAAe,OAAN;oBAChC,MAAM,AAAC,GAA+B,OAA7B,YAAW,oBAAwB,OAAN,OAAM;oBAC5C,MAAM;oBACN;oBACA,SAAS;wBAAE,QAAQ;4BAAC;yBAAM;oBAAC;oBAC3B,QAAQ;oBACR,UAAU;gBACZ;YACF;YAEA,oDAAoD;YACpD,eAAe,OAAO,CAAC,CAAA;gBACrB,iBAAiB,OAAO,CAAC,CAAA;oBACvB,KAAK,IAAI,CAAC;wBACR,IAAI,AAAC,QAA6B,OAAtB,KAAK,GAAG,IAAG,aAAoB,OAAT,OAAM,KAAW,OAAR;wBAC3C,MAAM,AAAC,GAA+B,OAA7B,YAAW,oBAA2B,OAAT,OAAM,KAAW,OAAR;wBAC/C,MAAM;wBACN;wBACA,SAAS;4BAAE,QAAQ;gCAAC;6BAAM;4BAAE,UAAU;gCAAC;6BAAQ;wBAAC;wBAChD,QAAQ;wBACR,UAAU;oBACZ;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,4BAA4B;QAChC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACpC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,eAAe,WAAW,iBAAiB,MAAM,KAAK,GAAG;YAC3D,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,UAAU;QAChB,QAAQ;QACR,gBAAgB;QAEhB,mCAAmC;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,MAAM,OAAO,CAAC,EAAE;YAEtB,kCAAkC;YAClC,QAAQ,CAAA,WACN,SAAS,GAAG,CAAC,CAAA,IACX,EAAE,EAAE,KAAK,IAAI,EAAE,GACX;wBAAE,GAAG,CAAC;wBAAE,QAAQ;oBAAsB,IACtC;YAIR,+BAA+B;YAC/B,IAAK,IAAI,WAAW,GAAG,YAAY,KAAK,YAAY,GAAI;gBACtD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,QAAQ,CAAA,WACN,SAAS,GAAG,CAAC,CAAA,IACX,EAAE,EAAE,KAAK,IAAI,EAAE,GACX;4BAAE,GAAG,CAAC;4BAAE;wBAAS,IACjB;YAGV;YAEA,kDAAkD;YAClD,MAAM,YAAY,KAAK,MAAM,KAAK,IAAI,mBAAmB;;YACzD,QAAQ,CAAA,WACN,SAAS,GAAG,CAAC,CAAA,IACX,EAAE,EAAE,KAAK,IAAI,EAAE,GACX;wBACE,GAAG,CAAC;wBACJ,QAAQ,YAAY,cAAuB;wBAC3C,UAAU;wBACV,aAAa,YAAY,IAAI,OAAO,WAAW,KAAK;wBACpD,OAAO,YAAY,YAAY;oBACjC,IACA;QAGV;QAEA,gBAAgB;QAEhB,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,KAAK,MAAM,KAAK,KAAK,MAAM;QACpE,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAE,6BAA4B;YACzC,aAAa,AAAC,GAAqB,OAAnB,cAAa,QAAqB,OAAf,QAAQ,MAAM,EAAC;QACpD;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ,EAAE;IACZ;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;QAC9B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW;YACf,SAAS;YACT,YAAY;YACZ,WAAW;YACX,QAAQ;QACV;QAEA,MAAM,SAAS;YACb,SAAS;YACT,YAAY;YACZ,WAAW;YACX,QAAQ;QACV;QAEA,qBACE,6LAAC,6HAAA,CAAA,QAAK;YAAC,SAAS,QAAQ,CAAC,OAAO;YAAE,WAAW,MAAM,CAAC,OAAO;sBACxD,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;;;;;;IAGrD;IAEA,MAAM,gBAAgB,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;IACvE,MAAM,YAAY,KAAK,MAAM;IAC7B,MAAM,kBAAkB,YAAY,IAAI,AAAC,gBAAgB,YAAa,MAAM;IAE5E,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAE3B,6LAAC,4HAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,4HAAA,CAAA,aAAU;8BACT,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,6LAAC;;kDACC,6LAAC,4HAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,4HAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzB,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;;0CACT,6LAAC,4HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,4HAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,8HAAA,CAAA,SAAM;gDAAC,OAAO;gDAAY,eAAe,CAAC,QAAU,cAAc;;kEACjE,6LAAC,8HAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,8HAAA,CAAA,gBAAa;;0EACZ,6LAAC,8HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,6LAAC,8HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,6LAAC,8HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,6LAAC,8HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,6LAAC,8HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAKlC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,6LAAC,8HAAA,CAAA,SAAM;gDAAC,OAAO;gDAAY,eAAe,CAAC,QAAU,cAAc;;kEACjE,6LAAC,8HAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,8HAAA,CAAA,cAAW;;;;;;;;;;kEAEd,6LAAC,8HAAA,CAAA,gBAAa;;0EACZ,6LAAC,8HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAQ;;;;;;0EAC1B,6LAAC,8HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAU;;;;;;0EAC5B,6LAAC,8HAAA,CAAA,aAAU;gEAAC,OAAM;0EAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC,+IAAA,CAAA,kBAAe;wCACd,OAAO;wCACP,UAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC,gIAAA,CAAA,WAAQ;wDACP,IAAI,AAAC,cAAmB,OAAN;wDAClB,SAAS,eAAe,QAAQ,CAAC;wDACjC,iBAAiB,CAAC,UAAY,kBAAkB,OAAO;;;;;;kEAEzD,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAS,AAAC,cAAmB,OAAN;wDAAS,WAAU;;4DAAU;4DAClD;;;;;;;;+CAPD;;;;;;;;;;;;;;;;4BAef,eAAe,yBACd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6HAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,6LAAC;wCAAI,WAAU;kDACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;gDAAkB,WAAU;;kEAC3B,6LAAC,gIAAA,CAAA,WAAQ;wDACP,IAAI,AAAC,gBAAuB,OAAR;wDACpB,SAAS,iBAAiB,QAAQ,CAAC;wDACnC,iBAAiB,CAAC,UAAY,oBAAoB,SAAS;;;;;;kEAE7D,6LAAC,6HAAA,CAAA,QAAK;wDAAC,SAAS,AAAC,gBAAuB,OAAR;wDAAW,WAAU;;4DAAU;4DACpD;;;;;;;;+CAPH;;;;;;;;;;;;;;;;0CAgBlB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;;4CAAgC;0DACxB,6LAAC;0DAAQ,kBAAkB,MAAM;;;;;;4CAAU;4CAC7D,UAAU,IAAI,IAAI,UAAU,EAAE,kBAC7B;;oDAAE;kEAAqB,6LAAC;kEAAQ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,IAAI,EAAE;;;;;;oDAAyB;kEAAI,6LAAC;kEAAQ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;0CAMxH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,gBAAgB,eAAe,MAAM,KAAK;;0DAEpD,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;oCAGpC,KAAK,MAAM,GAAG,mBACb,6LAAC,8HAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;wCAAiB,UAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;YASnF,KAAK,MAAM,GAAG,mBACb,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;;0CACT,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC,4HAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,4HAAA,CAAA,kBAAe;;oDACb;oDAAc;oDAAK;oDAAU;;;;;;;;;;;;;kDAGlC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDAAsB,KAAK,KAAK,CAAC;oDAAiB;;;;;;;0DACjE,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAGnD,6LAAC,gIAAA,CAAA,WAAQ;gCAAC,OAAO;gCAAiB,WAAU;;;;;;;;;;;;kCAE9C,6LAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;;kDACJ,6LAAC,6HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,6HAAA,CAAA,WAAQ;;8DACP,6LAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,6HAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,6HAAA,CAAA,YAAS;oDAAC,WAAU;8DAAa;;;;;;;;;;;;;;;;;kDAGtC,6LAAC,6HAAA,CAAA,YAAS;kDACP,KAAK,GAAG,CAAC,CAAC,oBACT,6LAAC,6HAAA,CAAA,WAAQ;;kEACP,6LAAC,6HAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;gEACZ,cAAc,IAAI,MAAM;8EACzB,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;sFAAe,IAAI,IAAI;;;;;;wEACrC,IAAI,KAAK,kBACR,6LAAC;4EAAI,WAAU;sFAAwB,IAAI,KAAK;;;;;;;;;;;;;;;;;;;;;;;kEAKxD,6LAAC,6HAAA,CAAA,YAAS;kEACP,eAAe,IAAI,MAAM;;;;;;kEAE5B,6LAAC,6HAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,gIAAA,CAAA,WAAQ;oEAAC,OAAO,IAAI,QAAQ;oEAAE,WAAU;;;;;;8EACzC,6LAAC;oEAAK,WAAU;;wEAAW,IAAI,QAAQ;wEAAC;;;;;;;;;;;;;;;;;;kEAG5C,6LAAC,6HAAA,CAAA,YAAS;kEACP,IAAI,WAAW,iBACd,6LAAC;4DAAI,WAAU;sEACZ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,IAAI,WAAW,GAAG;;;;;iFAGrC,6LAAC;4DAAK,WAAU;sEAAgC;;;;;;;;;;;kEAGpD,6LAAC,6HAAA,CAAA,YAAS;wDAAC,WAAU;kEAClB,IAAI,MAAM,KAAK,6BACd,6LAAC,8HAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;+CAjCb,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CzC;GAragB;KAAA", "debugId": null}}, {"offset": {"line": 9442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/report-analytics.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { mockGeneratedReports, mockReportAnalytics } from \"@/lib/data/reports-mock-data\"\nimport { \n  BarChart3, \n  TrendingUp, \n  TrendingDown, \n  Users, \n  FileText, \n  Download, \n  Clock,\n  Calendar,\n  Eye,\n  Share\n} from \"lucide-react\"\nimport { format, subDays, subMonths } from \"date-fns\"\nimport { useState } from \"react\"\n\ninterface ReportAnalyticsProps {\n  className?: string\n}\n\nexport function ReportAnalytics({ className }: ReportAnalyticsProps) {\n  const [timeRange, setTimeRange] = useState<\"7d\" | \"30d\" | \"90d\" | \"1y\">(\"30d\")\n\n  // Mock analytics data based on time range\n  const getAnalyticsData = () => {\n    const baseData = mockReportAnalytics\n    \n    // Simulate different data based on time range\n    const multipliers = {\n      \"7d\": 0.2,\n      \"30d\": 1,\n      \"90d\": 3,\n      \"1y\": 12\n    }\n    \n    const multiplier = multipliers[timeRange]\n    \n    return {\n      ...baseData,\n      totalReports: Math.round(baseData.totalReports * multiplier),\n      totalDownloads: Math.round(baseData.totalDownloads * multiplier),\n      uniqueUsers: Math.round(baseData.uniqueUsers * multiplier * 0.8),\n      averageGenerationTime: baseData.averageGenerationTime,\n      reportTypeDistribution: baseData.reportTypeDistribution.map(item => ({\n        ...item,\n        count: Math.round(item.count * multiplier)\n      })),\n      dailyActivity: baseData.dailyActivity.map(item => ({\n        ...item,\n        reports: Math.round(item.reports * multiplier * 0.1),\n        downloads: Math.round(item.downloads * multiplier * 0.1)\n      }))\n    }\n  }\n\n  const analytics = getAnalyticsData()\n\n  // Calculate trends (mock data)\n  const getTrendPercentage = (current: number, previous: number) => {\n    if (previous === 0) return 0\n    return ((current - previous) / previous) * 100\n  }\n\n  const reportsTrend = getTrendPercentage(analytics.totalReports, analytics.totalReports * 0.8)\n  const downloadsTrend = getTrendPercentage(analytics.totalDownloads, analytics.totalDownloads * 0.9)\n  const usersTrend = getTrendPercentage(analytics.uniqueUsers, analytics.uniqueUsers * 0.85)\n\n  const formatTrend = (trend: number) => {\n    const isPositive = trend >= 0\n    return {\n      value: Math.abs(trend).toFixed(1),\n      isPositive,\n      icon: isPositive ? TrendingUp : TrendingDown,\n      color: isPositive ? \"text-green-600\" : \"text-red-600\"\n    }\n  }\n\n  const getTimeRangeLabel = () => {\n    const labels = {\n      \"7d\": \"Last 7 days\",\n      \"30d\": \"Last 30 days\", \n      \"90d\": \"Last 90 days\",\n      \"1y\": \"Last year\"\n    }\n    return labels[timeRange]\n  }\n\n  // Most popular reports\n  const popularReports = mockGeneratedReports\n    .sort((a, b) => b.downloadCount - a.downloadCount)\n    .slice(0, 5)\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h2 className=\"text-2xl font-bold flex items-center gap-2\">\n            <BarChart3 className=\"h-6 w-6\" />\n            Report Analytics\n          </h2>\n          <p className=\"text-muted-foreground\">\n            Track report usage, performance, and trends\n          </p>\n        </div>\n        <Select value={timeRange} onValueChange={(value) => setTimeRange(value as typeof timeRange)}>\n          <SelectTrigger className=\"w-40\">\n            <SelectValue />\n          </SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"7d\">Last 7 days</SelectItem>\n            <SelectItem value=\"30d\">Last 30 days</SelectItem>\n            <SelectItem value=\"90d\">Last 90 days</SelectItem>\n            <SelectItem value=\"1y\">Last year</SelectItem>\n          </SelectContent>\n        </Select>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Total Reports</p>\n                <p className=\"text-2xl font-bold\">{analytics.totalReports.toLocaleString()}</p>\n                <div className=\"flex items-center gap-1 text-xs\">\n                  {(() => {\n                    const trend = formatTrend(reportsTrend)\n                    const Icon = trend.icon\n                    return (\n                      <>\n                        <Icon className={`h-3 w-3 ${trend.color}`} />\n                        <span className={trend.color}>\n                          {trend.value}% vs previous period\n                        </span>\n                      </>\n                    )\n                  })()}\n                </div>\n              </div>\n              <FileText className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Total Downloads</p>\n                <p className=\"text-2xl font-bold\">{analytics.totalDownloads.toLocaleString()}</p>\n                <div className=\"flex items-center gap-1 text-xs\">\n                  {(() => {\n                    const trend = formatTrend(downloadsTrend)\n                    const Icon = trend.icon\n                    return (\n                      <>\n                        <Icon className={`h-3 w-3 ${trend.color}`} />\n                        <span className={trend.color}>\n                          {trend.value}% vs previous period\n                        </span>\n                      </>\n                    )\n                  })()}\n                </div>\n              </div>\n              <Download className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Active Users</p>\n                <p className=\"text-2xl font-bold\">{analytics.uniqueUsers}</p>\n                <div className=\"flex items-center gap-1 text-xs\">\n                  {(() => {\n                    const trend = formatTrend(usersTrend)\n                    const Icon = trend.icon\n                    return (\n                      <>\n                        <Icon className={`h-3 w-3 ${trend.color}`} />\n                        <span className={trend.color}>\n                          {trend.value}% vs previous period\n                        </span>\n                      </>\n                    )\n                  })()}\n                </div>\n              </div>\n              <Users className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-muted-foreground\">Avg Generation Time</p>\n                <p className=\"text-2xl font-bold\">{analytics.averageGenerationTime}s</p>\n                <div className=\"flex items-center gap-1 text-xs text-green-600\">\n                  <TrendingDown className=\"h-3 w-3\" />\n                  <span>12% faster</span>\n                </div>\n              </div>\n              <Clock className=\"h-8 w-8 text-muted-foreground\" />\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Report Type Distribution */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <Card>\n          <CardHeader>\n            <CardTitle>Report Type Distribution</CardTitle>\n            <CardDescription>\n              Breakdown of reports by type for {getTimeRangeLabel().toLowerCase()}\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {analytics.reportTypeDistribution.map((item) => {\n                const percentage = analytics.totalReports > 0 \n                  ? (item.count / analytics.totalReports) * 100 \n                  : 0\n                \n                return (\n                  <div key={item.type} className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className={`w-3 h-3 rounded-full ${\n                        item.type === 'SF2' ? 'bg-blue-500' :\n                        item.type === 'SF4' ? 'bg-green-500' :\n                        item.type === 'DAILY' ? 'bg-purple-500' :\n                        item.type === 'WEEKLY' ? 'bg-orange-500' :\n                        item.type === 'MONTHLY' ? 'bg-pink-500' :\n                        'bg-gray-500'\n                      }`} />\n                      <span className=\"text-sm font-medium\">{item.type}</span>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      <span className=\"text-sm text-muted-foreground\">\n                        {item.count.toLocaleString()}\n                      </span>\n                      <Badge variant=\"secondary\" className=\"text-xs\">\n                        {percentage.toFixed(1)}%\n                      </Badge>\n                    </div>\n                  </div>\n                )\n              })}\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Most Popular Reports</CardTitle>\n            <CardDescription>\n              Reports with the highest download counts\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {popularReports.map((report, index) => (\n                <div key={report.id} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"flex items-center justify-center w-6 h-6 rounded-full bg-muted text-xs font-medium\">\n                      {index + 1}\n                    </div>\n                    <div>\n                      <div className=\"text-sm font-medium\">{report.config.name}</div>\n                      <div className=\"text-xs text-muted-foreground\">\n                        {format(new Date(report.generatedAt), \"MMM dd, yyyy\")}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center gap-2\">\n                    <Download className=\"h-3 w-3 text-muted-foreground\" />\n                    <span className=\"text-sm\">{report.downloadCount}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Daily Activity Chart */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Daily Activity</CardTitle>\n          <CardDescription>\n            Report generation and download activity over time\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {/* Simple bar chart representation */}\n            <div className=\"grid grid-cols-7 gap-2\">\n              {analytics.dailyActivity.slice(-7).map((day, index) => {\n                const maxValue = Math.max(...analytics.dailyActivity.map(d => d.reports + d.downloads))\n                const totalActivity = day.reports + day.downloads\n                const height = maxValue > 0 ? (totalActivity / maxValue) * 100 : 0\n                \n                return (\n                  <div key={index} className=\"flex flex-col items-center gap-2\">\n                    <div className=\"w-full bg-muted rounded-t\" style={{ height: '100px' }}>\n                      <div \n                        className=\"w-full bg-primary rounded-t transition-all\"\n                        style={{ height: `${height}%`, marginTop: `${100 - height}%` }}\n                      />\n                    </div>\n                    <div className=\"text-xs text-center\">\n                      <div className=\"font-medium\">{format(new Date(day.date), \"MMM dd\")}</div>\n                      <div className=\"text-muted-foreground\">{totalActivity}</div>\n                    </div>\n                  </div>\n                )\n              })}\n            </div>\n            \n            <div className=\"flex items-center justify-center gap-6 text-sm\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-3 h-3 bg-primary rounded\" />\n                <span>Total Activity</span>\n              </div>\n              <div className=\"text-muted-foreground\">\n                Last 7 days activity\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Performance Insights */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Performance Insights</CardTitle>\n          <CardDescription>\n            Key insights and recommendations\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div className=\"space-y-4\">\n              <h4 className=\"font-medium\">Key Insights</h4>\n              <div className=\"space-y-3\">\n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\" />\n                  <div>\n                    <p className=\"text-sm font-medium\">SF2 reports are most popular</p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {analytics.reportTypeDistribution.find(r => r.type === 'SF2')?.count || 0} generated this period\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\" />\n                  <div>\n                    <p className=\"text-sm font-medium\">Peak usage on weekdays</p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      Monday-Friday show highest activity\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start gap-3\">\n                  <div className=\"w-2 h-2 bg-orange-500 rounded-full mt-2\" />\n                  <div>\n                    <p className=\"text-sm font-medium\">Generation time improved</p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      12% faster than previous period\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"space-y-4\">\n              <h4 className=\"font-medium\">Recommendations</h4>\n              <div className=\"space-y-3\">\n                <div className=\"p-3 bg-blue-50 rounded-lg\">\n                  <p className=\"text-sm font-medium text-blue-800\">Optimize SF2 templates</p>\n                  <p className=\"text-xs text-blue-600\">\n                    Consider creating more SF2 template variations\n                  </p>\n                </div>\n                \n                <div className=\"p-3 bg-green-50 rounded-lg\">\n                  <p className=\"text-sm font-medium text-green-800\">Schedule bulk reports</p>\n                  <p className=\"text-xs text-green-600\">\n                    Use bulk generation for recurring reports\n                  </p>\n                </div>\n                \n                <div className=\"p-3 bg-purple-50 rounded-lg\">\n                  <p className=\"text-sm font-medium text-purple-800\">Archive old reports</p>\n                  <p className=\"text-xs text-purple-600\">\n                    Set up automatic archival for reports older than 90 days\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;;;AApBA;;;;;;;;AA0BO,SAAS,gBAAgB,KAAmC;QAAnC,EAAE,SAAS,EAAwB,GAAnC;QAiVT;;IAhVrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAExE,0CAA0C;IAC1C,MAAM,mBAAmB;QACvB,MAAM,WAAW,yIAAA,CAAA,sBAAmB;QAEpC,8CAA8C;QAC9C,MAAM,cAAc;YAClB,MAAM;YACN,OAAO;YACP,OAAO;YACP,MAAM;QACR;QAEA,MAAM,aAAa,WAAW,CAAC,UAAU;QAEzC,OAAO;YACL,GAAG,QAAQ;YACX,cAAc,KAAK,KAAK,CAAC,SAAS,YAAY,GAAG;YACjD,gBAAgB,KAAK,KAAK,CAAC,SAAS,cAAc,GAAG;YACrD,aAAa,KAAK,KAAK,CAAC,SAAS,WAAW,GAAG,aAAa;YAC5D,uBAAuB,SAAS,qBAAqB;YACrD,wBAAwB,SAAS,sBAAsB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACnE,GAAG,IAAI;oBACP,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,GAAG;gBACjC,CAAC;YACD,eAAe,SAAS,aAAa,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACjD,GAAG,IAAI;oBACP,SAAS,KAAK,KAAK,CAAC,KAAK,OAAO,GAAG,aAAa;oBAChD,WAAW,KAAK,KAAK,CAAC,KAAK,SAAS,GAAG,aAAa;gBACtD,CAAC;QACH;IACF;IAEA,MAAM,YAAY;IAElB,+BAA+B;IAC/B,MAAM,qBAAqB,CAAC,SAAiB;QAC3C,IAAI,aAAa,GAAG,OAAO;QAC3B,OAAO,AAAC,CAAC,UAAU,QAAQ,IAAI,WAAY;IAC7C;IAEA,MAAM,eAAe,mBAAmB,UAAU,YAAY,EAAE,UAAU,YAAY,GAAG;IACzF,MAAM,iBAAiB,mBAAmB,UAAU,cAAc,EAAE,UAAU,cAAc,GAAG;IAC/F,MAAM,aAAa,mBAAmB,UAAU,WAAW,EAAE,UAAU,WAAW,GAAG;IAErF,MAAM,cAAc,CAAC;QACnB,MAAM,aAAa,SAAS;QAC5B,OAAO;YACL,OAAO,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC;YAC/B;YACA,MAAM,aAAa,qNAAA,CAAA,aAAU,GAAG,yNAAA,CAAA,eAAY;YAC5C,OAAO,aAAa,mBAAmB;QACzC;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,SAAS;YACb,MAAM;YACN,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA,OAAO,MAAM,CAAC,UAAU;IAC1B;IAEA,uBAAuB;IACvB,MAAM,iBAAiB,yIAAA,CAAA,uBAAoB,CACxC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa,EAChD,KAAK,CAAC,GAAG;IAEZ,qBACE,6LAAC;QAAI,WAAW,AAAC,aAAsB,OAAV;;0BAE3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,qNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGnC,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC,8HAAA,CAAA,SAAM;wBAAC,OAAO;wBAAW,eAAe,CAAC,QAAU,aAAa;;0CAC/D,6LAAC,8HAAA,CAAA,gBAAa;gCAAC,WAAU;0CACvB,cAAA,6LAAC,8HAAA,CAAA,cAAW;;;;;;;;;;0CAEd,6LAAC,8HAAA,CAAA,gBAAa;;kDACZ,6LAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;kDACvB,6LAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,6LAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAM;;;;;;kDACxB,6LAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAsB,UAAU,YAAY,CAAC,cAAc;;;;;;0DACxE,6LAAC;gDAAI,WAAU;0DACZ,CAAC;oDACA,MAAM,QAAQ,YAAY;oDAC1B,MAAM,OAAO,MAAM,IAAI;oDACvB,qBACE;;0EACE,6LAAC;gEAAK,WAAW,AAAC,WAAsB,OAAZ,MAAM,KAAK;;;;;;0EACvC,6LAAC;gEAAK,WAAW,MAAM,KAAK;;oEACzB,MAAM,KAAK;oEAAC;;;;;;;;;gDAIrB,CAAC;;;;;;;;;;;;kDAGL,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAsB,UAAU,cAAc,CAAC,cAAc;;;;;;0DAC1E,6LAAC;gDAAI,WAAU;0DACZ,CAAC;oDACA,MAAM,QAAQ,YAAY;oDAC1B,MAAM,OAAO,MAAM,IAAI;oDACvB,qBACE;;0EACE,6LAAC;gEAAK,WAAW,AAAC,WAAsB,OAAZ,MAAM,KAAK;;;;;;0EACvC,6LAAC;gEAAK,WAAW,MAAM,KAAK;;oEACzB,MAAM,KAAK;oEAAC;;;;;;;;;gDAIrB,CAAC;;;;;;;;;;;;kDAGL,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAsB,UAAU,WAAW;;;;;;0DACxD,6LAAC;gDAAI,WAAU;0DACZ,CAAC;oDACA,MAAM,QAAQ,YAAY;oDAC1B,MAAM,OAAO,MAAM,IAAI;oDACvB,qBACE;;0EACE,6LAAC;gEAAK,WAAW,AAAC,WAAsB,OAAZ,MAAM,KAAK;;;;;;0EACvC,6LAAC;gEAAK,WAAW,MAAM,KAAK;;oEACzB,MAAM,KAAK;oEAAC;;;;;;;;;gDAIrB,CAAC;;;;;;;;;;;;kDAGL,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAKvB,6LAAC,4HAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,4HAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;;oDAAsB,UAAU,qBAAqB;oDAAC;;;;;;;0DACnE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAGV,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,4HAAA,CAAA,OAAI;;0CACH,6LAAC,4HAAA,CAAA,aAAU;;kDACT,6LAAC,4HAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,4HAAA,CAAA,kBAAe;;4CAAC;4CACmB,oBAAoB,WAAW;;;;;;;;;;;;;0CAGrE,6LAAC,4HAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,UAAU,sBAAsB,CAAC,GAAG,CAAC,CAAC;wCACrC,MAAM,aAAa,UAAU,YAAY,GAAG,IACxC,AAAC,KAAK,KAAK,GAAG,UAAU,YAAY,GAAI,MACxC;wCAEJ,qBACE,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,AAAC,wBAOhB,OANC,KAAK,IAAI,KAAK,QAAQ,gBACtB,KAAK,IAAI,KAAK,QAAQ,iBACtB,KAAK,IAAI,KAAK,UAAU,kBACxB,KAAK,IAAI,KAAK,WAAW,kBACzB,KAAK,IAAI,KAAK,YAAY,gBAC1B;;;;;;sEAEF,6LAAC;4DAAK,WAAU;sEAAuB,KAAK,IAAI;;;;;;;;;;;;8DAElD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,KAAK,KAAK,CAAC,cAAc;;;;;;sEAE5B,6LAAC,6HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;;gEAClC,WAAW,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;2CAjBnB,KAAK,IAAI;;;;;oCAsBvB;;;;;;;;;;;;;;;;;kCAKN,6LAAC,4HAAA,CAAA,OAAI;;0CACH,6LAAC,4HAAA,CAAA,aAAU;;kDACT,6LAAC,4HAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,4HAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,4HAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,6LAAC;4CAAoB,WAAU;;8DAC7B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,QAAQ;;;;;;sEAEX,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAuB,OAAO,MAAM,CAAC,IAAI;;;;;;8EACxD,6LAAC;oEAAI,WAAU;8EACZ,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,WAAW,GAAG;;;;;;;;;;;;;;;;;;8DAI5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAK,WAAU;sEAAW,OAAO,aAAa;;;;;;;;;;;;;2CAdzC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAwB7B,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;;0CACT,6LAAC,4HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,4HAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,UAAU,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK;wCAC3C,MAAM,WAAW,KAAK,GAAG,IAAI,UAAU,aAAa,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,GAAG,EAAE,SAAS;wCACrF,MAAM,gBAAgB,IAAI,OAAO,GAAG,IAAI,SAAS;wCACjD,MAAM,SAAS,WAAW,IAAI,AAAC,gBAAgB,WAAY,MAAM;wCAEjE,qBACE,6LAAC;4CAAgB,WAAU;;8DACzB,6LAAC;oDAAI,WAAU;oDAA4B,OAAO;wDAAE,QAAQ;oDAAQ;8DAClE,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,QAAQ,AAAC,GAAS,OAAP,QAAO;4DAAI,WAAW,AAAC,GAAe,OAAb,MAAM,QAAO;wDAAG;;;;;;;;;;;8DAGjE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAe,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,IAAI,IAAI,GAAG;;;;;;sEACzD,6LAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;;2CATlC;;;;;oCAad;;;;;;8CAGF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/C,6LAAC,4HAAA,CAAA,OAAI;;kCACH,6LAAC,4HAAA,CAAA,aAAU;;0CACT,6LAAC,4HAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,4HAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,4HAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAsB;;;;;;8EACnC,6LAAC;oEAAE,WAAU;;wEACV,EAAA,yCAAA,UAAU,sBAAsB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,oBAAtD,6DAAA,uCAA8D,KAAK,KAAI;wEAAE;;;;;;;;;;;;;;;;;;;8DAKhF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAsB;;;;;;8EACnC,6LAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;;;;;;;8DAMjD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAsB;;;;;;8EACnC,6LAAC;oEAAE,WAAU;8EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAc;;;;;;sDAC5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAoC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAKvC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAqC;;;;;;sEAClD,6LAAC;4DAAE,WAAU;sEAAyB;;;;;;;;;;;;8DAKxC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAsC;;;;;;sEACnD,6LAAC;4DAAE,WAAU;sEAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzD;GA1YgB;KAAA", "debugId": null}}]}