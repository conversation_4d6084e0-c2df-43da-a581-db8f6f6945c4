{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/sf4-report-preview.tsx"], "sourcesContent": ["\"use client\"\n\nimport { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ead<PERSON>, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Label } from \"@/components/ui/label\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { SF4Report, ExportFormat } from \"@/lib/types/reports\"\nimport { ReportExportManager } from \"@/lib/utils/export-utils\"\nimport { \n  TrendingUp, \n  Download, \n  Printer, \n  School,\n  Calendar,\n  Users,\n  UserPlus,\n  UserMinus,\n  UserX,\n  BarChart3,\n  CheckCircle,\n  XCircle\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\ninterface SF4ReportPreviewProps {\n  report: SF4Report\n  onDownload?: (format: ExportFormat) => void\n  onPrint?: () => void\n  className?: string\n}\n\nexport function SF4ReportPreview({\n  report,\n  onDownload,\n  onPrint,\n  className\n}: SF4ReportPreviewProps) {\n  const handleDownload = async (format: ExportFormat) => {\n    try {\n      const { blob, filename } = await ReportExportManager.exportReport(report, format)\n      ReportExportManager.downloadFile(blob, filename)\n      onDownload?.(format)\n    } catch (error) {\n      console.error('Export failed:', error)\n    }\n  }\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Header Actions */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"p-2 bg-green-100 rounded-lg\">\n            <TrendingUp className=\"h-6 w-6 text-green-600\" />\n          </div>\n          <div>\n            <h2 className=\"text-xl font-semibold\">SF4 Report Preview</h2>\n            <p className=\"text-sm text-muted-foreground\">\n              Monthly Learner's Movement - {report.month} {report.year}\n            </p>\n          </div>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button variant=\"outline\" onClick={() => handleDownload('EXCEL')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            Excel\n          </Button>\n          <Button variant=\"outline\" onClick={() => handleDownload('PDF')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            PDF\n          </Button>\n          <Button variant=\"outline\" onClick={() => handleDownload('CSV')}>\n            <Download className=\"mr-2 h-4 w-4\" />\n            CSV\n          </Button>\n          <Button onClick={onPrint}>\n            <Printer className=\"mr-2 h-4 w-4\" />\n            Print\n          </Button>\n        </div>\n      </div>\n\n      {/* SF4 Form Preview */}\n      <Card className=\"print:shadow-none print:border-none\">\n        <CardContent className=\"p-8 space-y-6\">\n          {/* Official Header */}\n          <div className=\"text-center space-y-2\">\n            <h1 className=\"text-lg font-bold\">Republic of the Philippines</h1>\n            <h2 className=\"text-base font-semibold\">Department of Education</h2>\n            <h3 className=\"text-base font-semibold\">{report.schoolInfo.region}</h3>\n            <h4 className=\"text-base font-semibold\">{report.schoolInfo.division}</h4>\n            <h5 className=\"text-base font-semibold\">{report.schoolInfo.schoolName}</h5>\n            <div className=\"pt-4\">\n              <h2 className=\"text-xl font-bold\">SCHOOL FORM 4 (SF4)</h2>\n              <h3 className=\"text-lg font-semibold\">MONTHLY REPORT ON LEARNER'S MOVEMENT</h3>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* School and Report Information */}\n          <div className=\"grid grid-cols-2 gap-8\">\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">School:</span>\n                <span className=\"underline\">{report.schoolInfo.schoolName}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">School ID:</span>\n                <span className=\"underline\">{report.schoolInfo.schoolId}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Division:</span>\n                <span className=\"underline\">{report.schoolInfo.division}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Region:</span>\n                <span className=\"underline\">{report.schoolInfo.region}</span>\n              </div>\n            </div>\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Grade & Section:</span>\n                <span className=\"underline\">Grade {report.grade} {report.section ? `- ${report.section}` : '(All Sections)'}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Month:</span>\n                <span className=\"underline\">{report.month}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">Year:</span>\n                <span className=\"underline\">{report.year}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"font-medium\">School Year:</span>\n                <span className=\"underline\">{report.schoolInfo.schoolYear}</span>\n              </div>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Enrollment Summary */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-center\">ENROLLMENT SUMMARY</h3>\n            \n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-blue-600\">{report.enrollment.beginningOfMonth}</div>\n                <div className=\"text-sm text-muted-foreground\">Beginning of Month</div>\n              </div>\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-green-600\">{report.enrollment.newAdmissions.length}</div>\n                <div className=\"text-sm text-muted-foreground\">New Admissions</div>\n              </div>\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-orange-600\">{report.enrollment.transfers.transferredOut.length}</div>\n                <div className=\"text-sm text-muted-foreground\">Transfers Out</div>\n              </div>\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-purple-600\">{report.enrollment.endOfMonth}</div>\n                <div className=\"text-sm text-muted-foreground\">End of Month</div>\n              </div>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Movement Details */}\n          <div className=\"space-y-6\">\n            <h3 className=\"text-lg font-semibold text-center\">LEARNER MOVEMENT DETAILS</h3>\n\n            {/* New Admissions */}\n            {report.enrollment.newAdmissions.length > 0 && (\n              <div className=\"space-y-3\">\n                <h4 className=\"font-medium text-green-600 flex items-center gap-2\">\n                  <UserPlus className=\"h-4 w-4\" />\n                  New Admissions ({report.enrollment.newAdmissions.length})\n                </h4>\n                <div className=\"border rounded-lg overflow-hidden\">\n                  <Table>\n                    <TableHeader>\n                      <TableRow className=\"bg-green-50\">\n                        <TableHead>Student Name</TableHead>\n                        <TableHead>Date of Admission</TableHead>\n                        <TableHead>Reason</TableHead>\n                      </TableRow>\n                    </TableHeader>\n                    <TableBody>\n                      {report.enrollment.newAdmissions.map((student, index) => (\n                        <TableRow key={index}>\n                          <TableCell>{student.name}</TableCell>\n                          <TableCell>{format(new Date(student.dateOfAction), \"MMM dd, yyyy\")}</TableCell>\n                          <TableCell>{student.reason}</TableCell>\n                        </TableRow>\n                      ))}\n                    </TableBody>\n                  </Table>\n                </div>\n              </div>\n            )}\n\n            {/* Transfers Out */}\n            {report.enrollment.transfers.transferredOut.length > 0 && (\n              <div className=\"space-y-3\">\n                <h4 className=\"font-medium text-orange-600 flex items-center gap-2\">\n                  <UserMinus className=\"h-4 w-4\" />\n                  Transfers Out ({report.enrollment.transfers.transferredOut.length})\n                </h4>\n                <div className=\"border rounded-lg overflow-hidden\">\n                  <Table>\n                    <TableHeader>\n                      <TableRow className=\"bg-orange-50\">\n                        <TableHead>Student Name</TableHead>\n                        <TableHead>Date of Transfer</TableHead>\n                        <TableHead>Destination</TableHead>\n                        <TableHead>Reason</TableHead>\n                      </TableRow>\n                    </TableHeader>\n                    <TableBody>\n                      {report.enrollment.transfers.transferredOut.map((student, index) => (\n                        <TableRow key={index}>\n                          <TableCell>{student.name}</TableCell>\n                          <TableCell>{format(new Date(student.dateOfAction), \"MMM dd, yyyy\")}</TableCell>\n                          <TableCell>{student.destination || 'Not specified'}</TableCell>\n                          <TableCell>{student.reason}</TableCell>\n                        </TableRow>\n                      ))}\n                    </TableBody>\n                  </Table>\n                </div>\n              </div>\n            )}\n\n            {/* Dropouts */}\n            {report.enrollment.dropouts.length > 0 && (\n              <div className=\"space-y-3\">\n                <h4 className=\"font-medium text-red-600 flex items-center gap-2\">\n                  <UserX className=\"h-4 w-4\" />\n                  Dropouts ({report.enrollment.dropouts.length})\n                </h4>\n                <div className=\"border rounded-lg overflow-hidden\">\n                  <Table>\n                    <TableHeader>\n                      <TableRow className=\"bg-red-50\">\n                        <TableHead>Student Name</TableHead>\n                        <TableHead>Date of Dropout</TableHead>\n                        <TableHead>Reason</TableHead>\n                      </TableRow>\n                    </TableHeader>\n                    <TableBody>\n                      {report.enrollment.dropouts.map((student, index) => (\n                        <TableRow key={index}>\n                          <TableCell>{student.name}</TableCell>\n                          <TableCell>{format(new Date(student.dateOfAction), \"MMM dd, yyyy\")}</TableCell>\n                          <TableCell>{student.reason}</TableCell>\n                        </TableRow>\n                      ))}\n                    </TableBody>\n                  </Table>\n                </div>\n              </div>\n            )}\n          </div>\n\n          <Separator />\n\n          {/* Attendance Statistics */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-center\">ATTENDANCE STATISTICS</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-blue-600\">{report.attendance.totalSchoolDays}</div>\n                <div className=\"text-sm text-muted-foreground\">Total School Days</div>\n              </div>\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-green-600\">{report.attendance.averageAttendance}</div>\n                <div className=\"text-sm text-muted-foreground\">Average Daily Attendance</div>\n              </div>\n              <div className=\"text-center p-4 border rounded-lg\">\n                <div className=\"text-2xl font-bold text-purple-600\">{report.attendance.attendanceRate.toFixed(1)}%</div>\n                <div className=\"text-sm text-muted-foreground\">Attendance Rate</div>\n              </div>\n            </div>\n\n            {/* Attendance Lists */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Perfect Attendance */}\n              <div className=\"space-y-2\">\n                <h4 className=\"font-medium text-green-600 flex items-center gap-2\">\n                  <CheckCircle className=\"h-4 w-4\" />\n                  Perfect Attendance ({report.attendance.perfectAttendance.length})\n                </h4>\n                <div className=\"p-3 bg-green-50 rounded-lg\">\n                  {report.attendance.perfectAttendance.length > 0 ? (\n                    <ul className=\"text-sm space-y-1\">\n                      {report.attendance.perfectAttendance.slice(0, 5).map((studentId, index) => (\n                        <li key={index}>• Student ID: {studentId}</li>\n                      ))}\n                      {report.attendance.perfectAttendance.length > 5 && (\n                        <li className=\"text-muted-foreground\">\n                          ... and {report.attendance.perfectAttendance.length - 5} more\n                        </li>\n                      )}\n                    </ul>\n                  ) : (\n                    <p className=\"text-sm text-muted-foreground\">No students with perfect attendance</p>\n                  )}\n                </div>\n              </div>\n\n              {/* Chronic Absentees */}\n              <div className=\"space-y-2\">\n                <h4 className=\"font-medium text-red-600 flex items-center gap-2\">\n                  <XCircle className=\"h-4 w-4\" />\n                  Chronic Absentees ({report.attendance.chronicAbsentees.length})\n                </h4>\n                <div className=\"p-3 bg-red-50 rounded-lg\">\n                  {report.attendance.chronicAbsentees.length > 0 ? (\n                    <ul className=\"text-sm space-y-1\">\n                      {report.attendance.chronicAbsentees.slice(0, 5).map((studentId, index) => (\n                        <li key={index}>• Student ID: {studentId}</li>\n                      ))}\n                      {report.attendance.chronicAbsentees.length > 5 && (\n                        <li className=\"text-muted-foreground\">\n                          ... and {report.attendance.chronicAbsentees.length - 5} more\n                        </li>\n                      )}\n                    </ul>\n                  ) : (\n                    <p className=\"text-sm text-muted-foreground\">No chronic absentees identified</p>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Principal Review Section */}\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-center\">PRINCIPAL'S REVIEW</h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-4\">\n                <div>\n                  <Label className=\"text-sm font-medium\">Reviewed By:</Label>\n                  <p className=\"text-sm\">{report.principalReview.reviewedBy}</p>\n                </div>\n                <div>\n                  <Label className=\"text-sm font-medium\">Review Date:</Label>\n                  <p className=\"text-sm\">\n                    {report.principalReview.reviewDate ? \n                      format(new Date(report.principalReview.reviewDate), \"MMMM dd, yyyy\") : \n                      'Not reviewed'\n                    }\n                  </p>\n                </div>\n                <div>\n                  <Label className=\"text-sm font-medium\">Status:</Label>\n                  <Badge \n                    variant={report.principalReview.approved ? \"default\" : \"secondary\"}\n                    className=\"ml-2\"\n                  >\n                    {report.principalReview.approved ? \"Approved\" : \"Pending Review\"}\n                  </Badge>\n                </div>\n              </div>\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium\">Remarks:</Label>\n                <div className=\"p-3 bg-gray-50 rounded-lg min-h-[100px]\">\n                  <p className=\"text-sm\">\n                    {report.principalReview.remarks || \"No remarks provided\"}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <Separator />\n\n          {/* Signature Section */}\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-2 gap-8\">\n              <div className=\"space-y-4\">\n                <div className=\"text-center\">\n                  <div className=\"border-b border-black w-48 mx-auto mb-2 h-12\"></div>\n                  <div className=\"text-sm font-medium\">Prepared by</div>\n                  <div className=\"text-xs text-muted-foreground\">Class Adviser/Teacher</div>\n                </div>\n              </div>\n              <div className=\"space-y-4\">\n                <div className=\"text-center\">\n                  <div className=\"border-b border-black w-48 mx-auto mb-2 h-12\"></div>\n                  <div className=\"text-sm font-medium\">Date</div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"border-b border-black w-64 mx-auto mb-2 h-12\"></div>\n              <div className=\"text-sm font-medium\">Principal's Signature</div>\n              <div className=\"text-xs text-muted-foreground\">{report.schoolInfo.principalName}</div>\n            </div>\n          </div>\n\n          {/* Footer */}\n          <div className=\"text-center text-xs text-muted-foreground pt-4 border-t\">\n            <p>SF4 - Monthly Report on Learner's Movement</p>\n            <p>Generated on {format(new Date(report.generatedAt), \"MMMM dd, yyyy 'at' HH:mm\")}</p>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAxBA;;;;;;;;;;;AAiCO,SAAS,iBAAiB,EAC/B,MAAM,EACN,UAAU,EACV,OAAO,EACP,SAAS,EACa;IACtB,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,MAAM,+HAAA,CAAA,sBAAmB,CAAC,YAAY,CAAC,QAAQ;YAC1E,+HAAA,CAAA,sBAAmB,CAAC,YAAY,CAAC,MAAM;YACvC,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IACA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;0CAExB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAE,WAAU;;4CAAgC;4CACb,OAAO,KAAK;4CAAC;4CAAE,OAAO,IAAI;;;;;;;;;;;;;;;;;;;kCAI9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,eAAe;;kDACtD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoB;;;;;;8CAClC,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,8OAAC;oCAAG,WAAU;8CAA2B,OAAO,UAAU,CAAC,MAAM;;;;;;8CACjE,8OAAC;oCAAG,WAAU;8CAA2B,OAAO,UAAU,CAAC,QAAQ;;;;;;8CACnE,8OAAC;oCAAG,WAAU;8CAA2B,OAAO,UAAU,CAAC,UAAU;;;;;;8CACrE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoB;;;;;;sDAClC,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAI1C,8OAAC,8HAAA,CAAA,YAAS;;;;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,UAAU;;;;;;;;;;;;sDAE3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,QAAQ;;;;;;;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,QAAQ;;;;;;;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,MAAM;;;;;;;;;;;;;;;;;;8CAGzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;;wDAAY;wDAAO,OAAO,KAAK;wDAAC;wDAAE,OAAO,OAAO,GAAG,CAAC,EAAE,EAAE,OAAO,OAAO,EAAE,GAAG;;;;;;;;;;;;;sDAE7F,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,KAAK;;;;;;;;;;;;sDAE3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,IAAI;;;;;;;;;;;;sDAE1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAc;;;;;;8DAC9B,8OAAC;oDAAK,WAAU;8DAAa,OAAO,UAAU,CAAC,UAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAK/D,8OAAC,8HAAA,CAAA,YAAS;;;;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC,OAAO,UAAU,CAAC,gBAAgB;;;;;;8DACrF,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqC,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM;;;;;;8DAC1F,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAsC,OAAO,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM;;;;;;8DACtG,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAsC,OAAO,UAAU,CAAC,UAAU;;;;;;8DACjF,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;sCAKrD,8OAAC,8HAAA,CAAA,YAAS;;;;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;gCAGjD,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,GAAG,mBACxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAY;gDACf,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM;gDAAC;;;;;;;sDAE1D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;kEACJ,8OAAC,0HAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;4DAAC,WAAU;;8EAClB,8OAAC,0HAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,0HAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,0HAAA,CAAA,YAAS;8EAAC;;;;;;;;;;;;;;;;;kEAGf,8OAAC,0HAAA,CAAA,YAAS;kEACP,OAAO,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7C,8OAAC,0HAAA,CAAA,WAAQ;;kFACP,8OAAC,0HAAA,CAAA,YAAS;kFAAE,QAAQ,IAAI;;;;;;kFACxB,8OAAC,0HAAA,CAAA,YAAS;kFAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,YAAY,GAAG;;;;;;kFACnD,8OAAC,0HAAA,CAAA,YAAS;kFAAE,QAAQ,MAAM;;;;;;;+DAHb;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAa1B,OAAO,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,GAAG,mBACnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;gDACjB,OAAO,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM;gDAAC;;;;;;;sDAEpE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;kEACJ,8OAAC,0HAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;4DAAC,WAAU;;8EAClB,8OAAC,0HAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,0HAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,0HAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,0HAAA,CAAA,YAAS;8EAAC;;;;;;;;;;;;;;;;;kEAGf,8OAAC,0HAAA,CAAA,YAAS;kEACP,OAAO,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,SAAS,sBACxD,8OAAC,0HAAA,CAAA,WAAQ;;kFACP,8OAAC,0HAAA,CAAA,YAAS;kFAAE,QAAQ,IAAI;;;;;;kFACxB,8OAAC,0HAAA,CAAA,YAAS;kFAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,YAAY,GAAG;;;;;;kFACnD,8OAAC,0HAAA,CAAA,YAAS;kFAAE,QAAQ,WAAW,IAAI;;;;;;kFACnC,8OAAC,0HAAA,CAAA,YAAS;kFAAE,QAAQ,MAAM;;;;;;;+DAJb;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAc1B,OAAO,UAAU,CAAC,QAAQ,CAAC,MAAM,GAAG,mBACnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,wMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAY;gDAClB,OAAO,UAAU,CAAC,QAAQ,CAAC,MAAM;gDAAC;;;;;;;sDAE/C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;kEACJ,8OAAC,0HAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;4DAAC,WAAU;;8EAClB,8OAAC,0HAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,0HAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,0HAAA,CAAA,YAAS;8EAAC;;;;;;;;;;;;;;;;;kEAGf,8OAAC,0HAAA,CAAA,YAAS;kEACP,OAAO,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACxC,8OAAC,0HAAA,CAAA,WAAQ;;kFACP,8OAAC,0HAAA,CAAA,YAAS;kFAAE,QAAQ,IAAI;;;;;;kFACxB,8OAAC,0HAAA,CAAA,YAAS;kFAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,YAAY,GAAG;;;;;;kFACnD,8OAAC,0HAAA,CAAA,YAAS;kFAAE,QAAQ,MAAM;;;;;;;+DAHb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAa7B,8OAAC,8HAAA,CAAA,YAAS;;;;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC,OAAO,UAAU,CAAC,eAAe;;;;;;8DACpF,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqC,OAAO,UAAU,CAAC,iBAAiB;;;;;;8DACvF,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDAAsC,OAAO,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC;wDAAG;;;;;;;8DACjG,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;8CAKnD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAAY;wDACd,OAAO,UAAU,CAAC,iBAAiB,CAAC,MAAM;wDAAC;;;;;;;8DAElE,8OAAC;oDAAI,WAAU;8DACZ,OAAO,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,kBAC5C,8OAAC;wDAAG,WAAU;;4DACX,OAAO,UAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,WAAW,sBAC/D,8OAAC;;wEAAe;wEAAe;;mEAAtB;;;;;4DAEV,OAAO,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG,mBAC5C,8OAAC;gEAAG,WAAU;;oEAAwB;oEAC3B,OAAO,UAAU,CAAC,iBAAiB,CAAC,MAAM,GAAG;oEAAE;;;;;;;;;;;;6EAK9D,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;sDAMnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,4MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;wDAAY;wDACX,OAAO,UAAU,CAAC,gBAAgB,CAAC,MAAM;wDAAC;;;;;;;8DAEhE,8OAAC;oDAAI,WAAU;8DACZ,OAAO,UAAU,CAAC,gBAAgB,CAAC,MAAM,GAAG,kBAC3C,8OAAC;wDAAG,WAAU;;4DACX,OAAO,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,WAAW,sBAC9D,8OAAC;;wEAAe;wEAAe;;mEAAtB;;;;;4DAEV,OAAO,UAAU,CAAC,gBAAgB,CAAC,MAAM,GAAG,mBAC3C,8OAAC;gEAAG,WAAU;;oEAAwB;oEAC3B,OAAO,UAAU,CAAC,gBAAgB,CAAC,MAAM,GAAG;oEAAE;;;;;;;;;;;;6EAK7D,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOvD,8OAAC,8HAAA,CAAA,YAAS;;;;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC,0HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAsB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEAAW,OAAO,eAAe,CAAC,UAAU;;;;;;;;;;;;8DAE3D,8OAAC;;sEACC,8OAAC,0HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAsB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEACV,OAAO,eAAe,CAAC,UAAU,GAChC,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,eAAe,CAAC,UAAU,GAAG,mBACpD;;;;;;;;;;;;8DAIN,8OAAC;;sEACC,8OAAC,0HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAsB;;;;;;sEACvC,8OAAC,0HAAA,CAAA,QAAK;4DACJ,SAAS,OAAO,eAAe,CAAC,QAAQ,GAAG,YAAY;4DACvD,WAAU;sEAET,OAAO,eAAe,CAAC,QAAQ,GAAG,aAAa;;;;;;;;;;;;;;;;;;sDAItD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0HAAA,CAAA,QAAK;oDAAC,WAAU;8DAAsB;;;;;;8DACvC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEACV,OAAO,eAAe,CAAC,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7C,8OAAC,8HAAA,CAAA,YAAS;;;;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEAAsB;;;;;;kEACrC,8OAAC;wDAAI,WAAU;kEAAgC;;;;;;;;;;;;;;;;;sDAGnD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;8CAK3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;sDAAsB;;;;;;sDACrC,8OAAC;4CAAI,WAAU;sDAAiC,OAAO,UAAU,CAAC,aAAa;;;;;;;;;;;;;;;;;;sCAKnF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;;wCAAE;wCAAc,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlE", "debugId": null}}]}