{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/data/reports-mock-data.ts"], "sourcesContent": ["import { \n  GeneratedReport, \n  ReportTemplate, \n  ReportSchedule, \n  SchoolInfo, \n  SF2Report, \n  SF4Report,\n  ReportAnalytics,\n  TeacherInfo\n} from '@/lib/types/reports'\n\n// School information for Tanauan School of Arts and Trade\nexport const schoolInfo: SchoolInfo = {\n  schoolId: \"301216\",\n  schoolName: \"Tanauan School of Arts and Trade\",\n  address: \"Tanauan City, Batangas, Philippines\",\n  district: \"Tanauan City District\",\n  division: \"Schools Division of Batangas\",\n  region: \"Region IV-A (CALABARZON)\",\n  principalName: \"Dr. <PERSON>\",\n  schoolYear: \"2024-2025\",\n  semester: \"1st Semester\",\n  quarter: \"2nd Quarter\"\n}\n\n// Mock teachers data\nexport const mockTeachers: TeacherInfo[] = [\n  {\n    id: \"TCH001\",\n    name: \"Prof. <PERSON>\",\n    position: \"Subject Teacher - Mathematics\",\n  },\n  {\n    id: \"TCH002\", \n    name: \"Mrs. <PERSON>\",\n    position: \"Subject Teacher - English\",\n  },\n  {\n    id: \"TCH003\",\n    name: \"Mr. <PERSON>\",\n    position: \"Subject Teacher - Science\",\n  },\n  {\n    id: \"TCH004\",\n    name: \"Ms. Princess <PERSON>\",\n    position: \"Subject Teacher - Filipino\",\n  }\n]\n\n// Mock generated reports\nexport const mockGeneratedReports: GeneratedReport[] = [\n  {\n    id: \"RPT001\",\n    config: {\n      id: \"CFG001\",\n      name: \"Daily Attendance Report - Grade 7A\",\n      type: \"SF2\",\n      description: \"SF2 Daily Attendance Report for Grade 7-A\",\n      dateRange: {\n        startDate: \"2025-01-02\",\n        endDate: \"2025-01-02\"\n      },\n      filters: {\n        grades: [\"7\"],\n        sections: [\"Grade 7-A\"]\n      },\n      settings: {\n        includePhotos: false,\n        includeSignatures: true,\n        pageOrientation: \"portrait\",\n        fontSize: \"medium\"\n      },\n      createdBy: \"admin\",\n      createdAt: \"2025-01-02T08:00:00Z\",\n      lastModified: \"2025-01-02T08:00:00Z\"\n    },\n    status: \"READY\",\n    generatedAt: \"2025-01-02T08:15:00Z\",\n    fileSize: 245760,\n    downloadCount: 12,\n    metadata: {\n      totalStudents: 35,\n      totalRecords: 35,\n      dateRange: {\n        startDate: \"2025-01-02\",\n        endDate: \"2025-01-02\"\n      },\n      statistics: {\n        presentCount: 32,\n        lateCount: 2,\n        absentCount: 1,\n        attendanceRate: 94.3\n      },\n      schoolInfo\n    }\n  },\n  {\n    id: \"RPT002\",\n    config: {\n      id: \"CFG002\",\n      name: \"Monthly Learner's Movement - December 2024\",\n      type: \"SF4\",\n      description: \"SF4 Monthly Learner's Movement Report for December 2024\",\n      dateRange: {\n        startDate: \"2024-12-01\",\n        endDate: \"2024-12-31\"\n      },\n      filters: {\n        grades: [\"7\", \"8\", \"9\", \"10\", \"11\", \"12\"]\n      },\n      settings: {\n        includeSignatures: true,\n        pageOrientation: \"landscape\",\n        fontSize: \"small\"\n      },\n      createdBy: \"principal\",\n      createdAt: \"2025-01-01T09:00:00Z\",\n      lastModified: \"2025-01-01T09:00:00Z\"\n    },\n    status: \"READY\",\n    generatedAt: \"2025-01-01T09:30:00Z\",\n    fileSize: 512000,\n    downloadCount: 8,\n    metadata: {\n      totalStudents: 1234,\n      totalRecords: 24680,\n      dateRange: {\n        startDate: \"2024-12-01\",\n        endDate: \"2024-12-31\"\n      },\n      statistics: {\n        presentCount: 22145,\n        lateCount: 1235,\n        absentCount: 1300,\n        attendanceRate: 89.7\n      },\n      schoolInfo\n    }\n  },\n  {\n    id: \"RPT003\",\n    config: {\n      id: \"CFG003\",\n      name: \"Weekly Attendance Summary\",\n      type: \"WEEKLY\",\n      description: \"Weekly attendance summary for all grades\",\n      dateRange: {\n        startDate: \"2024-12-30\",\n        endDate: \"2025-01-03\"\n      },\n      filters: {},\n      settings: {\n        showStatistics: true,\n        groupBy: \"grade\",\n        pageOrientation: \"landscape\"\n      },\n      createdBy: \"admin\",\n      createdAt: \"2025-01-03T16:00:00Z\",\n      lastModified: \"2025-01-03T16:00:00Z\"\n    },\n    status: \"GENERATING\",\n    generatedAt: \"2025-01-03T16:15:00Z\",\n    downloadCount: 0,\n    metadata: {\n      totalStudents: 1234,\n      totalRecords: 6170,\n      dateRange: {\n        startDate: \"2024-12-30\",\n        endDate: \"2025-01-03\"\n      },\n      statistics: {\n        presentCount: 5540,\n        lateCount: 310,\n        absentCount: 320,\n        attendanceRate: 89.8\n      },\n      schoolInfo\n    }\n  }\n]\n\n// Mock report templates\nexport const mockReportTemplates: ReportTemplate[] = [\n  {\n    id: \"TPL001\",\n    name: \"SF2 Daily Attendance\",\n    type: \"SF2\",\n    description: \"Standard SF2 Daily Attendance Report template\",\n    config: {\n      type: \"SF2\",\n      settings: {\n        includeSignatures: true,\n        pageOrientation: \"portrait\",\n        fontSize: \"medium\",\n        includeHeader: true,\n        includeFooter: true\n      }\n    },\n    isDefault: true,\n    isPublic: true,\n    createdBy: \"system\",\n    createdAt: \"2024-01-01T00:00:00Z\"\n  },\n  {\n    id: \"TPL002\",\n    name: \"SF4 Monthly Movement\",\n    type: \"SF4\",\n    description: \"Standard SF4 Monthly Learner's Movement Report template\",\n    config: {\n      type: \"SF4\",\n      settings: {\n        includeSignatures: true,\n        pageOrientation: \"landscape\",\n        fontSize: \"small\",\n        includeHeader: true,\n        includeFooter: true\n      }\n    },\n    isDefault: true,\n    isPublic: true,\n    createdBy: \"system\",\n    createdAt: \"2024-01-01T00:00:00Z\"\n  },\n  {\n    id: \"TPL003\",\n    name: \"Chronic Absenteeism Report\",\n    type: \"CUSTOM\",\n    description: \"Custom report for identifying students with chronic absenteeism\",\n    config: {\n      type: \"CUSTOM\",\n      filters: {\n        attendanceStatus: [\"Absent\"]\n      },\n      settings: {\n        showStatistics: true,\n        groupBy: \"grade\",\n        sortBy: \"attendance\"\n      }\n    },\n    isDefault: false,\n    isPublic: true,\n    createdBy: \"admin\",\n    createdAt: \"2024-06-15T10:00:00Z\"\n  }\n]\n\n// Mock scheduled reports\nexport const mockScheduledReports: ReportSchedule[] = [\n  {\n    id: \"SCH001\",\n    reportConfigId: \"CFG001\",\n    name: \"Daily SF2 Reports - All Grades\",\n    frequency: \"daily\",\n    time: \"07:00\",\n    timezone: \"Asia/Manila\",\n    isActive: true,\n    nextRun: \"2025-01-03T07:00:00Z\",\n    recipients: [\"<EMAIL>\", \"<EMAIL>\"],\n    createdBy: \"admin\",\n    createdAt: \"2024-08-01T00:00:00Z\"\n  },\n  {\n    id: \"SCH002\",\n    reportConfigId: \"CFG002\",\n    name: \"Monthly SF4 Reports\",\n    frequency: \"monthly\",\n    dayOfMonth: 1,\n    time: \"09:00\",\n    timezone: \"Asia/Manila\",\n    isActive: true,\n    lastRun: \"2025-01-01T09:00:00Z\",\n    nextRun: \"2025-02-01T09:00:00Z\",\n    recipients: [\"<EMAIL>\", \"<EMAIL>\"],\n    createdBy: \"principal\",\n    createdAt: \"2024-08-01T00:00:00Z\"\n  }\n]\n\n// Mock report analytics\nexport const mockReportAnalytics: ReportAnalytics[] = [\n  {\n    reportId: \"RPT001\",\n    views: 45,\n    downloads: 12,\n    exports: {\n      PDF: 8,\n      EXCEL: 3,\n      CSV: 1,\n      PRINT: 0\n    },\n    lastAccessed: \"2025-01-02T14:30:00Z\",\n    popularFilters: {\n      \"grades\": 15,\n      \"sections\": 12,\n      \"dateRange\": 45\n    },\n    averageGenerationTime: 15.5\n  },\n  {\n    reportId: \"RPT002\",\n    views: 23,\n    downloads: 8,\n    exports: {\n      PDF: 6,\n      EXCEL: 2,\n      CSV: 0,\n      PRINT: 0\n    },\n    lastAccessed: \"2025-01-01T16:45:00Z\",\n    popularFilters: {\n      \"grades\": 8,\n      \"dateRange\": 23\n    },\n    averageGenerationTime: 45.2\n  }\n]\n\n// Helper functions\nexport function getReportById(id: string): GeneratedReport | undefined {\n  return mockGeneratedReports.find(report => report.id === id)\n}\n\nexport function getTemplateById(id: string): ReportTemplate | undefined {\n  return mockReportTemplates.find(template => template.id === id)\n}\n\nexport function getScheduleById(id: string): ReportSchedule | undefined {\n  return mockScheduledReports.find(schedule => schedule.id === id)\n}\n\nexport function getReportsByType(type: string): GeneratedReport[] {\n  return mockGeneratedReports.filter(report => report.config.type === type)\n}\n\nexport function getReportsByStatus(status: string): GeneratedReport[] {\n  return mockGeneratedReports.filter(report => report.status === status)\n}\n\nexport function getTeacherById(id: string): TeacherInfo | undefined {\n  return mockTeachers.find(teacher => teacher.id === id)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAYO,MAAM,aAAyB;IACpC,UAAU;IACV,YAAY;IACZ,SAAS;IACT,UAAU;IACV,UAAU;IACV,QAAQ;IACR,eAAe;IACf,YAAY;IACZ,UAAU;IACV,SAAS;AACX;AAGO,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;IACZ;CACD;AAGM,MAAM,uBAA0C;IACrD;QACE,IAAI;QACJ,QAAQ;YACN,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,WAAW;gBACT,WAAW;gBACX,SAAS;YACX;YACA,SAAS;gBACP,QAAQ;oBAAC;iBAAI;gBACb,UAAU;oBAAC;iBAAY;YACzB;YACA,UAAU;gBACR,eAAe;gBACf,mBAAmB;gBACnB,iBAAiB;gBACjB,UAAU;YACZ;YACA,WAAW;YACX,WAAW;YACX,cAAc;QAChB;QACA,QAAQ;QACR,aAAa;QACb,UAAU;QACV,eAAe;QACf,UAAU;YACR,eAAe;YACf,cAAc;YACd,WAAW;gBACT,WAAW;gBACX,SAAS;YACX;YACA,YAAY;gBACV,cAAc;gBACd,WAAW;gBACX,aAAa;gBACb,gBAAgB;YAClB;YACA;QACF;IACF;IACA;QACE,IAAI;QACJ,QAAQ;YACN,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,WAAW;gBACT,WAAW;gBACX,SAAS;YACX;YACA,SAAS;gBACP,QAAQ;oBAAC;oBAAK;oBAAK;oBAAK;oBAAM;oBAAM;iBAAK;YAC3C;YACA,UAAU;gBACR,mBAAmB;gBACnB,iBAAiB;gBACjB,UAAU;YACZ;YACA,WAAW;YACX,WAAW;YACX,cAAc;QAChB;QACA,QAAQ;QACR,aAAa;QACb,UAAU;QACV,eAAe;QACf,UAAU;YACR,eAAe;YACf,cAAc;YACd,WAAW;gBACT,WAAW;gBACX,SAAS;YACX;YACA,YAAY;gBACV,cAAc;gBACd,WAAW;gBACX,aAAa;gBACb,gBAAgB;YAClB;YACA;QACF;IACF;IACA;QACE,IAAI;QACJ,QAAQ;YACN,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,WAAW;gBACT,WAAW;gBACX,SAAS;YACX;YACA,SAAS,CAAC;YACV,UAAU;gBACR,gBAAgB;gBAChB,SAAS;gBACT,iBAAiB;YACnB;YACA,WAAW;YACX,WAAW;YACX,cAAc;QAChB;QACA,QAAQ;QACR,aAAa;QACb,eAAe;QACf,UAAU;YACR,eAAe;YACf,cAAc;YACd,WAAW;gBACT,WAAW;gBACX,SAAS;YACX;YACA,YAAY;gBACV,cAAc;gBACd,WAAW;gBACX,aAAa;gBACb,gBAAgB;YAClB;YACA;QACF;IACF;CACD;AAGM,MAAM,sBAAwC;IACnD;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,QAAQ;YACN,MAAM;YACN,UAAU;gBACR,mBAAmB;gBACnB,iBAAiB;gBACjB,UAAU;gBACV,eAAe;gBACf,eAAe;YACjB;QACF;QACA,WAAW;QACX,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,QAAQ;YACN,MAAM;YACN,UAAU;gBACR,mBAAmB;gBACnB,iBAAiB;gBACjB,UAAU;gBACV,eAAe;gBACf,eAAe;YACjB;QACF;QACA,WAAW;QACX,UAAU;QACV,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,QAAQ;YACN,MAAM;YACN,SAAS;gBACP,kBAAkB;oBAAC;iBAAS;YAC9B;YACA,UAAU;gBACR,gBAAgB;gBAChB,SAAS;gBACT,QAAQ;YACV;QACF;QACA,WAAW;QACX,UAAU;QACV,WAAW;QACX,WAAW;IACb;CACD;AAGM,MAAM,uBAAyC;IACpD;QACE,IAAI;QACJ,gBAAgB;QAChB,MAAM;QACN,WAAW;QACX,MAAM;QACN,UAAU;QACV,UAAU;QACV,SAAS;QACT,YAAY;YAAC;YAA4B;SAAuB;QAChE,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,gBAAgB;QAChB,MAAM;QACN,WAAW;QACX,YAAY;QACZ,MAAM;QACN,UAAU;QACV,UAAU;QACV,SAAS;QACT,SAAS;QACT,YAAY;YAAC;YAA4B;SAA8B;QACvE,WAAW;QACX,WAAW;IACb;CACD;AAGM,MAAM,sBAAyC;IACpD;QACE,UAAU;QACV,OAAO;QACP,WAAW;QACX,SAAS;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,OAAO;QACT;QACA,cAAc;QACd,gBAAgB;YACd,UAAU;YACV,YAAY;YACZ,aAAa;QACf;QACA,uBAAuB;IACzB;IACA;QACE,UAAU;QACV,OAAO;QACP,WAAW;QACX,SAAS;YACP,KAAK;YACL,OAAO;YACP,KAAK;YACL,OAAO;QACT;QACA,cAAc;QACd,gBAAgB;YACd,UAAU;YACV,aAAa;QACf;QACA,uBAAuB;IACzB;CACD;AAGM,SAAS,cAAc,EAAU;IACtC,OAAO,qBAAqB,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAC3D;AAEO,SAAS,gBAAgB,EAAU;IACxC,OAAO,oBAAoB,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;AAC9D;AAEO,SAAS,gBAAgB,EAAU;IACxC,OAAO,qBAAqB,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;AAC/D;AAEO,SAAS,iBAAiB,IAAY;IAC3C,OAAO,qBAAqB,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,IAAI,KAAK;AACtE;AAEO,SAAS,mBAAmB,MAAc;IAC/C,OAAO,qBAAqB,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,KAAK;AACjE;AAEO,SAAS,eAAe,EAAU;IACvC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/utils/report-utils.ts"], "sourcesContent": ["import { \n  ReportConfig, \n  ReportFilters, \n  SF2Report, \n  SF4Report, \n  ReportMetadata,\n  SF2StudentRecord,\n  SF2Summary,\n  SF4Enrollment,\n  SF4Movement,\n  SF4Attendance\n} from '@/lib/types/reports'\nimport { Student, DetailedAttendanceRecord } from '@/lib/types/scanner'\nimport { schoolInfo, mockTeachers } from '@/lib/data/reports-mock-data'\n\n// Date utility functions\nexport function formatDateForDepEd(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleDateString('en-PH', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function getSchoolYear(date: Date = new Date()): string {\n  const year = date.getFullYear()\n  const month = date.getMonth() + 1\n  \n  // School year starts in June (month 6)\n  if (month >= 6) {\n    return `${year}-${year + 1}`\n  } else {\n    return `${year - 1}-${year}`\n  }\n}\n\nexport function getQuarter(date: Date = new Date()): string {\n  const month = date.getMonth() + 1\n  \n  if (month >= 6 && month <= 8) return \"1st Quarter\"\n  if (month >= 9 && month <= 11) return \"2nd Quarter\"\n  if (month >= 12 || month <= 2) return \"3rd Quarter\"\n  return \"4th Quarter\"\n}\n\nexport function getSemester(date: Date = new Date()): string {\n  const month = date.getMonth() + 1\n  \n  if (month >= 6 && month <= 10) return \"1st Semester\"\n  return \"2nd Semester\"\n}\n\n// Filter utility functions\nexport function applyReportFilters(\n  students: Student[], \n  attendanceRecords: DetailedAttendanceRecord[], \n  filters: ReportFilters\n): { students: Student[], records: DetailedAttendanceRecord[] } {\n  let filteredStudents = [...students]\n  let filteredRecords = [...attendanceRecords]\n\n  // Filter by grades\n  if (filters.grades && filters.grades.length > 0) {\n    filteredStudents = filteredStudents.filter(student => \n      filters.grades!.includes(student.grade)\n    )\n  }\n\n  // Filter by sections\n  if (filters.sections && filters.sections.length > 0) {\n    filteredStudents = filteredStudents.filter(student => \n      student.section && filters.sections!.includes(student.section)\n    )\n  }\n\n  // Filter by courses\n  if (filters.courses && filters.courses.length > 0) {\n    filteredStudents = filteredStudents.filter(student => \n      filters.courses!.includes(student.course)\n    )\n  }\n\n  // Filter by specific students\n  if (filters.students && filters.students.length > 0) {\n    filteredStudents = filteredStudents.filter(student => \n      filters.students!.includes(student.id)\n    )\n  }\n\n  // Filter by attendance status\n  if (filters.attendanceStatus && filters.attendanceStatus.length > 0) {\n    filteredRecords = filteredRecords.filter(record => \n      filters.attendanceStatus!.includes(record.status)\n    )\n  }\n\n  // Filter by student status\n  if (!filters.includeTransferred) {\n    filteredStudents = filteredStudents.filter(student => \n      student.status !== 'Transferred'\n    )\n  }\n\n  if (!filters.includeInactive) {\n    filteredStudents = filteredStudents.filter(student => \n      student.status === 'Active'\n    )\n  }\n\n  // Filter records to match filtered students\n  const studentIds = new Set(filteredStudents.map(s => s.id))\n  filteredRecords = filteredRecords.filter(record => \n    studentIds.has(record.studentId)\n  )\n\n  return { students: filteredStudents, records: filteredRecords }\n}\n\n// Statistics calculation functions\nexport function calculateAttendanceStatistics(records: DetailedAttendanceRecord[]) {\n  const total = records.length\n  const present = records.filter(r => r.status === 'Present').length\n  const late = records.filter(r => r.status === 'Late').length\n  const absent = records.filter(r => r.status === 'Absent').length\n  \n  return {\n    totalRecords: total,\n    presentCount: present,\n    lateCount: late,\n    absentCount: absent,\n    attendanceRate: total > 0 ? ((present + late) / total) * 100 : 0\n  }\n}\n\nexport function calculateStudentAttendanceRate(\n  studentId: string, \n  records: DetailedAttendanceRecord[]\n): number {\n  const studentRecords = records.filter(r => r.studentId === studentId)\n  const total = studentRecords.length\n  const present = studentRecords.filter(r => r.status === 'Present' || r.status === 'Late').length\n  \n  return total > 0 ? (present / total) * 100 : 0\n}\n\n// SF2 Report generation functions\nexport function generateSF2Report(\n  config: ReportConfig,\n  students: Student[],\n  attendanceRecords: DetailedAttendanceRecord[]\n): SF2Report {\n  const { students: filteredStudents, records: filteredRecords } = \n    applyReportFilters(students, attendanceRecords, config.filters)\n\n  // Get the first teacher (in real implementation, this would be based on subject/section)\n  const teacher = mockTeachers[0]\n\n  // Create student records for SF2\n  const sf2Students: SF2StudentRecord[] = filteredStudents.map(student => {\n    const studentRecords = filteredRecords.filter(r => r.studentId === student.id)\n    const dailyRecord = studentRecords.find(r => r.date === config.dateRange.startDate)\n    \n    return {\n      studentId: student.id,\n      studentName: `${student.firstName} ${student.middleName || ''} ${student.lastName}`.trim(),\n      attendance: {\n        'Morning': dailyRecord?.status === 'Present' ? 'P' : \n                  dailyRecord?.status === 'Late' ? 'L' : \n                  dailyRecord?.status === 'Absent' ? 'A' : 'A'\n      },\n      dailyStatus: dailyRecord?.status === 'Present' ? 'P' : \n                  dailyRecord?.status === 'Late' ? 'L' : \n                  dailyRecord?.status === 'Absent' ? 'A' : 'A',\n      remarks: dailyRecord?.remarks\n    }\n  })\n\n  // Calculate summary\n  const summary: SF2Summary = {\n    totalStudents: sf2Students.length,\n    presentCount: sf2Students.filter(s => s.dailyStatus === 'P').length,\n    lateCount: sf2Students.filter(s => s.dailyStatus === 'L').length,\n    absentCount: sf2Students.filter(s => s.dailyStatus === 'A').length,\n    excusedCount: sf2Students.filter(s => s.dailyStatus === 'E').length,\n    attendanceRate: 0\n  }\n  \n  summary.attendanceRate = summary.totalStudents > 0 ? \n    ((summary.presentCount + summary.lateCount) / summary.totalStudents) * 100 : 0\n\n  return {\n    id: `SF2_${Date.now()}`,\n    date: config.dateRange.startDate,\n    grade: config.filters.grades?.[0] || 'All',\n    section: config.filters.sections?.[0] || 'All',\n    teacher,\n    students: sf2Students,\n    summary,\n    schoolInfo,\n    generatedAt: new Date().toISOString()\n  }\n}\n\n// SF4 Report generation functions\nexport function generateSF4Report(\n  config: ReportConfig,\n  students: Student[],\n  attendanceRecords: DetailedAttendanceRecord[]\n): SF4Report {\n  const { students: filteredStudents, records: filteredRecords } = \n    applyReportFilters(students, attendanceRecords, config.filters)\n\n  const startDate = new Date(config.dateRange.startDate)\n  const endDate = new Date(config.dateRange.endDate)\n  \n  // Mock enrollment data (in real implementation, this would come from enrollment records)\n  const enrollment: SF4Enrollment = {\n    beginningOfMonth: filteredStudents.length,\n    newAdmissions: [],\n    transfers: {\n      transferredIn: [],\n      transferredOut: filteredStudents.filter(s => s.status === 'Transferred').map(s => ({\n        studentId: s.id,\n        name: `${s.firstName} ${s.lastName}`,\n        dateOfAction: config.dateRange.endDate,\n        reason: 'Transfer to another school'\n      }))\n    },\n    dropouts: [],\n    endOfMonth: filteredStudents.filter(s => s.status === 'Active').length\n  }\n\n  // Calculate movement statistics\n  const movement: SF4Movement = {\n    totalEnrolled: enrollment.beginningOfMonth,\n    maleCount: filteredStudents.filter(s => s.gender === 'Male').length,\n    femaleCount: filteredStudents.filter(s => s.gender === 'Female').length,\n    newAdmissions: enrollment.newAdmissions.length,\n    transfersIn: enrollment.transfers.transferredIn.length,\n    transfersOut: enrollment.transfers.transferredOut.length,\n    dropouts: enrollment.dropouts.length\n  }\n\n  // Calculate attendance statistics\n  const stats = calculateAttendanceStatistics(filteredRecords)\n  const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))\n  \n  const attendance: SF4Attendance = {\n    totalSchoolDays: totalDays,\n    averageAttendance: stats.presentCount + stats.lateCount,\n    attendanceRate: stats.attendanceRate,\n    chronicAbsentees: filteredStudents\n      .filter(s => calculateStudentAttendanceRate(s.id, filteredRecords) < 80)\n      .map(s => s.id),\n    perfectAttendance: filteredStudents\n      .filter(s => calculateStudentAttendanceRate(s.id, filteredRecords) === 100)\n      .map(s => s.id)\n  }\n\n  return {\n    id: `SF4_${Date.now()}`,\n    month: startDate.toLocaleDateString('en-US', { month: 'long' }),\n    year: startDate.getFullYear().toString(),\n    grade: config.filters.grades?.[0] || 'All',\n    section: config.filters.sections?.[0],\n    enrollment,\n    movement,\n    attendance,\n    schoolInfo,\n    principalReview: {\n      reviewedBy: schoolInfo.principalName,\n      reviewDate: new Date().toISOString().split('T')[0],\n      approved: false\n    },\n    generatedAt: new Date().toISOString()\n  }\n}\n\n// Report metadata generation\nexport function generateReportMetadata(\n  config: ReportConfig,\n  students: Student[],\n  attendanceRecords: DetailedAttendanceRecord[]\n): ReportMetadata {\n  const { students: filteredStudents, records: filteredRecords } = \n    applyReportFilters(students, attendanceRecords, config.filters)\n\n  const stats = calculateAttendanceStatistics(filteredRecords)\n\n  return {\n    totalStudents: filteredStudents.length,\n    totalRecords: filteredRecords.length,\n    dateRange: config.dateRange,\n    statistics: {\n      presentCount: stats.presentCount,\n      lateCount: stats.lateCount,\n      absentCount: stats.absentCount,\n      attendanceRate: stats.attendanceRate\n    },\n    schoolInfo\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAaA;;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS;QAAc,OAAA,iEAAa,IAAI;IAC7C,MAAM,OAAO,KAAK,WAAW;IAC7B,MAAM,QAAQ,KAAK,QAAQ,KAAK;IAEhC,uCAAuC;IACvC,IAAI,SAAS,GAAG;QACd,OAAO,AAAC,GAAU,OAAR,MAAK,KAAY,OAAT,OAAO;IAC3B,OAAO;QACL,OAAO,AAAC,GAAc,OAAZ,OAAO,GAAE,KAAQ,OAAL;IACxB;AACF;AAEO,SAAS;QAAW,OAAA,iEAAa,IAAI;IAC1C,MAAM,QAAQ,KAAK,QAAQ,KAAK;IAEhC,IAAI,SAAS,KAAK,SAAS,GAAG,OAAO;IACrC,IAAI,SAAS,KAAK,SAAS,IAAI,OAAO;IACtC,IAAI,SAAS,MAAM,SAAS,GAAG,OAAO;IACtC,OAAO;AACT;AAEO,SAAS;QAAY,OAAA,iEAAa,IAAI;IAC3C,MAAM,QAAQ,KAAK,QAAQ,KAAK;IAEhC,IAAI,SAAS,KAAK,SAAS,IAAI,OAAO;IACtC,OAAO;AACT;AAGO,SAAS,mBACd,QAAmB,EACnB,iBAA6C,EAC7C,OAAsB;IAEtB,IAAI,mBAAmB;WAAI;KAAS;IACpC,IAAI,kBAAkB;WAAI;KAAkB;IAE5C,mBAAmB;IACnB,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;QAC/C,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,MAAM,CAAE,QAAQ,CAAC,QAAQ,KAAK;IAE1C;IAEA,qBAAqB;IACrB,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;QACnD,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,OAAO,IAAI,QAAQ,QAAQ,CAAE,QAAQ,CAAC,QAAQ,OAAO;IAEjE;IAEA,oBAAoB;IACpB,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG;QACjD,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,OAAO,CAAE,QAAQ,CAAC,QAAQ,MAAM;IAE5C;IAEA,8BAA8B;IAC9B,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;QACnD,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,QAAQ,CAAE,QAAQ,CAAC,QAAQ,EAAE;IAEzC;IAEA,8BAA8B;IAC9B,IAAI,QAAQ,gBAAgB,IAAI,QAAQ,gBAAgB,CAAC,MAAM,GAAG,GAAG;QACnE,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,SACvC,QAAQ,gBAAgB,CAAE,QAAQ,CAAC,OAAO,MAAM;IAEpD;IAEA,2BAA2B;IAC3B,IAAI,CAAC,QAAQ,kBAAkB,EAAE;QAC/B,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,MAAM,KAAK;IAEvB;IAEA,IAAI,CAAC,QAAQ,eAAe,EAAE;QAC5B,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,UACzC,QAAQ,MAAM,KAAK;IAEvB;IAEA,4CAA4C;IAC5C,MAAM,aAAa,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IACzD,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,SACvC,WAAW,GAAG,CAAC,OAAO,SAAS;IAGjC,OAAO;QAAE,UAAU;QAAkB,SAAS;IAAgB;AAChE;AAGO,SAAS,8BAA8B,OAAmC;IAC/E,MAAM,QAAQ,QAAQ,MAAM;IAC5B,MAAM,UAAU,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;IAClE,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;IAC5D,MAAM,SAAS,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;IAEhE,OAAO;QACL,cAAc;QACd,cAAc;QACd,WAAW;QACX,aAAa;QACb,gBAAgB,QAAQ,IAAI,AAAC,CAAC,UAAU,IAAI,IAAI,QAAS,MAAM;IACjE;AACF;AAEO,SAAS,+BACd,SAAiB,EACjB,OAAmC;IAEnC,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;IAC3D,MAAM,QAAQ,eAAe,MAAM;IACnC,MAAM,UAAU,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,EAAE,MAAM,KAAK,QAAQ,MAAM;IAEhG,OAAO,QAAQ,IAAI,AAAC,UAAU,QAAS,MAAM;AAC/C;AAGO,SAAS,kBACd,MAAoB,EACpB,QAAmB,EACnB,iBAA6C;QA4CpC,wBACE;IA3CX,MAAM,EAAE,UAAU,gBAAgB,EAAE,SAAS,eAAe,EAAE,GAC5D,mBAAmB,UAAU,mBAAmB,OAAO,OAAO;IAEhE,yFAAyF;IACzF,MAAM,UAAU,yIAAA,CAAA,eAAY,CAAC,EAAE;IAE/B,iCAAiC;IACjC,MAAM,cAAkC,iBAAiB,GAAG,CAAC,CAAA;QAC3D,MAAM,iBAAiB,gBAAgB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,QAAQ,EAAE;QAC7E,MAAM,cAAc,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,SAAS;QAElF,OAAO;YACL,WAAW,QAAQ,EAAE;YACrB,aAAa,AAAC,GAAuB,OAArB,QAAQ,SAAS,EAAC,KAA+B,OAA5B,QAAQ,UAAU,IAAI,IAAG,KAAoB,OAAjB,QAAQ,QAAQ,EAAG,IAAI;YACxF,YAAY;gBACV,WAAW,CAAA,wBAAA,kCAAA,YAAa,MAAM,MAAK,YAAY,MACrC,CAAA,wBAAA,kCAAA,YAAa,MAAM,MAAK,SAAS,MACjC,CAAA,wBAAA,kCAAA,YAAa,MAAM,MAAK,WAAW,MAAM;YACrD;YACA,aAAa,CAAA,wBAAA,kCAAA,YAAa,MAAM,MAAK,YAAY,MACrC,CAAA,wBAAA,kCAAA,YAAa,MAAM,MAAK,SAAS,MACjC,CAAA,wBAAA,kCAAA,YAAa,MAAM,MAAK,WAAW,MAAM;YACrD,OAAO,EAAE,wBAAA,kCAAA,YAAa,OAAO;QAC/B;IACF;IAEA,oBAAoB;IACpB,MAAM,UAAsB;QAC1B,eAAe,YAAY,MAAM;QACjC,cAAc,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,KAAK,MAAM;QACnE,WAAW,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,KAAK,MAAM;QAChE,aAAa,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,KAAK,MAAM;QAClE,cAAc,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,KAAK,MAAM;QACnE,gBAAgB;IAClB;IAEA,QAAQ,cAAc,GAAG,QAAQ,aAAa,GAAG,IAC/C,AAAC,CAAC,QAAQ,YAAY,GAAG,QAAQ,SAAS,IAAI,QAAQ,aAAa,GAAI,MAAM;IAE/E,OAAO;QACL,IAAI,AAAC,OAAiB,OAAX,KAAK,GAAG;QACnB,MAAM,OAAO,SAAS,CAAC,SAAS;QAChC,OAAO,EAAA,yBAAA,OAAO,OAAO,CAAC,MAAM,cAArB,6CAAA,sBAAuB,CAAC,EAAE,KAAI;QACrC,SAAS,EAAA,2BAAA,OAAO,OAAO,CAAC,QAAQ,cAAvB,+CAAA,wBAAyB,CAAC,EAAE,KAAI;QACzC;QACA,UAAU;QACV;QACA,YAAA,yIAAA,CAAA,aAAU;QACV,aAAa,IAAI,OAAO,WAAW;IACrC;AACF;AAGO,SAAS,kBACd,MAAoB,EACpB,QAAmB,EACnB,iBAA6C;QAwDpC,wBACE;IAvDX,MAAM,EAAE,UAAU,gBAAgB,EAAE,SAAS,eAAe,EAAE,GAC5D,mBAAmB,UAAU,mBAAmB,OAAO,OAAO;IAEhE,MAAM,YAAY,IAAI,KAAK,OAAO,SAAS,CAAC,SAAS;IACrD,MAAM,UAAU,IAAI,KAAK,OAAO,SAAS,CAAC,OAAO;IAEjD,yFAAyF;IACzF,MAAM,aAA4B;QAChC,kBAAkB,iBAAiB,MAAM;QACzC,eAAe,EAAE;QACjB,WAAW;YACT,eAAe,EAAE;YACjB,gBAAgB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,GAAG,CAAC,CAAA,IAAK,CAAC;oBACjF,WAAW,EAAE,EAAE;oBACf,MAAM,AAAC,GAAiB,OAAf,EAAE,SAAS,EAAC,KAAc,OAAX,EAAE,QAAQ;oBAClC,cAAc,OAAO,SAAS,CAAC,OAAO;oBACtC,QAAQ;gBACV,CAAC;QACH;QACA,UAAU,EAAE;QACZ,YAAY,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;IACxE;IAEA,gCAAgC;IAChC,MAAM,WAAwB;QAC5B,eAAe,WAAW,gBAAgB;QAC1C,WAAW,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;QACnE,aAAa,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;QACvE,eAAe,WAAW,aAAa,CAAC,MAAM;QAC9C,aAAa,WAAW,SAAS,CAAC,aAAa,CAAC,MAAM;QACtD,cAAc,WAAW,SAAS,CAAC,cAAc,CAAC,MAAM;QACxD,UAAU,WAAW,QAAQ,CAAC,MAAM;IACtC;IAEA,kCAAkC;IAClC,MAAM,QAAQ,8BAA8B;IAC5C,MAAM,YAAY,KAAK,IAAI,CAAC,CAAC,QAAQ,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;IAE5F,MAAM,aAA4B;QAChC,iBAAiB;QACjB,mBAAmB,MAAM,YAAY,GAAG,MAAM,SAAS;QACvD,gBAAgB,MAAM,cAAc;QACpC,kBAAkB,iBACf,MAAM,CAAC,CAAA,IAAK,+BAA+B,EAAE,EAAE,EAAE,mBAAmB,IACpE,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAChB,mBAAmB,iBAChB,MAAM,CAAC,CAAA,IAAK,+BAA+B,EAAE,EAAE,EAAE,qBAAqB,KACtE,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;IAClB;IAEA,OAAO;QACL,IAAI,AAAC,OAAiB,OAAX,KAAK,GAAG;QACnB,OAAO,UAAU,kBAAkB,CAAC,SAAS;YAAE,OAAO;QAAO;QAC7D,MAAM,UAAU,WAAW,GAAG,QAAQ;QACtC,OAAO,EAAA,yBAAA,OAAO,OAAO,CAAC,MAAM,cAArB,6CAAA,sBAAuB,CAAC,EAAE,KAAI;QACrC,OAAO,GAAE,2BAAA,OAAO,OAAO,CAAC,QAAQ,cAAvB,+CAAA,wBAAyB,CAAC,EAAE;QACrC;QACA;QACA;QACA,YAAA,yIAAA,CAAA,aAAU;QACV,iBAAiB;YACf,YAAY,yIAAA,CAAA,aAAU,CAAC,aAAa;YACpC,YAAY,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAClD,UAAU;QACZ;QACA,aAAa,IAAI,OAAO,WAAW;IACrC;AACF;AAGO,SAAS,uBACd,MAAoB,EACpB,QAAmB,EACnB,iBAA6C;IAE7C,MAAM,EAAE,UAAU,gBAAgB,EAAE,SAAS,eAAe,EAAE,GAC5D,mBAAmB,UAAU,mBAAmB,OAAO,OAAO;IAEhE,MAAM,QAAQ,8BAA8B;IAE5C,OAAO;QACL,eAAe,iBAAiB,MAAM;QACtC,cAAc,gBAAgB,MAAM;QACpC,WAAW,OAAO,SAAS;QAC3B,YAAY;YACV,cAAc,MAAM,YAAY;YAChC,WAAW,MAAM,SAAS;YAC1B,aAAa,MAAM,WAAW;YAC9B,gBAAgB,MAAM,cAAc;QACtC;QACA,YAAA,yIAAA,CAAA,aAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/data/mock-data.ts"], "sourcesContent": ["import { Student, Subject, TimePeriod, AttendanceRecord } from \"@/lib/types/scanner\"\n\n// Enhanced mock student data with Philippine names and Grade 7-12 structure\nexport const mockStudents: Student[] = [\n  // Grade 7 Students\n  {\n    id: \"STU001\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"1st Year\",\n    section: \"Grade 7-A\",\n    grade: \"7\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU001_2025\"\n  },\n  {\n    id: \"STU002\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"1st Year\",\n    section: \"Grade 7-B\",\n    grade: \"7\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU002_2025\"\n  },\n  {\n    id: \"STU003\",\n    name: \"<PERSON>\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"1st Year\",\n    section: \"Grade 7-A\",\n    grade: \"7\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU003_2025\"\n  },\n  // Grade 8 Students\n  {\n    id: \"STU004\",\n    name: \"Jose Miguel Rodriguez\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"2nd Year\",\n    section: \"Grade 8-A\",\n    grade: \"8\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU004_2025\"\n  },\n  {\n    id: \"STU005\",\n    name: \"Princess Mae Garcia\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"2nd Year\",\n    section: \"Grade 8-B\",\n    grade: \"8\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU005_2025\"\n  },\n  // Grade 9 Students\n  {\n    id: \"STU006\",\n    name: \"Mark Anthony Villanueva\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"3rd Year\",\n    section: \"Grade 9-A\",\n    grade: \"9\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU006_2025\"\n  },\n  {\n    id: \"STU007\",\n    name: \"Angelica Mae Torres\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"3rd Year\",\n    section: \"Grade 9-B\",\n    grade: \"9\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU007_2025\"\n  },\n  // Grade 10 Students\n  {\n    id: \"STU008\",\n    name: \"Christian Paul Mendoza\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"4th Year\",\n    section: \"Grade 10-A\",\n    grade: \"10\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU008_2025\"\n  },\n  {\n    id: \"STU009\",\n    name: \"Kimberly Rose Flores\",\n    email: \"<EMAIL>\",\n    course: \"Junior High School\",\n    year: \"4th Year\",\n    section: \"Grade 10-B\",\n    grade: \"10\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU009_2025\"\n  },\n  // Grade 11 Students (Senior High School)\n  {\n    id: \"STU010\",\n    name: \"John Michael Cruz\",\n    email: \"<EMAIL>\",\n    course: \"Information and Communications Technology\",\n    year: \"1st Year Senior High\",\n    section: \"ICT 11-A\",\n    grade: \"11\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-f4e0f30006d5?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU010_2025\"\n  },\n  {\n    id: \"STU011\",\n    name: \"Mary Grace Aquino\",\n    email: \"<EMAIL>\",\n    course: \"Accountancy, Business and Management\",\n    year: \"1st Year Senior High\",\n    section: \"ABM 11-A\",\n    grade: \"11\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU011_2025\"\n  },\n  // Grade 12 Students (Senior High School)\n  {\n    id: \"STU012\",\n    name: \"Ryan James Bautista\",\n    email: \"<EMAIL>\",\n    course: \"Information and Communications Technology\",\n    year: \"2nd Year Senior High\",\n    section: \"ICT 12-A\",\n    grade: \"12\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU012_2025\"\n  },\n  {\n    id: \"STU013\",\n    name: \"Sarah Jane Morales\",\n    email: \"<EMAIL>\",\n    course: \"Humanities and Social Sciences\",\n    year: \"2nd Year Senior High\",\n    section: \"HUMSS 12-A\",\n    grade: \"12\",\n    status: \"Active\",\n    photo: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    qrCode: \"QR_STU013_2025\"\n  }\n]\n\n// Mock subjects\nexport const mockSubjects: Subject[] = [\n  {\n    id: \"SUBJ001\",\n    name: \"Programming Fundamentals\",\n    code: \"IT101\",\n    instructor: \"Prof. Martinez\",\n    schedule: [\n      { day: \"Monday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Wednesday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Friday\", startTime: \"08:00\", endTime: \"10:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ002\",\n    name: \"Database Management\",\n    code: \"IT201\",\n    instructor: \"Prof. Rodriguez\",\n    schedule: [\n      { day: \"Tuesday\", startTime: \"10:00\", endTime: \"12:00\" },\n      { day: \"Thursday\", startTime: \"10:00\", endTime: \"12:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ003\",\n    name: \"Web Development\",\n    code: \"IT301\",\n    instructor: \"Prof. Santos\",\n    schedule: [\n      { day: \"Monday\", startTime: \"13:00\", endTime: \"15:00\" },\n      { day: \"Wednesday\", startTime: \"13:00\", endTime: \"15:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ004\",\n    name: \"Data Structures\",\n    code: \"CS201\",\n    instructor: \"Prof. Reyes\",\n    schedule: [\n      { day: \"Tuesday\", startTime: \"08:00\", endTime: \"10:00\" },\n      { day: \"Thursday\", startTime: \"08:00\", endTime: \"10:00\" }\n    ]\n  },\n  {\n    id: \"SUBJ005\",\n    name: \"Software Engineering\",\n    code: \"CS301\",\n    instructor: \"Prof. Cruz\",\n    schedule: [\n      { day: \"Monday\", startTime: \"15:00\", endTime: \"17:00\" },\n      { day: \"Friday\", startTime: \"15:00\", endTime: \"17:00\" }\n    ]\n  }\n]\n\n// Mock time periods\nexport const mockTimePeriods: TimePeriod[] = [\n  {\n    id: \"PERIOD001\",\n    name: \"1st Period\",\n    startTime: \"08:00\",\n    endTime: \"10:00\",\n    type: \"morning\"\n  },\n  {\n    id: \"PERIOD002\",\n    name: \"2nd Period\",\n    startTime: \"10:00\",\n    endTime: \"12:00\",\n    type: \"morning\"\n  },\n  {\n    id: \"PERIOD003\",\n    name: \"3rd Period\",\n    startTime: \"13:00\",\n    endTime: \"15:00\",\n    type: \"afternoon\"\n  },\n  {\n    id: \"PERIOD004\",\n    name: \"4th Period\",\n    startTime: \"15:00\",\n    endTime: \"17:00\",\n    type: \"afternoon\"\n  },\n  {\n    id: \"PERIOD005\",\n    name: \"Evening Class\",\n    startTime: \"18:00\",\n    endTime: \"20:00\",\n    type: \"evening\"\n  }\n]\n\n// Enhanced mock attendance records with realistic patterns\nexport const mockAttendanceRecords: AttendanceRecord[] = [\n  // Today's attendance records\n  {\n    id: \"ATT001\",\n    studentId: \"STU001\",\n    studentName: \"Maria Cristina Santos\",\n    course: \"Junior High School\",\n    checkIn: \"7:45 AM\",\n    checkOut: \"4:30 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 45, 0))\n  },\n  {\n    id: \"ATT002\",\n    studentId: \"STU002\",\n    studentName: \"Juan Carlos Dela Cruz\",\n    course: \"Junior High School\",\n    checkIn: \"7:50 AM\",\n    checkOut: \"4:25 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 50, 0))\n  },\n  {\n    id: \"ATT003\",\n    studentId: \"STU003\",\n    studentName: \"Ana Marie Reyes\",\n    course: \"Junior High School\",\n    checkIn: \"8:15 AM\",\n    checkOut: \"4:35 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Late\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(8, 15, 0))\n  },\n  {\n    id: \"ATT004\",\n    studentId: \"STU004\",\n    studentName: \"Jose Miguel Rodriguez\",\n    course: \"Junior High School\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Absent\",\n    type: \"subject\",\n    subject: \"Mathematics\",\n    period: \"1st Period\",\n    timestamp: new Date(new Date().setHours(8, 0, 0))\n  },\n  {\n    id: \"ATT005\",\n    studentId: \"STU005\",\n    studentName: \"Princess Mae Garcia\",\n    course: \"Junior High School\",\n    checkIn: \"7:55 AM\",\n    checkOut: \"4:20 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 55, 0))\n  },\n  {\n    id: \"ATT006\",\n    studentId: \"STU010\",\n    studentName: \"John Michael Cruz\",\n    course: \"Information and Communications Technology\",\n    checkIn: \"7:40 AM\",\n    checkOut: \"5:00 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Present\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(7, 40, 0))\n  },\n  {\n    id: \"ATT007\",\n    studentId: \"STU012\",\n    studentName: \"Ryan James Bautista\",\n    course: \"Information and Communications Technology\",\n    checkIn: \"8:10 AM\",\n    checkOut: \"5:05 PM\",\n    date: new Date().toISOString().split('T')[0],\n    status: \"Late\",\n    type: \"gate\",\n    timestamp: new Date(new Date().setHours(8, 10, 0))\n  }\n]\n\n// Dashboard statistics data\nexport const mockDashboardStats = {\n  totalStudents: 1234,\n  presentToday: 1105,\n  lateToday: 23,\n  absentToday: 106,\n  attendanceRate: 89.5,\n  weeklyTrend: [\n    { day: 'Mon', present: 1150, late: 15, absent: 69 },\n    { day: 'Tue', present: 1120, late: 28, absent: 86 },\n    { day: 'Wed', present: 1105, late: 23, absent: 106 },\n    { day: 'Thu', present: 1140, late: 18, absent: 76 },\n    { day: 'Fri', present: 1095, late: 35, absent: 104 }\n  ],\n  gradeBreakdown: [\n    { grade: '7', total: 180, present: 165, late: 3, absent: 12 },\n    { grade: '8', total: 175, present: 158, late: 4, absent: 13 },\n    { grade: '9', total: 170, present: 155, late: 2, absent: 13 },\n    { grade: '10', total: 165, present: 148, late: 5, absent: 12 },\n    { grade: '11', total: 272, present: 245, late: 6, absent: 21 },\n    { grade: '12', total: 272, present: 234, late: 3, absent: 35 }\n  ]\n}\n\n// Recent activity feed\nexport const mockRecentActivity = [\n  {\n    id: \"ACT001\",\n    type: \"scan\",\n    studentName: \"Maria Cristina Santos\",\n    action: \"Check In\",\n    time: \"2 minutes ago\",\n    status: \"success\"\n  },\n  {\n    id: \"ACT002\",\n    type: \"scan\",\n    studentName: \"Juan Carlos Dela Cruz\",\n    action: \"Check Out\",\n    time: \"5 minutes ago\",\n    status: \"success\"\n  },\n  {\n    id: \"ACT003\",\n    type: \"alert\",\n    studentName: \"Jose Miguel Rodriguez\",\n    action: \"Marked Absent\",\n    time: \"15 minutes ago\",\n    status: \"warning\"\n  },\n  {\n    id: \"ACT004\",\n    type: \"scan\",\n    studentName: \"Princess Mae Garcia\",\n    action: \"Late Arrival\",\n    time: \"25 minutes ago\",\n    status: \"warning\"\n  }\n]\n\n// Helper functions\nexport function findStudentById(id: string): Student | undefined {\n  return mockStudents.find(student => student.id === id)\n}\n\nexport function findStudentByQRCode(qrCode: string): Student | undefined {\n  return mockStudents.find(student => student.qrCode === qrCode)\n}\n\nexport function findSubjectById(id: string): Subject | undefined {\n  return mockSubjects.find(subject => subject.id === id)\n}\n\nexport function findPeriodById(id: string): TimePeriod | undefined {\n  return mockTimePeriods.find(period => period.id === id)\n}\n\nexport function getStudentAttendanceRecords(studentId: string): AttendanceRecord[] {\n  return mockAttendanceRecords.filter(record => record.studentId === studentId)\n}\n\nexport function getTodayAttendanceRecord(studentId: string): AttendanceRecord | undefined {\n  const today = new Date().toISOString().split('T')[0]\n  return mockAttendanceRecords.find(record => \n    record.studentId === studentId && record.date === today\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGO,MAAM,eAA0B;IACrC,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,oBAAoB;IACpB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,yCAAyC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA,yCAAyC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;CACD;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAa,WAAW;gBAAS,SAAS;YAAQ;YACzD;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;SACvD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAW,WAAW;gBAAS,SAAS;YAAQ;YACvD;gBAAE,KAAK;gBAAY,WAAW;gBAAS,SAAS;YAAQ;SACzD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAa,WAAW;gBAAS,SAAS;YAAQ;SAC1D;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAW,WAAW;gBAAS,SAAS;YAAQ;YACvD;gBAAE,KAAK;gBAAY,WAAW;gBAAS,SAAS;YAAQ;SACzD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,YAAY;QACZ,UAAU;YACR;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;YACtD;gBAAE,KAAK;gBAAU,WAAW;gBAAS,SAAS;YAAQ;SACvD;IACH;CACD;AAGM,MAAM,kBAAgC;IAC3C;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;IACR;CACD;AAGM,MAAM,wBAA4C;IACvD,6BAA6B;IAC7B;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,SAAS;QACT,QAAQ;QACR,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,GAAG;IAChD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,QAAQ;QACR,MAAM;QACN,WAAW,IAAI,KAAK,IAAI,OAAO,QAAQ,CAAC,GAAG,IAAI;IACjD;CACD;AAGM,MAAM,qBAAqB;IAChC,eAAe;IACf,cAAc;IACd,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,aAAa;QACX;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAG;QAClD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAG;QAClD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAI;QACnD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAG;QAClD;YAAE,KAAK;YAAO,SAAS;YAAM,MAAM;YAAI,QAAQ;QAAI;KACpD;IACD,gBAAgB;QACd;YAAE,OAAO;YAAK,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC5D;YAAE,OAAO;YAAK,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC5D;YAAE,OAAO;YAAK,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC5D;YAAE,OAAO;YAAM,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC7D;YAAE,OAAO;YAAM,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;QAC7D;YAAE,OAAO;YAAM,OAAO;YAAK,SAAS;YAAK,MAAM;YAAG,QAAQ;QAAG;KAC9D;AACH;AAGO,MAAM,qBAAqB;IAChC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,MAAM;QACN,QAAQ;IACV;CACD;AAGM,SAAS,gBAAgB,EAAU;IACxC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,oBAAoB,MAAc;IAChD,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,MAAM,KAAK;AACzD;AAEO,SAAS,gBAAgB,EAAU;IACxC,OAAO,aAAa,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACrD;AAEO,SAAS,eAAe,EAAU;IACvC,OAAO,gBAAgB,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AACtD;AAEO,SAAS,4BAA4B,SAAiB;IAC3D,OAAO,sBAAsB,MAAM,CAAC,CAAA,SAAU,OAAO,SAAS,KAAK;AACrE;AAEO,SAAS,yBAAyB,SAAiB;IACxD,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACpD,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAChC,OAAO,SAAS,KAAK,aAAa,OAAO,IAAI,KAAK;AAEtD", "debugId": null}}, {"offset": {"line": 1140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/lib/utils/export-utils.ts"], "sourcesContent": ["import { format } from \"date-fns\"\nimport { SF2Report, SF4Report, CustomReport, ExportFormat } from '@/lib/types/reports'\n\n// Types for export data\nexport interface AttendanceRecord {\n  id: string\n  studentId: string\n  studentName: string\n  course: string\n  grade: string\n  section: string\n  checkIn?: string\n  checkOut?: string\n  date: string\n  status: \"Present\" | \"Late\" | \"Absent\"\n  type: \"gate\" | \"subject\"\n  subject?: string\n  period?: string\n  reason?: string\n}\n\nexport interface ExportOptions {\n  format: \"csv\" | \"pdf\" | \"excel\"\n  dateRange?: {\n    from: Date\n    to: Date\n  }\n  includePhotos?: boolean\n  groupBy?: \"grade\" | \"section\" | \"course\" | \"none\"\n}\n\n// CSV Export Functions\nexport function exportToCSV(data: AttendanceRecord[], filename?: string): void {\n  const headers = [\n    \"Student ID\",\n    \"Student Name\", \n    \"Grade\",\n    \"Section\",\n    \"Course\",\n    \"Date\",\n    \"Check In\",\n    \"Check Out\",\n    \"Status\",\n    \"Type\",\n    \"Subject\",\n    \"Period\",\n    \"Reason\"\n  ]\n\n  const csvContent = [\n    headers.join(\",\"),\n    ...data.map(record => [\n      record.studentId,\n      `\"${record.studentName}\"`,\n      record.grade,\n      `\"${record.section}\"`,\n      `\"${record.course}\"`,\n      record.date,\n      record.checkIn || \"\",\n      record.checkOut || \"\",\n      record.status,\n      record.type,\n      record.subject || \"\",\n      record.period || \"\",\n      record.reason ? `\"${record.reason}\"` : \"\"\n    ].join(\",\"))\n  ].join(\"\\n\")\n\n  downloadFile(\n    csvContent,\n    filename || `attendance-report-${format(new Date(), \"yyyy-MM-dd\")}.csv`,\n    \"text/csv\"\n  )\n}\n\n// Generate daily report data\nexport function generateDailyReport(data: AttendanceRecord[], date: Date) {\n  const dateStr = format(date, \"yyyy-MM-dd\")\n  const dayData = data.filter(record => record.date === dateStr)\n  \n  const summary = {\n    date: format(date, \"MMMM d, yyyy\"),\n    totalStudents: dayData.length,\n    present: dayData.filter(r => r.status === \"Present\").length,\n    late: dayData.filter(r => r.status === \"Late\").length,\n    absent: dayData.filter(r => r.status === \"Absent\").length,\n    attendanceRate: 0\n  }\n\n  if (summary.totalStudents > 0) {\n    summary.attendanceRate = ((summary.present + summary.late) / summary.totalStudents) * 100\n  }\n\n  const gradeBreakdown = dayData.reduce((acc, record) => {\n    const grade = record.grade\n    if (!acc[grade]) {\n      acc[grade] = { total: 0, present: 0, late: 0, absent: 0 }\n    }\n    acc[grade].total++\n    acc[grade][record.status.toLowerCase() as keyof typeof acc[typeof grade]]++\n    return acc\n  }, {} as Record<string, { total: number; present: number; late: number; absent: number }>)\n\n  return {\n    summary,\n    gradeBreakdown,\n    records: dayData\n  }\n}\n\n// Print functionality\nexport function printDailyReport(data: AttendanceRecord[], date: Date): void {\n  const report = generateDailyReport(data, date)\n  \n  const printContent = `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>Daily Attendance Report - ${report.summary.date}</title>\n      <style>\n        body { \n          font-family: Arial, sans-serif; \n          margin: 20px; \n          color: #333;\n        }\n        .header { \n          text-align: center; \n          margin-bottom: 30px;\n          border-bottom: 2px solid #333;\n          padding-bottom: 20px;\n        }\n        .school-name {\n          font-size: 24px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        .report-title {\n          font-size: 18px;\n          color: #666;\n        }\n        .summary { \n          display: grid; \n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 20px;\n          margin-bottom: 30px;\n        }\n        .summary-card {\n          border: 1px solid #ddd;\n          padding: 15px;\n          border-radius: 8px;\n          text-align: center;\n        }\n        .summary-value {\n          font-size: 32px;\n          font-weight: bold;\n          margin-bottom: 5px;\n        }\n        .summary-label {\n          color: #666;\n          font-size: 14px;\n        }\n        .present { color: #22c55e; }\n        .late { color: #f59e0b; }\n        .absent { color: #ef4444; }\n        .rate { color: #3b82f6; }\n        \n        .grade-breakdown {\n          margin-bottom: 30px;\n        }\n        .grade-breakdown h3 {\n          margin-bottom: 15px;\n          color: #333;\n        }\n        .grade-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 20px;\n        }\n        .grade-table th,\n        .grade-table td {\n          border: 1px solid #ddd;\n          padding: 8px;\n          text-align: center;\n        }\n        .grade-table th {\n          background-color: #f5f5f5;\n          font-weight: bold;\n        }\n        \n        .records-table {\n          width: 100%;\n          border-collapse: collapse;\n          font-size: 12px;\n        }\n        .records-table th,\n        .records-table td {\n          border: 1px solid #ddd;\n          padding: 6px;\n          text-align: left;\n        }\n        .records-table th {\n          background-color: #f5f5f5;\n          font-weight: bold;\n        }\n        \n        .footer {\n          margin-top: 40px;\n          text-align: center;\n          color: #666;\n          font-size: 12px;\n          border-top: 1px solid #ddd;\n          padding-top: 20px;\n        }\n        \n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        <div class=\"school-name\">QRSAMS - Tanauan National High School</div>\n        <div class=\"report-title\">Daily Attendance Report</div>\n        <div style=\"margin-top: 10px; font-size: 16px;\">${report.summary.date}</div>\n      </div>\n      \n      <div class=\"summary\">\n        <div class=\"summary-card\">\n          <div class=\"summary-value\">${report.summary.totalStudents}</div>\n          <div class=\"summary-label\">Total Students</div>\n        </div>\n        <div class=\"summary-card\">\n          <div class=\"summary-value present\">${report.summary.present}</div>\n          <div class=\"summary-label\">Present</div>\n        </div>\n        <div class=\"summary-card\">\n          <div class=\"summary-value late\">${report.summary.late}</div>\n          <div class=\"summary-label\">Late</div>\n        </div>\n        <div class=\"summary-card\">\n          <div class=\"summary-value absent\">${report.summary.absent}</div>\n          <div class=\"summary-label\">Absent</div>\n        </div>\n        <div class=\"summary-card\">\n          <div class=\"summary-value rate\">${report.summary.attendanceRate.toFixed(1)}%</div>\n          <div class=\"summary-label\">Attendance Rate</div>\n        </div>\n      </div>\n      \n      <div class=\"grade-breakdown\">\n        <h3>Grade Level Breakdown</h3>\n        <table class=\"grade-table\">\n          <thead>\n            <tr>\n              <th>Grade</th>\n              <th>Total</th>\n              <th>Present</th>\n              <th>Late</th>\n              <th>Absent</th>\n              <th>Rate</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${Object.entries(report.gradeBreakdown).map(([grade, stats]) => `\n              <tr>\n                <td>Grade ${grade}</td>\n                <td>${stats.total}</td>\n                <td class=\"present\">${stats.present}</td>\n                <td class=\"late\">${stats.late}</td>\n                <td class=\"absent\">${stats.absent}</td>\n                <td>${((stats.present + stats.late) / stats.total * 100).toFixed(1)}%</td>\n              </tr>\n            `).join(\"\")}\n          </tbody>\n        </table>\n      </div>\n      \n      <div>\n        <h3>Detailed Records</h3>\n        <table class=\"records-table\">\n          <thead>\n            <tr>\n              <th>Student ID</th>\n              <th>Name</th>\n              <th>Grade</th>\n              <th>Section</th>\n              <th>Check In</th>\n              <th>Check Out</th>\n              <th>Status</th>\n              <th>Notes</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${report.records.map(record => `\n              <tr>\n                <td>${record.studentId}</td>\n                <td>${record.studentName}</td>\n                <td>${record.grade}</td>\n                <td>${record.section}</td>\n                <td>${record.checkIn || \"-\"}</td>\n                <td>${record.checkOut || \"-\"}</td>\n                <td class=\"${record.status.toLowerCase()}\">${record.status}</td>\n                <td>${record.reason || \"-\"}</td>\n              </tr>\n            `).join(\"\")}\n          </tbody>\n        </table>\n      </div>\n      \n      <div class=\"footer\">\n        Generated on ${format(new Date(), \"MMMM d, yyyy 'at' h:mm a\")} by QRSAMS\n      </div>\n    </body>\n    </html>\n  `\n\n  const printWindow = window.open(\"\", \"_blank\")\n  if (printWindow) {\n    printWindow.document.write(printContent)\n    printWindow.document.close()\n    printWindow.focus()\n    printWindow.print()\n    printWindow.close()\n  }\n}\n\n// Utility function to download files\nfunction downloadFile(content: string, filename: string, mimeType: string): void {\n  const blob = new Blob([content], { type: mimeType })\n  const url = URL.createObjectURL(blob)\n  const link = document.createElement(\"a\")\n  link.href = url\n  link.download = filename\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n  URL.revokeObjectURL(url)\n}\n\n// Generate summary statistics\nexport function generateSummaryStats(data: AttendanceRecord[]) {\n  const total = data.length\n  const present = data.filter(r => r.status === \"Present\").length\n  const late = data.filter(r => r.status === \"Late\").length\n  const absent = data.filter(r => r.status === \"Absent\").length\n  const attendanceRate = total > 0 ? ((present + late) / total) * 100 : 0\n\n  return {\n    total,\n    present,\n    late,\n    absent,\n    attendanceRate\n  }\n}\n\n// Enhanced PDF Generation for Reports\nexport class ReportPDFGenerator {\n  static async generateSF2PDF(report: SF2Report): Promise<Blob> {\n    const htmlContent = this.generateSF2HTML(report)\n\n    // Simulate PDF generation delay\n    await new Promise(resolve => setTimeout(resolve, 1000))\n\n    // In a real implementation, this would use a PDF library like jsPDF or Puppeteer\n    // For now, we'll create a mock PDF blob\n    const pdfContent = `%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n\n4 0 obj\n<<\n/Length 44\n>>\nstream\nBT\n/F1 12 Tf\n72 720 Td\n(SF2 Daily Attendance Report) Tj\nET\nendstream\nendobj\n\nxref\n0 5\n0000000000 65535 f\n0000000009 00000 n\n0000000058 00000 n\n0000000115 00000 n\n0000000206 00000 n\ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n299\n%%EOF`\n\n    return new Blob([pdfContent], { type: 'application/pdf' })\n  }\n\n  static async generateSF4PDF(report: SF4Report): Promise<Blob> {\n    const htmlContent = this.generateSF4HTML(report)\n\n    await new Promise(resolve => setTimeout(resolve, 1000))\n\n    const pdfContent = `%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 792 612]\n/Contents 4 0 R\n>>\nendobj\n\n4 0 obj\n<<\n/Length 50\n>>\nstream\nBT\n/F1 12 Tf\n72 720 Td\n(SF4 Monthly Learner's Movement) Tj\nET\nendstream\nendobj\n\nxref\n0 5\n0000000000 65535 f\n0000000009 00000 n\n0000000058 00000 n\n0000000115 00000 n\n0000000206 00000 n\ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n305\n%%EOF`\n\n    return new Blob([pdfContent], { type: 'application/pdf' })\n  }\n\n  static async generateCustomPDF(report: CustomReport): Promise<Blob> {\n    const htmlContent = this.generateCustomHTML(report)\n\n    await new Promise(resolve => setTimeout(resolve, 1000))\n\n    const pdfContent = `%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n/Contents 4 0 R\n>>\nendobj\n\n4 0 obj\n<<\n/Length 35\n>>\nstream\nBT\n/F1 12 Tf\n72 720 Td\n(Custom Report) Tj\nET\nendstream\nendobj\n\nxref\n0 5\n0000000000 65535 f\n0000000009 00000 n\n0000000058 00000 n\n0000000115 00000 n\n0000000206 00000 n\ntrailer\n<<\n/Size 5\n/Root 1 0 R\n>>\nstartxref\n290\n%%EOF`\n\n    return new Blob([pdfContent], { type: 'application/pdf' })\n  }\n\n  private static generateSF2HTML(report: SF2Report): string {\n    return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>SF2 Daily Attendance Report</title>\n      <style>\n        body { font-family: Arial, sans-serif; margin: 20px; }\n        .header { text-align: center; margin-bottom: 30px; }\n        .school-info { margin-bottom: 20px; }\n        .attendance-table { width: 100%; border-collapse: collapse; }\n        .attendance-table th, .attendance-table td { border: 1px solid #000; padding: 8px; text-align: center; }\n        .signature-section { margin-top: 40px; }\n        @media print { body { margin: 0; } }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        <h1>Republic of the Philippines</h1>\n        <h2>Department of Education</h2>\n        <h3>${report.schoolInfo.region}</h3>\n        <h4>${report.schoolInfo.division}</h4>\n        <h5>${report.schoolInfo.schoolName}</h5>\n        <h2>SCHOOL FORM 2 (SF2)</h2>\n        <h3>DAILY ATTENDANCE REPORT OF LEARNERS</h3>\n      </div>\n\n      <div class=\"school-info\">\n        <p><strong>Grade & Section:</strong> Grade ${report.grade} - ${report.section}</p>\n        <p><strong>Subject:</strong> ${report.subject || 'All Subjects'}</p>\n        <p><strong>Teacher:</strong> ${report.teacher.name}</p>\n        <p><strong>Date:</strong> ${new Date(report.date).toLocaleDateString('en-PH', { year: 'numeric', month: 'long', day: 'numeric' })}</p>\n      </div>\n\n      <table class=\"attendance-table\">\n        <thead>\n          <tr>\n            <th>No.</th>\n            <th>LEARNER'S NAME</th>\n            <th>ATTENDANCE</th>\n            <th>REMARKS</th>\n          </tr>\n        </thead>\n        <tbody>\n          ${report.students.map((student, index) => `\n            <tr>\n              <td>${index + 1}</td>\n              <td>${student.studentName}</td>\n              <td>${student.dailyStatus}</td>\n              <td>${student.remarks || ''}</td>\n            </tr>\n          `).join('')}\n        </tbody>\n      </table>\n\n      <div class=\"signature-section\">\n        <p><strong>Summary:</strong></p>\n        <p>Total Students: ${report.summary.totalStudents}</p>\n        <p>Present: ${report.summary.presentCount}</p>\n        <p>Late: ${report.summary.lateCount}</p>\n        <p>Absent: ${report.summary.absentCount}</p>\n        <p>Attendance Rate: ${report.summary.attendanceRate.toFixed(1)}%</p>\n\n        <div style=\"margin-top: 60px;\">\n          <div style=\"display: inline-block; width: 300px; text-align: center;\">\n            <div style=\"border-bottom: 1px solid #000; height: 40px;\"></div>\n            <p>Teacher's Signature</p>\n            <p>${report.teacher.name}</p>\n          </div>\n          <div style=\"display: inline-block; width: 300px; text-align: center; margin-left: 50px;\">\n            <div style=\"border-bottom: 1px solid #000; height: 40px;\"></div>\n            <p>Principal's Signature</p>\n            <p>${report.schoolInfo.principalName}</p>\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n    `\n  }\n\n  private static generateSF4HTML(report: SF4Report): string {\n    return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>SF4 Monthly Learner's Movement</title>\n      <style>\n        body { font-family: Arial, sans-serif; margin: 20px; }\n        .header { text-align: center; margin-bottom: 30px; }\n        .info-section { margin-bottom: 20px; }\n        .movement-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n        .movement-table th, .movement-table td { border: 1px solid #000; padding: 8px; }\n        .signature-section { margin-top: 40px; }\n        @media print { body { margin: 0; } }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        <h1>Republic of the Philippines</h1>\n        <h2>Department of Education</h2>\n        <h3>${report.schoolInfo.region}</h3>\n        <h4>${report.schoolInfo.division}</h4>\n        <h5>${report.schoolInfo.schoolName}</h5>\n        <h2>SCHOOL FORM 4 (SF4)</h2>\n        <h3>MONTHLY REPORT ON LEARNER'S MOVEMENT</h3>\n      </div>\n\n      <div class=\"info-section\">\n        <p><strong>Month:</strong> ${report.month} ${report.year}</p>\n        <p><strong>Grade:</strong> ${report.grade}</p>\n        ${report.section ? `<p><strong>Section:</strong> ${report.section}</p>` : ''}\n      </div>\n\n      <h4>ENROLLMENT SUMMARY</h4>\n      <table class=\"movement-table\">\n        <tr>\n          <td>Beginning of Month</td>\n          <td>${report.enrollment.beginningOfMonth}</td>\n        </tr>\n        <tr>\n          <td>New Admissions</td>\n          <td>${report.enrollment.newAdmissions.length}</td>\n        </tr>\n        <tr>\n          <td>Transfers Out</td>\n          <td>${report.enrollment.transfers.transferredOut.length}</td>\n        </tr>\n        <tr>\n          <td>Dropouts</td>\n          <td>${report.enrollment.dropouts.length}</td>\n        </tr>\n        <tr>\n          <td><strong>End of Month</strong></td>\n          <td><strong>${report.enrollment.endOfMonth}</strong></td>\n        </tr>\n      </table>\n\n      <h4>ATTENDANCE STATISTICS</h4>\n      <table class=\"movement-table\">\n        <tr>\n          <td>Total School Days</td>\n          <td>${report.attendance.totalSchoolDays}</td>\n        </tr>\n        <tr>\n          <td>Average Daily Attendance</td>\n          <td>${report.attendance.averageAttendance}</td>\n        </tr>\n        <tr>\n          <td>Attendance Rate</td>\n          <td>${report.attendance.attendanceRate.toFixed(1)}%</td>\n        </tr>\n      </table>\n\n      <div class=\"signature-section\">\n        <div style=\"margin-top: 60px;\">\n          <div style=\"display: inline-block; width: 300px; text-align: center;\">\n            <div style=\"border-bottom: 1px solid #000; height: 40px;\"></div>\n            <p>Prepared by</p>\n            <p>Class Adviser/Teacher</p>\n          </div>\n          <div style=\"display: inline-block; width: 300px; text-align: center; margin-left: 50px;\">\n            <div style=\"border-bottom: 1px solid #000; height: 40px;\"></div>\n            <p>Principal's Signature</p>\n            <p>${report.schoolInfo.principalName}</p>\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n    `\n  }\n\n  private static generateCustomHTML(report: CustomReport): string {\n    return `\n    <!DOCTYPE html>\n    <html>\n    <head>\n      <title>${report.name}</title>\n      <style>\n        body { font-family: Arial, sans-serif; margin: 20px; }\n        .header { text-align: center; margin-bottom: 30px; }\n        .report-table { width: 100%; border-collapse: collapse; }\n        .report-table th, .report-table td { border: 1px solid #000; padding: 8px; }\n        @media print { body { margin: 0; } }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        <h1>${report.name}</h1>\n        <p>${report.description}</p>\n        <p>Generated on ${new Date(report.generatedAt).toLocaleDateString()}</p>\n      </div>\n\n      <table class=\"report-table\">\n        <thead>\n          <tr>\n            ${report.query.fields.map(field => `<th>${field}</th>`).join('')}\n          </tr>\n        </thead>\n        <tbody>\n          ${report.data.map(row => `\n            <tr>\n              ${report.query.fields.map(field => `<td>${row[field] || ''}</td>`).join('')}\n            </tr>\n          `).join('')}\n        </tbody>\n      </table>\n    </body>\n    </html>\n    `\n  }\n}\n\n// Enhanced Excel Generation for Reports\nexport class ReportExcelGenerator {\n  static async generateSF2Excel(report: SF2Report): Promise<Blob> {\n    // In a real implementation, this would use a library like SheetJS or ExcelJS\n    const csvContent = this.generateSF2CSV(report)\n\n    await new Promise(resolve => setTimeout(resolve, 500))\n\n    return new Blob([csvContent], {\n      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n    })\n  }\n\n  static async generateSF4Excel(report: SF4Report): Promise<Blob> {\n    const csvContent = this.generateSF4CSV(report)\n\n    await new Promise(resolve => setTimeout(resolve, 500))\n\n    return new Blob([csvContent], {\n      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n    })\n  }\n\n  static async generateCustomExcel(report: CustomReport): Promise<Blob> {\n    const csvContent = this.generateCustomCSV(report)\n\n    await new Promise(resolve => setTimeout(resolve, 500))\n\n    return new Blob([csvContent], {\n      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n    })\n  }\n\n  private static generateSF2CSV(report: SF2Report): string {\n    const headers = ['No.', 'Student Name', 'Attendance Status', 'Remarks']\n    const rows = report.students.map((student, index) => [\n      index + 1,\n      student.studentName,\n      student.dailyStatus,\n      student.remarks || ''\n    ])\n\n    return [\n      `SF2 Daily Attendance Report - ${report.date}`,\n      `Grade ${report.grade} - ${report.section}`,\n      `Teacher: ${report.teacher.name}`,\n      '',\n      headers.join(','),\n      ...rows.map(row => row.join(','))\n    ].join('\\n')\n  }\n\n  private static generateSF4CSV(report: SF4Report): string {\n    return [\n      `SF4 Monthly Learner's Movement - ${report.month} ${report.year}`,\n      `Grade ${report.grade}`,\n      '',\n      'Enrollment Summary',\n      `Beginning of Month,${report.enrollment.beginningOfMonth}`,\n      `New Admissions,${report.enrollment.newAdmissions.length}`,\n      `Transfers Out,${report.enrollment.transfers.transferredOut.length}`,\n      `Dropouts,${report.enrollment.dropouts.length}`,\n      `End of Month,${report.enrollment.endOfMonth}`,\n      '',\n      'Attendance Statistics',\n      `Total School Days,${report.attendance.totalSchoolDays}`,\n      `Average Daily Attendance,${report.attendance.averageAttendance}`,\n      `Attendance Rate,${report.attendance.attendanceRate.toFixed(1)}%`\n    ].join('\\n')\n  }\n\n  private static generateCustomCSV(report: CustomReport): string {\n    const headers = report.query.fields\n    const rows = report.data.map(row =>\n      headers.map(field => row[field] || '').join(',')\n    )\n\n    return [\n      report.name,\n      report.description,\n      `Generated on ${new Date(report.generatedAt).toLocaleDateString()}`,\n      '',\n      headers.join(','),\n      ...rows\n    ].join('\\n')\n  }\n}\n\n// Enhanced Export Manager for Reports\nexport class ReportExportManager {\n  static async exportReport(\n    report: SF2Report | SF4Report | CustomReport,\n    format: ExportFormat\n  ): Promise<{ blob: Blob; filename: string }> {\n    let blob: Blob\n    let filename: string\n\n    // Determine report type\n    const reportType = 'students' in report ? 'SF2' :\n                      'enrollment' in report ? 'SF4' : 'CUSTOM'\n\n    switch (format) {\n      case 'PDF':\n        if (reportType === 'SF2') {\n          blob = await ReportPDFGenerator.generateSF2PDF(report as SF2Report)\n          filename = `SF2_${(report as SF2Report).date}_Grade${(report as SF2Report).grade}.pdf`\n        } else if (reportType === 'SF4') {\n          blob = await ReportPDFGenerator.generateSF4PDF(report as SF4Report)\n          filename = `SF4_${(report as SF4Report).month}_${(report as SF4Report).year}_Grade${(report as SF4Report).grade}.pdf`\n        } else {\n          blob = await ReportPDFGenerator.generateCustomPDF(report as CustomReport)\n          filename = `${(report as CustomReport).name.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`\n        }\n        break\n\n      case 'EXCEL':\n        if (reportType === 'SF2') {\n          blob = await ReportExcelGenerator.generateSF2Excel(report as SF2Report)\n          filename = `SF2_${(report as SF2Report).date}_Grade${(report as SF2Report).grade}.xlsx`\n        } else if (reportType === 'SF4') {\n          blob = await ReportExcelGenerator.generateSF4Excel(report as SF4Report)\n          filename = `SF4_${(report as SF4Report).month}_${(report as SF4Report).year}_Grade${(report as SF4Report).grade}.xlsx`\n        } else {\n          blob = await ReportExcelGenerator.generateCustomExcel(report as CustomReport)\n          filename = `${(report as CustomReport).name.replace(/[^a-zA-Z0-9]/g, '_')}.xlsx`\n        }\n        break\n\n      case 'CSV':\n        if (reportType === 'SF2') {\n          const csvContent = ReportExcelGenerator['generateSF2CSV'](report as SF2Report)\n          blob = new Blob([csvContent], { type: 'text/csv' })\n          filename = `SF2_${(report as SF2Report).date}_Grade${(report as SF2Report).grade}.csv`\n        } else if (reportType === 'SF4') {\n          const csvContent = ReportExcelGenerator['generateSF4CSV'](report as SF4Report)\n          blob = new Blob([csvContent], { type: 'text/csv' })\n          filename = `SF4_${(report as SF4Report).month}_${(report as SF4Report).year}_Grade${(report as SF4Report).grade}.csv`\n        } else {\n          const csvContent = ReportExcelGenerator['generateCustomCSV'](report as CustomReport)\n          blob = new Blob([csvContent], { type: 'text/csv' })\n          filename = `${(report as CustomReport).name.replace(/[^a-zA-Z0-9]/g, '_')}.csv`\n        }\n        break\n\n      default:\n        throw new Error(`Unsupported export format: ${format}`)\n    }\n\n    return { blob, filename }\n  }\n\n  static downloadFile(blob: Blob, filename: string) {\n    const url = URL.createObjectURL(blob)\n    const link = document.createElement('a')\n    link.href = url\n    link.download = filename\n    document.body.appendChild(link)\n    link.click()\n    document.body.removeChild(link)\n    URL.revokeObjectURL(url)\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAgCO,SAAS,YAAY,IAAwB,EAAE,QAAiB;IACrE,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa;QACjB,QAAQ,IAAI,CAAC;WACV,KAAK,GAAG,CAAC,CAAA,SAAU;gBACpB,OAAO,SAAS;gBACf,IAAsB,OAAnB,OAAO,WAAW,EAAC;gBACvB,OAAO,KAAK;gBACX,IAAkB,OAAf,OAAO,OAAO,EAAC;gBAClB,IAAiB,OAAd,OAAO,MAAM,EAAC;gBAClB,OAAO,IAAI;gBACX,OAAO,OAAO,IAAI;gBAClB,OAAO,QAAQ,IAAI;gBACnB,OAAO,MAAM;gBACb,OAAO,IAAI;gBACX,OAAO,OAAO,IAAI;gBAClB,OAAO,MAAM,IAAI;gBACjB,OAAO,MAAM,GAAG,AAAC,IAAiB,OAAd,OAAO,MAAM,EAAC,OAAK;aACxC,CAAC,IAAI,CAAC;KACR,CAAC,IAAI,CAAC;IAEP,aACE,YACA,YAAY,AAAC,qBAAqD,OAAjC,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,eAAc,SAClE;AAEJ;AAGO,SAAS,oBAAoB,IAAwB,EAAE,IAAU;IACtE,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IAC7B,MAAM,UAAU,KAAK,MAAM,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;IAEtD,MAAM,UAAU;QACd,MAAM,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QACnB,eAAe,QAAQ,MAAM;QAC7B,SAAS,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;QAC3D,MAAM,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;QACrD,QAAQ,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;QACzD,gBAAgB;IAClB;IAEA,IAAI,QAAQ,aAAa,GAAG,GAAG;QAC7B,QAAQ,cAAc,GAAG,AAAC,CAAC,QAAQ,OAAO,GAAG,QAAQ,IAAI,IAAI,QAAQ,aAAa,GAAI;IACxF;IAEA,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAC,KAAK;QAC1C,MAAM,QAAQ,OAAO,KAAK;QAC1B,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YACf,GAAG,CAAC,MAAM,GAAG;gBAAE,OAAO;gBAAG,SAAS;gBAAG,MAAM;gBAAG,QAAQ;YAAE;QAC1D;QACA,GAAG,CAAC,MAAM,CAAC,KAAK;QAChB,GAAG,CAAC,MAAM,CAAC,OAAO,MAAM,CAAC,WAAW,GAAqC;QACzE,OAAO;IACT,GAAG,CAAC;IAEJ,OAAO;QACL;QACA;QACA,SAAS;IACX;AACF;AAGO,SAAS,iBAAiB,IAAwB,EAAE,IAAU;IACnE,MAAM,SAAS,oBAAoB,MAAM;IAEzC,MAAM,eAAe,AAAC,yFA8GkC,OA1GjB,OAAO,OAAO,CAAC,IAAI,EAAC,6vFA+GtB,OALmB,OAAO,OAAO,CAAC,IAAI,EAAC,wIAS/B,OAJR,OAAO,OAAO,CAAC,aAAa,EAAC,wKAQxB,OAJG,OAAO,OAAO,CAAC,OAAO,EAAC,8JAQxB,OAJF,OAAO,OAAO,CAAC,IAAI,EAAC,6JAQpB,OAJE,OAAO,OAAO,CAAC,MAAM,EAAC,6JAuBtD,OAnB8B,OAAO,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAG,igBAiDvE,OA9BA,OAAO,OAAO,CAAC,OAAO,cAAc,EAAE,GAAG,CAAC;YAAC,CAAC,OAAO,MAAM;eAAK,AAAC,mDAGvD,OADM,OAAM,+BAEI,OADhB,MAAM,KAAK,EAAC,+CAEC,OADG,MAAM,OAAO,EAAC,4CAEf,OADF,MAAM,IAAI,EAAC,8CAExB,OADe,MAAM,MAAM,EAAC,+BACkC,OAA9D,CAAC,CAAC,MAAM,OAAO,GAAG,MAAM,IAAI,IAAI,MAAM,KAAK,GAAG,GAAG,EAAE,OAAO,CAAC,IAAG;OAErE,IAAI,CAAC,KAAI,kgBAsCD,OAjBT,OAAO,OAAO,CAAC,GAAG,CAAC,CAAA,SAAU,AAAC,6CAGtB,OADA,OAAO,SAAS,EAAC,+BAEjB,OADA,OAAO,WAAW,EAAC,+BAEnB,OADA,OAAO,KAAK,EAAC,+BAEb,OADA,OAAO,OAAO,EAAC,+BAEf,OADA,OAAO,OAAO,IAAI,KAAI,+BAEf,OADP,OAAO,QAAQ,IAAI,KAAI,sCACgB,OAAhC,OAAO,MAAM,CAAC,WAAW,IAAG,MACnC,OADuC,OAAO,MAAM,EAAC,+BAChC,OAArB,OAAO,MAAM,IAAI,KAAI,6CAE5B,IAAI,CAAC,KAAI,mHAM8C,OAA/C,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,6BAA4B;IAMpE,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;IACpC,IAAI,aAAa;QACf,YAAY,QAAQ,CAAC,KAAK,CAAC;QAC3B,YAAY,QAAQ,CAAC,KAAK;QAC1B,YAAY,KAAK;QACjB,YAAY,KAAK;QACjB,YAAY,KAAK;IACnB;AACF;AAEA,qCAAqC;AACrC,SAAS,aAAa,OAAe,EAAE,QAAgB,EAAE,QAAgB;IACvE,MAAM,OAAO,IAAI,KAAK;QAAC;KAAQ,EAAE;QAAE,MAAM;IAAS;IAClD,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,qBAAqB,IAAwB;IAC3D,MAAM,QAAQ,KAAK,MAAM;IACzB,MAAM,UAAU,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;IAC/D,MAAM,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;IACzD,MAAM,SAAS,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;IAC7D,MAAM,iBAAiB,QAAQ,IAAI,AAAC,CAAC,UAAU,IAAI,IAAI,QAAS,MAAM;IAEtE,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAGO,MAAM;IACX,aAAa,eAAe,MAAiB,EAAiB;QAC5D,MAAM,cAAc,IAAI,CAAC,eAAe,CAAC;QAEzC,gCAAgC;QAChC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,iFAAiF;QACjF,wCAAwC;QACxC,MAAM,aAAc;QAsDpB,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAkB;IAC1D;IAEA,aAAa,eAAe,MAAiB,EAAiB;QAC5D,MAAM,cAAc,IAAI,CAAC,eAAe,CAAC;QAEzC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,aAAc;QAsDpB,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAkB;IAC1D;IAEA,aAAa,kBAAkB,MAAoB,EAAiB;QAClE,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC;QAE5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,MAAM,aAAc;QAsDpB,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAkB;IAC1D;IAEA,OAAe,gBAAgB,MAAiB,EAAU;QACxD,OAAO,AAAC,kuBAoBE,OADA,OAAO,UAAU,CAAC,MAAM,EAAC,uBAEzB,OADA,OAAO,UAAU,CAAC,QAAQ,EAAC,uBAOY,OANvC,OAAO,UAAU,CAAC,UAAU,EAAC,2MAM4B,OAAlB,OAAO,KAAK,EAAC,OAC3B,OADgC,OAAO,OAAO,EAAC,+CAE/C,OADA,OAAO,OAAO,IAAI,gBAAe,+CAEpC,OADG,OAAO,OAAO,CAAC,IAAI,EAAC,4CAc/C,OAbwB,IAAI,KAAK,OAAO,IAAI,EAAE,kBAAkB,CAAC,SAAS;YAAE,MAAM;YAAW,OAAO;YAAQ,KAAK;QAAU,IAAG,gSA0B7G,OAbjB,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,QAAU,AAAC,yCAGjC,OADA,QAAQ,GAAE,6BAEV,OADA,QAAQ,WAAW,EAAC,6BAEpB,OADA,QAAQ,WAAW,EAAC,6BACE,OAAtB,QAAQ,OAAO,IAAI,IAAG,yCAE7B,IAAI,CAAC,KAAI,sJAOA,OADO,OAAO,OAAO,CAAC,aAAa,EAAC,8BAEvC,OADG,OAAO,OAAO,CAAC,YAAY,EAAC,2BAE7B,OADF,OAAO,OAAO,CAAC,SAAS,EAAC,6BAEd,OADT,OAAO,OAAO,CAAC,WAAW,EAAC,sCAO/B,OANa,OAAO,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAG,8QAWtD,OALA,OAAO,OAAO,CAAC,IAAI,EAAC,yQAKY,OAAhC,OAAO,UAAU,CAAC,aAAa,EAAC;IAO/C;IAEA,OAAe,gBAAgB,MAAiB,EAAU;QACxD,OAAO,AAAC,kuBAoBE,OADA,OAAO,UAAU,CAAC,MAAM,EAAC,uBAEzB,OADA,OAAO,UAAU,CAAC,QAAQ,EAAC,uBAOJ,OANvB,OAAO,UAAU,CAAC,UAAU,EAAC,8LAMU,OAAhB,OAAO,KAAK,EAAC,KACb,OADgB,OAAO,IAAI,EAAC,6CAEvD,OAD2B,OAAO,KAAK,EAAC,kBAQlC,OAPN,OAAO,OAAO,GAAG,AAAC,gCAA8C,OAAf,OAAO,OAAO,EAAC,UAAQ,IAAG,kKAWrE,OAJA,OAAO,UAAU,CAAC,gBAAgB,EAAC,yFAQnC,OAJA,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM,EAAC,wFAQvC,OAJA,OAAO,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,EAAC,mFAQ1C,OAJR,OAAO,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAC,gHAYlC,OARQ,OAAO,UAAU,CAAC,UAAU,EAAC,mMAYrC,OAJA,OAAO,UAAU,CAAC,eAAe,EAAC,mGAQlC,OAJA,OAAO,UAAU,CAAC,iBAAiB,EAAC,0FAkBnC,OAdD,OAAO,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,IAAG,smBAcX,OAAhC,OAAO,UAAU,CAAC,aAAa,EAAC;IAO/C;IAEA,OAAe,mBAAmB,MAAoB,EAAU;QAC9D,OAAO,AAAC,+DAeE,OAXC,OAAO,IAAI,EAAC,mbAYd,OADC,OAAO,IAAI,EAAC,sBAEA,OADb,OAAO,WAAW,EAAC,kCAOlB,OANY,IAAI,KAAK,OAAO,WAAW,EAAE,kBAAkB,IAAG,2GAUhE,OAJE,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,AAAC,OAAY,OAAN,OAAM,UAAQ,IAAI,CAAC,KAAI,oEAQvD,OAJV,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,AAAC,qCAEsD,OAA1E,OAAO,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,AAAC,OAAuB,OAAjB,GAAG,CAAC,MAAM,IAAI,IAAG,UAAQ,IAAI,CAAC,KAAI,oCAE7E,IAAI,CAAC,KAAI;IAMpB;AACF;AAGO,MAAM;IACX,aAAa,iBAAiB,MAAiB,EAAiB;QAC9D,6EAA6E;QAC7E,MAAM,aAAa,IAAI,CAAC,cAAc,CAAC;QAEvC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAC5B,MAAM;QACR;IACF;IAEA,aAAa,iBAAiB,MAAiB,EAAiB;QAC9D,MAAM,aAAa,IAAI,CAAC,cAAc,CAAC;QAEvC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAC5B,MAAM;QACR;IACF;IAEA,aAAa,oBAAoB,MAAoB,EAAiB;QACpE,MAAM,aAAa,IAAI,CAAC,iBAAiB,CAAC;QAE1C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAC5B,MAAM;QACR;IACF;IAEA,OAAe,eAAe,MAAiB,EAAU;QACvD,MAAM,UAAU;YAAC;YAAO;YAAgB;YAAqB;SAAU;QACvE,MAAM,OAAO,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,QAAU;gBACnD,QAAQ;gBACR,QAAQ,WAAW;gBACnB,QAAQ,WAAW;gBACnB,QAAQ,OAAO,IAAI;aACpB;QAED,OAAO;YACJ,iCAA4C,OAAZ,OAAO,IAAI;YAC3C,SAA0B,OAAlB,OAAO,KAAK,EAAC,OAAoB,OAAf,OAAO,OAAO;YACxC,YAA+B,OAApB,OAAO,OAAO,CAAC,IAAI;YAC/B;YACA,QAAQ,IAAI,CAAC;eACV,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,CAAC;SAC7B,CAAC,IAAI,CAAC;IACT;IAEA,OAAe,eAAe,MAAiB,EAAU;QACvD,OAAO;YACJ,oCAAmD,OAAhB,OAAO,KAAK,EAAC,KAAe,OAAZ,OAAO,IAAI;YAC9D,SAAqB,OAAb,OAAO,KAAK;YACrB;YACA;YACC,sBAAwD,OAAnC,OAAO,UAAU,CAAC,gBAAgB;YACvD,kBAAwD,OAAvC,OAAO,UAAU,CAAC,aAAa,CAAC,MAAM;YACvD,iBAAkE,OAAlD,OAAO,UAAU,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM;YACjE,YAA6C,OAAlC,OAAO,UAAU,CAAC,QAAQ,CAAC,MAAM;YAC5C,gBAA4C,OAA7B,OAAO,UAAU,CAAC,UAAU;YAC5C;YACA;YACC,qBAAsD,OAAlC,OAAO,UAAU,CAAC,eAAe;YACrD,4BAA+D,OAApC,OAAO,UAAU,CAAC,iBAAiB;YAC9D,mBAA8D,OAA5C,OAAO,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,IAAG;SAChE,CAAC,IAAI,CAAC;IACT;IAEA,OAAe,kBAAkB,MAAoB,EAAU;QAC7D,MAAM,UAAU,OAAO,KAAK,CAAC,MAAM;QACnC,MAAM,OAAO,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA,MAC3B,QAAQ,GAAG,CAAC,CAAA,QAAS,GAAG,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC;QAG9C,OAAO;YACL,OAAO,IAAI;YACX,OAAO,WAAW;YACjB,gBAAiE,OAAlD,IAAI,KAAK,OAAO,WAAW,EAAE,kBAAkB;YAC/D;YACA,QAAQ,IAAI,CAAC;eACV;SACJ,CAAC,IAAI,CAAC;IACT;AACF;AAGO,MAAM;IACX,aAAa,aACX,MAA4C,EAC5C,MAAoB,EACuB;QAC3C,IAAI;QACJ,IAAI;QAEJ,wBAAwB;QACxB,MAAM,aAAa,cAAc,SAAS,QACxB,gBAAgB,SAAS,QAAQ;QAEnD,OAAQ;YACN,KAAK;gBACH,IAAI,eAAe,OAAO;oBACxB,OAAO,MAAM,mBAAmB,cAAc,CAAC;oBAC/C,WAAW,AAAC,OAAyC,OAAnC,AAAC,OAAqB,IAAI,EAAC,UAAoC,OAA5B,AAAC,OAAqB,KAAK,EAAC;gBACnF,OAAO,IAAI,eAAe,OAAO;oBAC/B,OAAO,MAAM,mBAAmB,cAAc,CAAC;oBAC/C,WAAW,AAAC,OAAqC,OAA/B,AAAC,OAAqB,KAAK,EAAC,KAAsC,OAAnC,AAAC,OAAqB,IAAI,EAAC,UAAoC,OAA5B,AAAC,OAAqB,KAAK,EAAC;gBAClH,OAAO;oBACL,OAAO,MAAM,mBAAmB,iBAAiB,CAAC;oBAClD,WAAW,AAAC,GAA8D,OAA5D,AAAC,OAAwB,IAAI,CAAC,OAAO,CAAC,iBAAiB,MAAK;gBAC5E;gBACA;YAEF,KAAK;gBACH,IAAI,eAAe,OAAO;oBACxB,OAAO,MAAM,qBAAqB,gBAAgB,CAAC;oBACnD,WAAW,AAAC,OAAyC,OAAnC,AAAC,OAAqB,IAAI,EAAC,UAAoC,OAA5B,AAAC,OAAqB,KAAK,EAAC;gBACnF,OAAO,IAAI,eAAe,OAAO;oBAC/B,OAAO,MAAM,qBAAqB,gBAAgB,CAAC;oBACnD,WAAW,AAAC,OAAqC,OAA/B,AAAC,OAAqB,KAAK,EAAC,KAAsC,OAAnC,AAAC,OAAqB,IAAI,EAAC,UAAoC,OAA5B,AAAC,OAAqB,KAAK,EAAC;gBAClH,OAAO;oBACL,OAAO,MAAM,qBAAqB,mBAAmB,CAAC;oBACtD,WAAW,AAAC,GAA8D,OAA5D,AAAC,OAAwB,IAAI,CAAC,OAAO,CAAC,iBAAiB,MAAK;gBAC5E;gBACA;YAEF,KAAK;gBACH,IAAI,eAAe,OAAO;oBACxB,MAAM,aAAa,oBAAoB,CAAC,iBAAiB,CAAC;oBAC1D,OAAO,IAAI,KAAK;wBAAC;qBAAW,EAAE;wBAAE,MAAM;oBAAW;oBACjD,WAAW,AAAC,OAAyC,OAAnC,AAAC,OAAqB,IAAI,EAAC,UAAoC,OAA5B,AAAC,OAAqB,KAAK,EAAC;gBACnF,OAAO,IAAI,eAAe,OAAO;oBAC/B,MAAM,aAAa,oBAAoB,CAAC,iBAAiB,CAAC;oBAC1D,OAAO,IAAI,KAAK;wBAAC;qBAAW,EAAE;wBAAE,MAAM;oBAAW;oBACjD,WAAW,AAAC,OAAqC,OAA/B,AAAC,OAAqB,KAAK,EAAC,KAAsC,OAAnC,AAAC,OAAqB,IAAI,EAAC,UAAoC,OAA5B,AAAC,OAAqB,KAAK,EAAC;gBAClH,OAAO;oBACL,MAAM,aAAa,oBAAoB,CAAC,oBAAoB,CAAC;oBAC7D,OAAO,IAAI,KAAK;wBAAC;qBAAW,EAAE;wBAAE,MAAM;oBAAW;oBACjD,WAAW,AAAC,GAA8D,OAA5D,AAAC,OAAwB,IAAI,CAAC,OAAO,CAAC,iBAAiB,MAAK;gBAC5E;gBACA;YAEF;gBACE,MAAM,IAAI,MAAM,AAAC,8BAAoC,OAAP;QAClD;QAEA,OAAO;YAAE;YAAM;QAAS;IAC1B;IAEA,OAAO,aAAa,IAAU,EAAE,QAAgB,EAAE;QAChD,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAChB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;AACF", "debugId": null}}, {"offset": {"line": 1482, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/app/%28dashboard%29/reports/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from \"@/components/ui/tabs\"\nimport { ReportTypeSelector } from \"@/components/reports/report-type-selector\"\nimport { ReportFilters } from \"@/components/reports/report-filters\"\nimport { ReportsLibrary } from \"@/components/reports/reports-library\"\nimport { SF2ReportDialog } from \"@/components/reports/sf2-report-dialog\"\nimport { SF4ReportDialog } from \"@/components/reports/sf4-report-dialog\"\nimport { CustomReportDialog } from \"@/components/reports/custom-report-dialog\"\nimport { ReportScheduler } from \"@/components/reports/report-scheduler\"\nimport { ReportArchive } from \"@/components/reports/report-archive\"\nimport { ReportWizardDialog } from \"@/components/reports/report-wizard-dialog\"\nimport { <PERSON>ulkReportGenerator } from \"@/components/reports/bulk-report-generator\"\nimport { ReportAnalytics } from \"@/components/reports/report-analytics\"\nimport { ReportType, ReportFilters as ReportFiltersType, ExportFormat } from \"@/lib/types/reports\"\nimport { mockGeneratedReports, mockReportAnalytics } from \"@/lib/data/reports-mock-data\"\nimport {\n  FileText,\n  Download,\n  Calendar,\n  Users,\n  Plus,\n  BarChart3,\n  Clock,\n  TrendingUp,\n  Settings\n} from \"lucide-react\"\nimport { toast } from \"sonner\"\n\nexport default function ReportsPage() {\n  const [selectedReportType, setSelectedReportType] = useState<ReportType>(\"SF2\")\n  const [reportFilters, setReportFilters] = useState<ReportFiltersType>({})\n  const [activeTab, setActiveTab] = useState(\"generate\")\n  const [showSF2Dialog, setShowSF2Dialog] = useState(false)\n  const [showSF4Dialog, setShowSF4Dialog] = useState(false)\n  const [showCustomDialog, setShowCustomDialog] = useState(false)\n  const [showWizardDialog, setShowWizardDialog] = useState(false)\n\n  // Mock analytics data\n  const totalReports = mockGeneratedReports.length\n  const readyReports = mockGeneratedReports.filter(r => r.status === \"READY\").length\n  const totalDownloads = mockGeneratedReports.reduce((sum, r) => sum + r.downloadCount, 0)\n  const scheduledReports = 8 // Mock data\n\n  const handleGenerateReport = () => {\n    if (selectedReportType === \"SF2\") {\n      setShowSF2Dialog(true)\n    } else if (selectedReportType === \"SF4\") {\n      setShowSF4Dialog(true)\n    } else if (selectedReportType === \"CUSTOM\") {\n      setShowCustomDialog(true)\n    } else {\n      toast.success(\"Report generation started\", {\n        description: \"Your report will be ready in a few minutes\"\n      })\n    }\n  }\n\n  const handleDownload = (reportId: string, format: ExportFormat) => {\n    toast.success(`Downloading report in ${format} format`)\n  }\n\n  const handlePreview = (reportId: string) => {\n    toast.info(\"Opening report preview\")\n  }\n\n  const handleDelete = (reportId: string) => {\n    toast.success(\"Report deleted successfully\")\n  }\n\n  const handleArchive = (reportId: string) => {\n    toast.success(\"Report archived successfully\")\n  }\n\n  const handleShare = (reportId: string) => {\n    toast.success(\"Report sharing link copied to clipboard\")\n  }\n\n  const handleRegenerate = (reportId: string) => {\n    toast.success(\"Report regeneration started\")\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Reports Dashboard</h1>\n          <p className=\"text-muted-foreground\">\n            Generate Philippine DepEd forms and custom attendance reports\n          </p>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button variant=\"outline\" size=\"sm\" onClick={() => setShowWizardDialog(true)}>\n            <Settings className=\"mr-2 h-4 w-4\" />\n            Wizard\n          </Button>\n          <Button onClick={handleGenerateReport}>\n            <Plus className=\"mr-2 h-4 w-4\" />\n            New Report\n          </Button>\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid gap-4 md:grid-cols-4\">\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Total Reports</CardTitle>\n            <FileText className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{totalReports}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              {readyReports} ready to download\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Downloads</CardTitle>\n            <Download className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{totalDownloads}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Total downloads\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">Scheduled Reports</CardTitle>\n            <Clock className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">{scheduledReports}</div>\n            <p className=\"text-xs text-muted-foreground\">\n              Auto-generated\n            </p>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n            <CardTitle className=\"text-sm font-medium\">DepEd Compliance</CardTitle>\n            <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-2xl font-bold\">100%</div>\n            <p className=\"text-xs text-muted-foreground\">\n              SF2 & SF4 ready\n            </p>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Main Content Tabs */}\n      <Tabs value={activeTab} onValueChange={setActiveTab} className=\"space-y-6\">\n        <TabsList className=\"grid w-full grid-cols-6\">\n          <TabsTrigger value=\"generate\">Generate Report</TabsTrigger>\n          <TabsTrigger value=\"library\">Reports Library</TabsTrigger>\n          <TabsTrigger value=\"bulk\">Bulk Generate</TabsTrigger>\n          <TabsTrigger value=\"scheduled\">Scheduled</TabsTrigger>\n          <TabsTrigger value=\"archive\">Archive</TabsTrigger>\n          <TabsTrigger value=\"analytics\">Analytics</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"generate\" className=\"space-y-6\">\n          <div className=\"grid gap-6 lg:grid-cols-3\">\n            <div className=\"lg:col-span-2\">\n              <ReportTypeSelector\n                selectedType={selectedReportType}\n                onTypeSelect={(type) => {\n                  setSelectedReportType(type)\n                  if (type === \"SF2\") {\n                    setShowSF2Dialog(true)\n                  } else if (type === \"SF4\") {\n                    setShowSF4Dialog(true)\n                  } else if (type === \"CUSTOM\") {\n                    setShowCustomDialog(true)\n                  }\n                }}\n              />\n            </div>\n            <div>\n              <ReportFilters\n                filters={reportFilters}\n                onFiltersChange={setReportFilters}\n                reportType={selectedReportType}\n              />\n            </div>\n          </div>\n\n          {/* Generate Button */}\n          <Card>\n            <CardContent className=\"pt-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h3 className=\"text-lg font-medium\">Ready to Generate</h3>\n                  <p className=\"text-sm text-muted-foreground\">\n                    {selectedReportType} report with {Object.keys(reportFilters).length} filters applied\n                  </p>\n                </div>\n                <Button size=\"lg\" onClick={handleGenerateReport}>\n                  <FileText className=\"mr-2 h-5 w-5\" />\n                  Generate Report\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"library\" className=\"space-y-6\">\n          <ReportsLibrary\n            reports={mockGeneratedReports}\n            onDownload={handleDownload}\n            onPreview={handlePreview}\n            onDelete={handleDelete}\n            onArchive={handleArchive}\n            onShare={handleShare}\n            onRegenerate={handleRegenerate}\n          />\n        </TabsContent>\n\n        <TabsContent value=\"bulk\" className=\"space-y-6\">\n          <BulkReportGenerator\n            onGenerate={(configs) => {\n              toast.success(`Bulk generation started for ${configs.length} reports`)\n            }}\n          />\n        </TabsContent>\n\n        <TabsContent value=\"scheduled\" className=\"space-y-6\">\n          <ReportScheduler />\n        </TabsContent>\n\n        <TabsContent value=\"archive\" className=\"space-y-6\">\n          <ReportArchive />\n        </TabsContent>\n\n        <TabsContent value=\"analytics\" className=\"space-y-6\">\n          <ReportAnalytics />\n        </TabsContent>\n      </Tabs>\n\n      {/* SF2 Report Dialog */}\n      <SF2ReportDialog\n        open={showSF2Dialog}\n        onOpenChange={setShowSF2Dialog}\n        config={{\n          id: `temp-${Date.now()}`,\n          name: `SF2 Report - ${selectedReportType}`,\n          type: selectedReportType,\n          description: \"SF2 Daily Attendance Report\",\n          dateRange: {\n            startDate: new Date().toISOString().split('T')[0],\n            endDate: new Date().toISOString().split('T')[0]\n          },\n          filters: reportFilters,\n          settings: {\n            includeSignatures: true,\n            pageOrientation: \"portrait\"\n          },\n          createdBy: \"current-user\",\n          createdAt: new Date().toISOString(),\n          lastModified: new Date().toISOString()\n        }}\n      />\n\n      {/* SF4 Report Dialog */}\n      <SF4ReportDialog\n        open={showSF4Dialog}\n        onOpenChange={setShowSF4Dialog}\n        config={{\n          id: `temp-${Date.now()}`,\n          name: `SF4 Report - ${selectedReportType}`,\n          type: selectedReportType,\n          description: \"SF4 Monthly Learner's Movement Report\",\n          dateRange: {\n            startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],\n            endDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString().split('T')[0]\n          },\n          filters: reportFilters,\n          settings: {\n            includeSignatures: true,\n            pageOrientation: \"landscape\"\n          },\n          createdBy: \"current-user\",\n          createdAt: new Date().toISOString(),\n          lastModified: new Date().toISOString()\n        }}\n      />\n\n      {/* Custom Report Dialog */}\n      <CustomReportDialog\n        open={showCustomDialog}\n        onOpenChange={setShowCustomDialog}\n      />\n\n      {/* Report Wizard Dialog */}\n      <ReportWizardDialog\n        open={showWizardDialog}\n        onOpenChange={setShowWizardDialog}\n        onComplete={(config) => {\n          toast.success(\"Report wizard completed\", {\n            description: `${config.name} is being generated`\n          })\n        }}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AA/BA;;;;;;;;;;;;;;;;;;;AAiCe,SAAS;;IACtB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,sBAAsB;IACtB,MAAM,eAAe,yIAAA,CAAA,uBAAoB,CAAC,MAAM;IAChD,MAAM,eAAe,yIAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,SAAS,MAAM;IAClF,MAAM,iBAAiB,yIAAA,CAAA,uBAAoB,CAAC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE;IACtF,MAAM,mBAAmB,EAAE,YAAY;;IAEvC,MAAM,uBAAuB;QAC3B,IAAI,uBAAuB,OAAO;YAChC,iBAAiB;QACnB,OAAO,IAAI,uBAAuB,OAAO;YACvC,iBAAiB;QACnB,OAAO,IAAI,uBAAuB,UAAU;YAC1C,oBAAoB;QACtB,OAAO;YACL,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,6BAA6B;gBACzC,aAAa;YACf;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC,UAAkB;QACxC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,yBAA+B,OAAP,QAAO;IAChD;IAEA,MAAM,gBAAgB,CAAC;QACrB,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,MAAM,eAAe,CAAC;QACpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,gBAAgB,CAAC;QACrB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,cAAc,CAAC;QACnB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS,IAAM,oBAAoB;;kDACrE,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,8HAAA,CAAA,SAAM;gCAAC,SAAS;;kDACf,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,4HAAA,CAAA,OAAI;;0CACH,6LAAC,4HAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,iNAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6LAAC,4HAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDAAsB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;;4CACV;4CAAa;;;;;;;;;;;;;;;;;;;kCAKpB,6LAAC,4HAAA,CAAA,OAAI;;0CACH,6LAAC,4HAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEtB,6LAAC,4HAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDAAsB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,6LAAC,4HAAA,CAAA,OAAI;;0CACH,6LAAC,4HAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,6LAAC,4HAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDAAsB;;;;;;kDACrC,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAMjD,6LAAC,4HAAA,CAAA,OAAI;;0CACH,6LAAC,4HAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;0CAExB,6LAAC,4HAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;kDAAqB;;;;;;kDACpC,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC,4HAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;gBAAc,WAAU;;kCAC7D,6LAAC,4HAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;0CAAO;;;;;;0CAC1B,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;0CAC/B,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;;;;;;;kCAGjC,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;;0CACtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uJAAA,CAAA,qBAAkB;4CACjB,cAAc;4CACd,cAAc,CAAC;gDACb,sBAAsB;gDACtB,IAAI,SAAS,OAAO;oDAClB,iBAAiB;gDACnB,OAAO,IAAI,SAAS,OAAO;oDACzB,iBAAiB;gDACnB,OAAO,IAAI,SAAS,UAAU;oDAC5B,oBAAoB;gDACtB;4CACF;;;;;;;;;;;kDAGJ,6LAAC;kDACC,cAAA,6LAAC,8IAAA,CAAA,gBAAa;4CACZ,SAAS;4CACT,iBAAiB;4CACjB,YAAY;;;;;;;;;;;;;;;;;0CAMlB,6LAAC,4HAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,4HAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsB;;;;;;kEACpC,6LAAC;wDAAE,WAAU;;4DACV;4DAAmB;4DAAc,OAAO,IAAI,CAAC,eAAe,MAAM;4DAAC;;;;;;;;;;;;;0DAGxE,6LAAC,8HAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAS;;kEACzB,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/C,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,6LAAC,+IAAA,CAAA,iBAAc;4BACb,SAAS,yIAAA,CAAA,uBAAoB;4BAC7B,YAAY;4BACZ,WAAW;4BACX,UAAU;4BACV,WAAW;4BACX,SAAS;4BACT,cAAc;;;;;;;;;;;kCAIlB,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;wBAAO,WAAU;kCAClC,cAAA,6LAAC,wJAAA,CAAA,sBAAmB;4BAClB,YAAY,CAAC;gCACX,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,AAAC,+BAA6C,OAAf,QAAQ,MAAM,EAAC;4BAC9D;;;;;;;;;;;kCAIJ,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;kCACvC,cAAA,6LAAC,gJAAA,CAAA,kBAAe;;;;;;;;;;kCAGlB,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;kCACrC,cAAA,6LAAC,8IAAA,CAAA,gBAAa;;;;;;;;;;kCAGhB,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;kCACvC,cAAA,6LAAC,gJAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;0BAKpB,6LAAC,oJAAA,CAAA,kBAAe;gBACd,MAAM;gBACN,cAAc;gBACd,QAAQ;oBACN,IAAI,AAAC,QAAkB,OAAX,KAAK,GAAG;oBACpB,MAAM,AAAC,gBAAkC,OAAnB;oBACtB,MAAM;oBACN,aAAa;oBACb,WAAW;wBACT,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;wBACjD,SAAS,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACjD;oBACA,SAAS;oBACT,UAAU;wBACR,mBAAmB;wBACnB,iBAAiB;oBACnB;oBACA,WAAW;oBACX,WAAW,IAAI,OAAO,WAAW;oBACjC,cAAc,IAAI,OAAO,WAAW;gBACtC;;;;;;0BAIF,6LAAC,oJAAA,CAAA,kBAAe;gBACd,MAAM;gBACN,cAAc;gBACd,QAAQ;oBACN,IAAI,AAAC,QAAkB,OAAX,KAAK,GAAG;oBACpB,MAAM,AAAC,gBAAkC,OAAnB;oBACtB,MAAM;oBACN,aAAa;oBACb,WAAW;wBACT,WAAW,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,QAAQ,IAAI,GAAG,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;wBACnG,SAAS,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,QAAQ,KAAK,GAAG,GAAG,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBACvG;oBACA,SAAS;oBACT,UAAU;wBACR,mBAAmB;wBACnB,iBAAiB;oBACnB;oBACA,WAAW;oBACX,WAAW,IAAI,OAAO,WAAW;oBACjC,cAAc,IAAI,OAAO,WAAW;gBACtC;;;;;;0BAIF,6LAAC,uJAAA,CAAA,qBAAkB;gBACjB,MAAM;gBACN,cAAc;;;;;;0BAIhB,6LAAC,uJAAA,CAAA,qBAAkB;gBACjB,MAAM;gBACN,cAAc;gBACd,YAAY,CAAC;oBACX,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,2BAA2B;wBACvC,aAAa,AAAC,GAAc,OAAZ,OAAO,IAAI,EAAC;oBAC9B;gBACF;;;;;;;;;;;;AAIR;GA5RwB;KAAA", "debugId": null}}]}