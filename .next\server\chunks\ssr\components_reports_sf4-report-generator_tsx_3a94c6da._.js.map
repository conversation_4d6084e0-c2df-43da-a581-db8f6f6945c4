{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/qrsams/components/reports/sf4-report-generator.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { But<PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Checkbox } from \"@/components/ui/checkbox\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from \"@/components/ui/table\"\nimport { SF4Report, ReportConfig, PrincipalReview } from \"@/lib/types/reports\"\nimport { schoolInfo } from \"@/lib/data/reports-mock-data\"\nimport { generateSF4Report } from \"@/lib/utils/report-utils\"\nimport { mockStudents, mockAttendanceRecords } from \"@/lib/data/mock-data\"\nimport { \n  TrendingUp, \n  Calendar, \n  Users, \n  School, \n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Download,\n  Eye,\n  Printer,\n  UserPlus,\n  UserMinus,\n  UserX,\n  BarChart3\n} from \"lucide-react\"\nimport { format } from \"date-fns\"\n\ninterface SF4ReportGeneratorProps {\n  config: ReportConfig\n  onGenerate: (report: SF4Report) => void\n  onPreview: (report: SF4Report) => void\n  className?: string\n}\n\nexport function SF4ReportGenerator({ \n  config, \n  onGenerate, \n  onPreview, \n  className \n}: SF4ReportGeneratorProps) {\n  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth())\n  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())\n  const [principalReview, setPrincipalReview] = useState<Partial<PrincipalReview>>({\n    reviewedBy: schoolInfo.principalName,\n    approved: false,\n    remarks: \"\"\n  })\n  const [reportSettings, setReportSettings] = useState({\n    includeSignatures: true,\n    includeStatistics: true,\n    includeChronicAbsentees: true,\n    includePerfectAttendance: true,\n    showMovementDetails: true\n  })\n  const [isGenerating, setIsGenerating] = useState(false)\n  const [generatedReport, setGeneratedReport] = useState<SF4Report | null>(null)\n\n  const months = [\n    \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\n    \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\n  ]\n\n  const currentYear = new Date().getFullYear()\n  const years = Array.from({ length: 5 }, (_, i) => currentYear - 2 + i)\n\n  // Mock enrollment movement data\n  const mockMovementData = {\n    newAdmissions: [\n      { studentId: \"STU101\", name: \"Maria Santos\", dateOfAction: \"2025-01-15\", reason: \"New enrollment\" },\n      { studentId: \"STU102\", name: \"Juan Dela Cruz\", dateOfAction: \"2025-01-20\", reason: \"Transfer from private school\" }\n    ],\n    transfersOut: [\n      { studentId: \"STU050\", name: \"Ana Reyes\", dateOfAction: \"2025-01-10\", reason: \"Family relocation\", destination: \"Manila Science High School\" }\n    ],\n    dropouts: [\n      { studentId: \"STU075\", name: \"Jose Rodriguez\", dateOfAction: \"2025-01-25\", reason: \"Financial difficulties\" }\n    ]\n  }\n\n  const handleGenerateReport = async () => {\n    setIsGenerating(true)\n    \n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 2000))\n      \n      // Update config with selected month/year\n      const updatedConfig = {\n        ...config,\n        dateRange: {\n          startDate: `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-01`,\n          endDate: `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-${new Date(selectedYear, selectedMonth + 1, 0).getDate()}`\n        }\n      }\n      \n      // Generate the SF4 report\n      const report = generateSF4Report(\n        updatedConfig,\n        mockStudents,\n        mockAttendanceRecords as any[]\n      )\n      \n      // Add principal review\n      const enhancedReport: SF4Report = {\n        ...report,\n        month: months[selectedMonth],\n        year: selectedYear.toString(),\n        principalReview: {\n          ...principalReview,\n          reviewDate: new Date().toISOString().split('T')[0]\n        } as PrincipalReview\n      }\n      \n      setGeneratedReport(enhancedReport)\n      onGenerate(enhancedReport)\n    } catch (error) {\n      console.error(\"Error generating SF4 report:\", error)\n    } finally {\n      setIsGenerating(false)\n    }\n  }\n\n  const handlePreviewReport = () => {\n    if (generatedReport) {\n      onPreview(generatedReport)\n    }\n  }\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* SF4 Report Header */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <TrendingUp className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div>\n                <CardTitle>SF4 Monthly Learner's Movement</CardTitle>\n                <CardDescription>\n                  Official DepEd School Form 4 - Monthly Report on Learner's Movement\n                </CardDescription>\n              </div>\n            </div>\n            <Badge variant=\"secondary\" className=\"bg-green-100 text-green-800\">\n              DepEd Official\n            </Badge>\n          </div>\n        </CardHeader>\n      </Card>\n\n      {/* School Information */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <School className=\"h-5 w-5\" />\n            School Information\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <Label className=\"text-sm font-medium\">School Name</Label>\n              <p className=\"text-sm text-muted-foreground\">{schoolInfo.schoolName}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium\">School ID</Label>\n              <p className=\"text-sm text-muted-foreground\">{schoolInfo.schoolId}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium\">Division</Label>\n              <p className=\"text-sm text-muted-foreground\">{schoolInfo.division}</p>\n            </div>\n            <div>\n              <Label className=\"text-sm font-medium\">Region</Label>\n              <p className=\"text-sm text-muted-foreground\">{schoolInfo.region}</p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Report Period */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Calendar className=\"h-5 w-5\" />\n            Report Period\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"month-select\">Month</Label>\n              <Select \n                value={selectedMonth.toString()} \n                onValueChange={(value) => setSelectedMonth(parseInt(value))}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select month\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {months.map((month, index) => (\n                    <SelectItem key={index} value={index.toString()}>\n                      {month}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"year-select\">Year</Label>\n              <Select \n                value={selectedYear.toString()} \n                onValueChange={(value) => setSelectedYear(parseInt(value))}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select year\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {years.map((year) => (\n                    <SelectItem key={year} value={year.toString()}>\n                      {year}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n\n          <div className=\"p-4 bg-blue-50 rounded-lg\">\n            <div className=\"flex items-center gap-2 text-blue-800\">\n              <Calendar className=\"h-4 w-4\" />\n              <span className=\"font-medium\">\n                Report Period: {months[selectedMonth]} {selectedYear}\n              </span>\n            </div>\n            <p className=\"text-sm text-blue-600 mt-1\">\n              This report will cover all learner movements for the selected month\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Learner Movement Preview */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Users className=\"h-5 w-5\" />\n            Learner Movement Summary\n          </CardTitle>\n          <CardDescription>\n            Preview of enrollment changes for {months[selectedMonth]} {selectedYear}\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          {/* Movement Statistics */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            <div className=\"text-center p-4 border rounded-lg\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <UserPlus className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"text-2xl font-bold text-green-600\">{mockMovementData.newAdmissions.length}</div>\n              <div className=\"text-sm text-muted-foreground\">New Admissions</div>\n            </div>\n            <div className=\"text-center p-4 border rounded-lg\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <UserMinus className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"text-2xl font-bold text-blue-600\">{mockMovementData.transfersOut.length}</div>\n              <div className=\"text-sm text-muted-foreground\">Transfers Out</div>\n            </div>\n            <div className=\"text-center p-4 border rounded-lg\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <UserX className=\"h-6 w-6 text-red-600\" />\n              </div>\n              <div className=\"text-2xl font-bold text-red-600\">{mockMovementData.dropouts.length}</div>\n              <div className=\"text-sm text-muted-foreground\">Dropouts</div>\n            </div>\n            <div className=\"text-center p-4 border rounded-lg\">\n              <div className=\"flex items-center justify-center mb-2\">\n                <BarChart3 className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <div className=\"text-2xl font-bold text-purple-600\">\n                {mockStudents.filter(s => s.status === 'Active').length}\n              </div>\n              <div className=\"text-sm text-muted-foreground\">Current Enrollment</div>\n            </div>\n          </div>\n\n          {/* Movement Details */}\n          {reportSettings.showMovementDetails && (\n            <div className=\"space-y-4\">\n              <Separator />\n              <h4 className=\"font-medium\">Movement Details</h4>\n              \n              {/* New Admissions */}\n              {mockMovementData.newAdmissions.length > 0 && (\n                <div>\n                  <h5 className=\"text-sm font-medium text-green-600 mb-2\">New Admissions</h5>\n                  <div className=\"border rounded-lg overflow-hidden\">\n                    <Table>\n                      <TableHeader>\n                        <TableRow className=\"bg-green-50\">\n                          <TableHead>Student Name</TableHead>\n                          <TableHead>Date</TableHead>\n                          <TableHead>Reason</TableHead>\n                        </TableRow>\n                      </TableHeader>\n                      <TableBody>\n                        {mockMovementData.newAdmissions.map((student) => (\n                          <TableRow key={student.studentId}>\n                            <TableCell>{student.name}</TableCell>\n                            <TableCell>{format(new Date(student.dateOfAction), \"MMM dd, yyyy\")}</TableCell>\n                            <TableCell>{student.reason}</TableCell>\n                          </TableRow>\n                        ))}\n                      </TableBody>\n                    </Table>\n                  </div>\n                </div>\n              )}\n\n              {/* Transfers Out */}\n              {mockMovementData.transfersOut.length > 0 && (\n                <div>\n                  <h5 className=\"text-sm font-medium text-blue-600 mb-2\">Transfers Out</h5>\n                  <div className=\"border rounded-lg overflow-hidden\">\n                    <Table>\n                      <TableHeader>\n                        <TableRow className=\"bg-blue-50\">\n                          <TableHead>Student Name</TableHead>\n                          <TableHead>Date</TableHead>\n                          <TableHead>Destination</TableHead>\n                          <TableHead>Reason</TableHead>\n                        </TableRow>\n                      </TableHeader>\n                      <TableBody>\n                        {mockMovementData.transfersOut.map((student) => (\n                          <TableRow key={student.studentId}>\n                            <TableCell>{student.name}</TableCell>\n                            <TableCell>{format(new Date(student.dateOfAction), \"MMM dd, yyyy\")}</TableCell>\n                            <TableCell>{student.destination}</TableCell>\n                            <TableCell>{student.reason}</TableCell>\n                          </TableRow>\n                        ))}\n                      </TableBody>\n                    </Table>\n                  </div>\n                </div>\n              )}\n\n              {/* Dropouts */}\n              {mockMovementData.dropouts.length > 0 && (\n                <div>\n                  <h5 className=\"text-sm font-medium text-red-600 mb-2\">Dropouts</h5>\n                  <div className=\"border rounded-lg overflow-hidden\">\n                    <Table>\n                      <TableHeader>\n                        <TableRow className=\"bg-red-50\">\n                          <TableHead>Student Name</TableHead>\n                          <TableHead>Date</TableHead>\n                          <TableHead>Reason</TableHead>\n                        </TableRow>\n                      </TableHeader>\n                      <TableBody>\n                        {mockMovementData.dropouts.map((student) => (\n                          <TableRow key={student.studentId}>\n                            <TableCell>{student.name}</TableCell>\n                            <TableCell>{format(new Date(student.dateOfAction), \"MMM dd, yyyy\")}</TableCell>\n                            <TableCell>{student.reason}</TableCell>\n                          </TableRow>\n                        ))}\n                      </TableBody>\n                    </Table>\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Principal Review */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <CheckCircle className=\"h-5 w-5\" />\n            Principal Review\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"principal-name\">Principal Name</Label>\n              <Input\n                id=\"principal-name\"\n                value={principalReview.reviewedBy || \"\"}\n                onChange={(e) => setPrincipalReview({...principalReview, reviewedBy: e.target.value})}\n                placeholder=\"Principal's full name\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label>Review Status</Label>\n              <div className=\"flex items-center space-x-2\">\n                <Checkbox\n                  id=\"approved\"\n                  checked={principalReview.approved || false}\n                  onCheckedChange={(checked) =>\n                    setPrincipalReview({...principalReview, approved: checked as boolean})\n                  }\n                />\n                <Label htmlFor=\"approved\" className=\"text-sm\">\n                  Report approved by principal\n                </Label>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"principal-remarks\">Principal's Remarks</Label>\n            <Textarea\n              id=\"principal-remarks\"\n              value={principalReview.remarks || \"\"}\n              onChange={(e) => setPrincipalReview({...principalReview, remarks: e.target.value})}\n              placeholder=\"Optional remarks or comments from the principal\"\n              rows={3}\n            />\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Report Settings */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <CheckCircle className=\"h-5 w-5\" />\n            Report Settings\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-signatures\"\n                checked={reportSettings.includeSignatures}\n                onCheckedChange={(checked) =>\n                  setReportSettings({...reportSettings, includeSignatures: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"include-signatures\" className=\"text-sm\">\n                Include signature fields\n              </Label>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-statistics\"\n                checked={reportSettings.includeStatistics}\n                onCheckedChange={(checked) =>\n                  setReportSettings({...reportSettings, includeStatistics: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"include-statistics\" className=\"text-sm\">\n                Include attendance statistics\n              </Label>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-chronic\"\n                checked={reportSettings.includeChronicAbsentees}\n                onCheckedChange={(checked) =>\n                  setReportSettings({...reportSettings, includeChronicAbsentees: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"include-chronic\" className=\"text-sm\">\n                Include chronic absentees list\n              </Label>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"include-perfect\"\n                checked={reportSettings.includePerfectAttendance}\n                onCheckedChange={(checked) =>\n                  setReportSettings({...reportSettings, includePerfectAttendance: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"include-perfect\" className=\"text-sm\">\n                Include perfect attendance list\n              </Label>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"show-movement\"\n                checked={reportSettings.showMovementDetails}\n                onCheckedChange={(checked) =>\n                  setReportSettings({...reportSettings, showMovementDetails: checked as boolean})\n                }\n              />\n              <Label htmlFor=\"show-movement\" className=\"text-sm\">\n                Show detailed movement records\n              </Label>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Generation Actions */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-1\">\n              <h3 className=\"text-lg font-medium\">Generate SF4 Report</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                Create official DepEd SF4 Monthly Learner's Movement Report for {months[selectedMonth]} {selectedYear}\n              </p>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              {generatedReport && (\n                <>\n                  <Button variant=\"outline\" onClick={handlePreviewReport}>\n                    <Eye className=\"mr-2 h-4 w-4\" />\n                    Preview\n                  </Button>\n                  <Button variant=\"outline\">\n                    <Download className=\"mr-2 h-4 w-4\" />\n                    Download\n                  </Button>\n                  <Button variant=\"outline\">\n                    <Printer className=\"mr-2 h-4 w-4\" />\n                    Print\n                  </Button>\n                </>\n              )}\n              <Button\n                onClick={handleGenerateReport}\n                disabled={isGenerating}\n                size=\"lg\"\n              >\n                {isGenerating ? (\n                  <>\n                    <Clock className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Generating...\n                  </>\n                ) : (\n                  <>\n                    <TrendingUp className=\"mr-2 h-4 w-4\" />\n                    Generate SF4\n                  </>\n                )}\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAjCA;;;;;;;;;;;;;;;;;;AA0CO,SAAS,mBAAmB,EACjC,MAAM,EACN,UAAU,EACV,SAAS,EACT,SAAS,EACe;IACxB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO,QAAQ;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO,WAAW;IACvE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;QAC/E,YAAY,sIAAA,CAAA,aAAU,CAAC,aAAa;QACpC,UAAU;QACV,SAAS;IACX;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,mBAAmB;QACnB,mBAAmB;QACnB,yBAAyB;QACzB,0BAA0B;QAC1B,qBAAqB;IACvB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEzE,MAAM,SAAS;QACb;QAAW;QAAY;QAAS;QAAS;QAAO;QAChD;QAAQ;QAAU;QAAa;QAAW;QAAY;KACvD;IAED,MAAM,cAAc,IAAI,OAAO,WAAW;IAC1C,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAE,GAAG,CAAC,GAAG,IAAM,cAAc,IAAI;IAEpE,gCAAgC;IAChC,MAAM,mBAAmB;QACvB,eAAe;YACb;gBAAE,WAAW;gBAAU,MAAM;gBAAgB,cAAc;gBAAc,QAAQ;YAAiB;YAClG;gBAAE,WAAW;gBAAU,MAAM;gBAAkB,cAAc;gBAAc,QAAQ;YAA+B;SACnH;QACD,cAAc;YACZ;gBAAE,WAAW;gBAAU,MAAM;gBAAa,cAAc;gBAAc,QAAQ;gBAAqB,aAAa;YAA6B;SAC9I;QACD,UAAU;YACR;gBAAE,WAAW;gBAAU,MAAM;gBAAkB,cAAc;gBAAc,QAAQ;YAAyB;SAC7G;IACH;IAEA,MAAM,uBAAuB;QAC3B,gBAAgB;QAEhB,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,yCAAyC;YACzC,MAAM,gBAAgB;gBACpB,GAAG,MAAM;gBACT,WAAW;oBACT,WAAW,GAAG,aAAa,CAAC,EAAE,OAAO,gBAAgB,GAAG,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;oBAC7E,SAAS,GAAG,aAAa,CAAC,EAAE,OAAO,gBAAgB,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,KAAK,cAAc,gBAAgB,GAAG,GAAG,OAAO,IAAI;gBACpI;YACF;YAEA,0BAA0B;YAC1B,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,oBAAiB,AAAD,EAC7B,eACA,2HAAA,CAAA,eAAY,EACZ,2HAAA,CAAA,wBAAqB;YAGvB,uBAAuB;YACvB,MAAM,iBAA4B;gBAChC,GAAG,MAAM;gBACT,OAAO,MAAM,CAAC,cAAc;gBAC5B,MAAM,aAAa,QAAQ;gBAC3B,iBAAiB;oBACf,GAAG,eAAe;oBAClB,YAAY,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACpD;YACF;YAEA,mBAAmB;YACnB,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,iBAAiB;YACnB,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,yHAAA,CAAA,aAAU;8BACT,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;;0DACC,8OAAC,yHAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,yHAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;;0CAKrB,8OAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAA8B;;;;;;;;;;;;;;;;;;;;;;0BAQzE,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIlC,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,0HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAiC,sIAAA,CAAA,aAAU,CAAC,UAAU;;;;;;;;;;;;8CAErE,8OAAC;;sDACC,8OAAC,0HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAiC,sIAAA,CAAA,aAAU,CAAC,QAAQ;;;;;;;;;;;;8CAEnE,8OAAC;;sDACC,8OAAC,0HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAiC,sIAAA,CAAA,aAAU,CAAC,QAAQ;;;;;;;;;;;;8CAEnE,8OAAC;;sDACC,8OAAC,0HAAA,CAAA,QAAK;4CAAC,WAAU;sDAAsB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAiC,sIAAA,CAAA,aAAU,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvE,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIpC,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAe;;;;;;0DAC9B,8OAAC,2HAAA,CAAA,SAAM;gDACL,OAAO,cAAc,QAAQ;gDAC7B,eAAe,CAAC,QAAU,iBAAiB,SAAS;;kEAEpD,8OAAC,2HAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,2HAAA,CAAA,gBAAa;kEACX,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,2HAAA,CAAA,aAAU;gEAAa,OAAO,MAAM,QAAQ;0EAC1C;+DADc;;;;;;;;;;;;;;;;;;;;;;kDAQzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,8OAAC,2HAAA,CAAA,SAAM;gDACL,OAAO,aAAa,QAAQ;gDAC5B,eAAe,CAAC,QAAU,gBAAgB,SAAS;;kEAEnD,8OAAC,2HAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,2HAAA,CAAA,gBAAa;kEACX,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,2HAAA,CAAA,aAAU;gEAAY,OAAO,KAAK,QAAQ;0EACxC;+DADc;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;;oDAAc;oDACZ,MAAM,CAAC,cAAc;oDAAC;oDAAE;;;;;;;;;;;;;kDAG5C,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;0BAQhD,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;;0CACT,8OAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,8OAAC,yHAAA,CAAA,kBAAe;;oCAAC;oCACoB,MAAM,CAAC,cAAc;oCAAC;oCAAE;;;;;;;;;;;;;kCAG/D,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;0DAAqC,iBAAiB,aAAa,CAAC,MAAM;;;;;;0DACzF,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAEjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAI,WAAU;0DAAoC,iBAAiB,YAAY,CAAC,MAAM;;;;;;0DACvF,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAEjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;0DAAmC,iBAAiB,QAAQ,CAAC,MAAM;;;;;;0DAClF,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAEjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;gDAAI,WAAU;0DACZ,2HAAA,CAAA,eAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;;;;;;0DAEzD,8OAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;4BAKlD,eAAe,mBAAmB,kBACjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8HAAA,CAAA,YAAS;;;;;kDACV,8OAAC;wCAAG,WAAU;kDAAc;;;;;;oCAG3B,iBAAiB,aAAa,CAAC,MAAM,GAAG,mBACvC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA0C;;;;;;0DACxD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;sEACJ,8OAAC,0HAAA,CAAA,cAAW;sEACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;gEAAC,WAAU;;kFAClB,8OAAC,0HAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,0HAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,0HAAA,CAAA,YAAS;kFAAC;;;;;;;;;;;;;;;;;sEAGf,8OAAC,0HAAA,CAAA,YAAS;sEACP,iBAAiB,aAAa,CAAC,GAAG,CAAC,CAAC,wBACnC,8OAAC,0HAAA,CAAA,WAAQ;;sFACP,8OAAC,0HAAA,CAAA,YAAS;sFAAE,QAAQ,IAAI;;;;;;sFACxB,8OAAC,0HAAA,CAAA,YAAS;sFAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,YAAY,GAAG;;;;;;sFACnD,8OAAC,0HAAA,CAAA,YAAS;sFAAE,QAAQ,MAAM;;;;;;;mEAHb,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAa3C,iBAAiB,YAAY,CAAC,MAAM,GAAG,mBACtC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;sEACJ,8OAAC,0HAAA,CAAA,cAAW;sEACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;gEAAC,WAAU;;kFAClB,8OAAC,0HAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,0HAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,0HAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,0HAAA,CAAA,YAAS;kFAAC;;;;;;;;;;;;;;;;;sEAGf,8OAAC,0HAAA,CAAA,YAAS;sEACP,iBAAiB,YAAY,CAAC,GAAG,CAAC,CAAC,wBAClC,8OAAC,0HAAA,CAAA,WAAQ;;sFACP,8OAAC,0HAAA,CAAA,YAAS;sFAAE,QAAQ,IAAI;;;;;;sFACxB,8OAAC,0HAAA,CAAA,YAAS;sFAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,YAAY,GAAG;;;;;;sFACnD,8OAAC,0HAAA,CAAA,YAAS;sFAAE,QAAQ,WAAW;;;;;;sFAC/B,8OAAC,0HAAA,CAAA,YAAS;sFAAE,QAAQ,MAAM;;;;;;;mEAJb,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAc3C,iBAAiB,QAAQ,CAAC,MAAM,GAAG,mBAClC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;;sEACJ,8OAAC,0HAAA,CAAA,cAAW;sEACV,cAAA,8OAAC,0HAAA,CAAA,WAAQ;gEAAC,WAAU;;kFAClB,8OAAC,0HAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,0HAAA,CAAA,YAAS;kFAAC;;;;;;kFACX,8OAAC,0HAAA,CAAA,YAAS;kFAAC;;;;;;;;;;;;;;;;;sEAGf,8OAAC,0HAAA,CAAA,YAAS;sEACP,iBAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAC9B,8OAAC,0HAAA,CAAA,WAAQ;;sFACP,8OAAC,0HAAA,CAAA,YAAS;sFAAE,QAAQ,IAAI;;;;;;sFACxB,8OAAC,0HAAA,CAAA,YAAS;sFAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,YAAY,GAAG;;;;;;sFACnD,8OAAC,0HAAA,CAAA,YAAS;sFAAE,QAAQ,MAAM;;;;;;;mEAHb,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiBpD,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIvC,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAiB;;;;;;0DAChC,8OAAC,0HAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,gBAAgB,UAAU,IAAI;gDACrC,UAAU,CAAC,IAAM,mBAAmB;wDAAC,GAAG,eAAe;wDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oDAAA;gDACnF,aAAY;;;;;;;;;;;;kDAGhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,6HAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,SAAS,gBAAgB,QAAQ,IAAI;wDACrC,iBAAiB,CAAC,UAChB,mBAAmB;gEAAC,GAAG,eAAe;gEAAE,UAAU;4DAAkB;;;;;;kEAGxE,8OAAC,0HAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAOpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAoB;;;;;;kDACnC,8OAAC,6HAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO,gBAAgB,OAAO,IAAI;wCAClC,UAAU,CAAC,IAAM,mBAAmB;gDAAC,GAAG,eAAe;gDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4CAAA;wCAChF,aAAY;wCACZ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,8OAAC,yHAAA,CAAA,OAAI;;kCACH,8OAAC,yHAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,yHAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,2NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIvC,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,iBAAiB;4CACzC,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,mBAAmB;gDAAkB;;;;;;sDAG/E,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAqB,WAAU;sDAAU;;;;;;;;;;;;8CAK1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,iBAAiB;4CACzC,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,mBAAmB;gDAAkB;;;;;;sDAG/E,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAqB,WAAU;sDAAU;;;;;;;;;;;;8CAK1D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,uBAAuB;4CAC/C,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,yBAAyB;gDAAkB;;;;;;sDAGrF,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAkB,WAAU;sDAAU;;;;;;;;;;;;8CAKvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,wBAAwB;4CAChD,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,0BAA0B;gDAAkB;;;;;;sDAGtF,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAkB,WAAU;sDAAU;;;;;;;;;;;;8CAKvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,SAAS,eAAe,mBAAmB;4CAC3C,iBAAiB,CAAC,UAChB,kBAAkB;oDAAC,GAAG,cAAc;oDAAE,qBAAqB;gDAAkB;;;;;;sDAGjF,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAgB,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,8OAAC,yHAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,yHAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,8OAAC;wCAAE,WAAU;;4CAAgC;4CACsB,MAAM,CAAC,cAAc;4CAAC;4CAAE;;;;;;;;;;;;;0CAG7F,8OAAC;gCAAI,WAAU;;oCACZ,iCACC;;0DACE,8OAAC,2HAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS;;kEACjC,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,8OAAC,2HAAA,CAAA,SAAM;gDAAC,SAAQ;;kEACd,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGvC,8OAAC,2HAAA,CAAA,SAAM;gDAAC,SAAQ;;kEACd,8OAAC,wMAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;kDAK1C,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU;wCACV,MAAK;kDAEJ,6BACC;;8DACE,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAA8B;;yEAIjD;;8DACE,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3D", "debugId": null}}]}